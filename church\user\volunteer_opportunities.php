<?php
session_start();
require_once '../config.php';
require_once '../admin/includes/admin_notification_functions.php';

// Check if user is authenticated
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

$userId = $_SESSION['user_id'];
$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['apply_for_opportunity'])) {
            // Check if user already applied
            $stmt = $pdo->prepare("
                SELECT id FROM volunteer_applications 
                WHERE opportunity_id = ? AND member_id = ?
            ");
            $stmt->execute([$_POST['opportunity_id'], $userId]);
            
            if ($stmt->fetch()) {
                $error = "You have already applied for this opportunity.";
            } else {
                // Submit application
                $stmt = $pdo->prepare("
                    INSERT INTO volunteer_applications 
                    (opportunity_id, member_id, application_message, availability, relevant_experience)
                    VALUES (?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $_POST['opportunity_id'],
                    $userId,
                    $_POST['application_message'],
                    $_POST['availability'],
                    $_POST['relevant_experience']
                ]);

                $message = "Your application has been submitted successfully!";

                // Notify all admins about new volunteer application
                try {
                    // Get member name and opportunity title
                    $stmt = $pdo->prepare("SELECT full_name FROM members WHERE id = ?");
                    $stmt->execute([$userId]);
                    $memberName = $stmt->fetchColumn() ?: 'A member';

                    $stmt = $pdo->prepare("SELECT title FROM volunteer_opportunities WHERE id = ?");
                    $stmt->execute([$_POST['opportunity_id']]);
                    $opportunityTitle = $stmt->fetchColumn() ?: 'a volunteer opportunity';

                    // Get all admin IDs
                    $stmt = $pdo->prepare("SELECT id FROM admins");
                    $stmt->execute();
                    $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

                    $notificationTitle = "New Volunteer Application";
                    $notificationMessage = "$memberName has applied for volunteer opportunity: \"$opportunityTitle\"";
                    $actionUrl = "../admin/volunteer_opportunities.php?id=" . $_POST['opportunity_id'];

                    foreach ($adminIds as $adminId) {
                        createAdminNotification(
                            $pdo,
                            $adminId,
                            $notificationTitle,
                            $notificationMessage,
                            'member_activity',
                            $userId,
                            'member',
                            $actionUrl,
                            'normal'
                        );
                    }
                } catch (Exception $e) {
                    error_log("Error creating admin notification for volunteer application: " . $e->getMessage());
                }
            }
        }
        
        if (isset($_POST['withdraw_application'])) {
            // Withdraw application
            $stmt = $pdo->prepare("
                UPDATE volunteer_applications
                SET status = 'withdrawn'
                WHERE id = ? AND member_id = ?
            ");

            $stmt->execute([$_POST['application_id'], $userId]);
            $message = "Your application has been withdrawn.";

            // Notify all admins about withdrawn application
            try {
                // Get member name and opportunity title
                $stmt = $pdo->prepare("SELECT full_name FROM members WHERE id = ?");
                $stmt->execute([$userId]);
                $memberName = $stmt->fetchColumn() ?: 'A member';

                $stmt = $pdo->prepare("
                    SELECT vo.title
                    FROM volunteer_opportunities vo
                    JOIN volunteer_applications va ON vo.id = va.opportunity_id
                    WHERE va.id = ?
                ");
                $stmt->execute([$_POST['application_id']]);
                $opportunityTitle = $stmt->fetchColumn() ?: 'a volunteer opportunity';

                // Get all admin IDs
                $stmt = $pdo->prepare("SELECT id FROM admins");
                $stmt->execute();
                $adminIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

                $notificationTitle = "Volunteer Application Withdrawn";
                $notificationMessage = "$memberName has withdrawn their application for volunteer opportunity: \"$opportunityTitle\"";
                $actionUrl = "../admin/volunteer_opportunities.php";

                foreach ($adminIds as $adminId) {
                    createAdminNotification(
                        $pdo,
                        $adminId,
                        $notificationTitle,
                        $notificationMessage,
                        'member_activity',
                        $userId,
                        'member',
                        $actionUrl,
                        'normal'
                    );
                }
            } catch (Exception $e) {
                error_log("Error creating admin notification for withdrawn application: " . $e->getMessage());
            }
        }
        
    } catch (PDOException $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Pagination parameters for opportunities
$page = max(1, intval($_GET['page'] ?? 1));
$opportunitiesPerPage = 6; // 2x3 grid
$offset = ($page - 1) * $opportunitiesPerPage;

// Get total count for pagination
try {
    $countStmt = $pdo->prepare("
        SELECT COUNT(DISTINCT vo.id)
        FROM volunteer_opportunities vo
        WHERE vo.status = 'active'
    ");
    $countStmt->execute();
    $totalOpportunities = $countStmt->fetchColumn();
    $totalPages = ceil($totalOpportunities / $opportunitiesPerPage);
} catch (PDOException $e) {
    $totalOpportunities = 0;
    $totalPages = 0;
}

// Get available volunteer opportunities with pagination
try {
    $stmt = $pdo->prepare("
        SELECT vo.*, m.full_name as contact_name, m.email as contact_email,
               COUNT(DISTINCT va.id) as application_count,
               COUNT(DISTINCT CASE WHEN va.status = 'approved' THEN va.id END) as filled_positions,
               MAX(CASE WHEN va.member_id = ? THEN va.status END) as my_application_status,
               MAX(CASE WHEN va.member_id = ? THEN va.id END) as my_application_id
        FROM volunteer_opportunities vo
        LEFT JOIN members m ON vo.contact_person_id = m.id
        LEFT JOIN volunteer_applications va ON vo.id = va.opportunity_id
        WHERE vo.status = 'active'
        GROUP BY vo.id
        ORDER BY vo.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$userId, $userId, $opportunitiesPerPage, $offset]);
    $opportunities = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $opportunities = [];
    $error = "Error loading opportunities: " . $e->getMessage();
}

// Get user's applications
try {
    $stmt = $pdo->prepare("
        SELECT va.*, vo.title as opportunity_title, vo.description as opportunity_description,
               m.full_name as contact_name, m.email as contact_email
        FROM volunteer_applications va
        JOIN volunteer_opportunities vo ON va.opportunity_id = vo.id
        LEFT JOIN members m ON vo.contact_person_id = m.id
        WHERE va.member_id = ? AND va.status != 'withdrawn'
        ORDER BY va.created_at DESC
    ");
    $stmt->execute([$userId]);
    $my_applications = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $my_applications = [];
}

// Get user data
try {
    $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
    $stmt->execute([$userId]);
    $userData = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$userData) {
        header('Location: login.php');
        exit;
    }
} catch (PDOException $e) {
    $error = "Error loading user data: " . $e->getMessage();
    $userData = ['first_name' => 'User', 'full_name' => 'User'];
}

// Get site settings for branding
$sitename = get_organization_name() . ' - Volunteer Opportunities';
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'site_name'");
$stmt->execute();
$siteNameResult = $stmt->fetch();
if ($siteNameResult) {
    $sitename = $siteNameResult['setting_value'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Volunteer Opportunities - <?php echo htmlspecialchars($sitename); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom Theme CSS -->
    <?php include 'includes/theme_css.php'; ?>
    
    <style>
        body {
            background-color: var(--bs-body-bg, #f8f9fa);
            color: var(--bs-body-color, #212529);
            font-family: var(--bs-font-sans-serif, 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif);
        }

        .navbar {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
        }

        .navbar-logo {
            max-height: 40px;
            max-width: 150px;
            height: auto;
            width: auto;
            object-fit: contain;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .navbar-logo {
                max-height: 32px;
                max-width: 120px;
            }

            .navbar-brand {
                font-size: 1rem;
            }
        }
        
        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .navbar-nav .nav-link:hover {
            color: white !important;
        }
        
        .volunteer-container {
            margin-top: 2rem;
        }
        
        .volunteer-header {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            color: white;
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .volunteer-card {
            background: var(--bs-body-bg, white);
            border-radius: var(--bs-border-radius, 15px);
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: none;
        }
        
        .opportunity-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }
        
        .opportunity-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .category-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
        
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .skill-tag {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 15px;
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            color: #495057;
        }

        /* Pagination Styles */
        .pagination .page-link {
            border: none;
            color: var(--bs-primary, #667eea);
            background: transparent;
            margin: 0 2px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .pagination .page-link:hover {
            background: var(--bs-primary, #667eea);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, var(--bs-primary, #667eea) 0%, var(--bs-secondary, #764ba2) 100%);
            border: none;
            color: white;
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .pagination .page-item.disabled .page-link {
            color: var(--bs-gray-400, #6c757d);
            background: transparent;
        }

        .pagination-info {
            color: var(--bs-gray-600, #6c757d);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <?php include 'includes/navbar.php'; ?>

    <div class="container volunteer-container">
        <!-- Volunteer Header -->
        <div class="volunteer-header">
            <h1><i class="bi bi-person-workspace"></i> Volunteer Opportunities</h1>
            <p class="mb-0">Find meaningful ways to serve and make a difference in your community</p>
        </div>

        <!-- Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- My Applications Section -->
        <?php if (!empty($my_applications)): ?>
        <div class="volunteer-card">
            <h5><i class="bi bi-file-earmark-person"></i> My Applications</h5>
            <div class="row">
                <?php foreach ($my_applications as $app): ?>
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h6 class="card-title mb-0"><?php echo htmlspecialchars($app['opportunity_title']); ?></h6>
                                <span class="badge bg-<?php
                                    echo $app['status'] === 'approved' ? 'success' :
                                        ($app['status'] === 'rejected' ? 'danger' : 'warning');
                                ?>">
                                    <?php echo ucfirst($app['status']); ?>
                                </span>
                            </div>
                            <p class="card-text small text-muted">
                                Applied: <?php echo date('M j, Y', strtotime($app['created_at'])); ?>
                            </p>
                            <?php if ($app['status'] === 'pending'): ?>
                                <button class="btn btn-sm btn-outline-danger withdraw-btn"
                                        data-application-id="<?php echo $app['id']; ?>"
                                        data-opportunity-title="<?php echo htmlspecialchars($app['opportunity_title']); ?>">
                                    <i class="bi bi-x-circle"></i> Withdraw
                                </button>
                            <?php endif; ?>
                            <?php if ($app['review_notes']): ?>
                                <div class="mt-2">
                                    <small class="text-muted">
                                        <strong>Review Notes:</strong> <?php echo htmlspecialchars($app['review_notes']); ?>
                                    </small>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Available Opportunities -->
        <div class="volunteer-card">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h5><i class="bi bi-briefcase"></i> Available Opportunities</h5>
                <div>
                    <span class="badge bg-primary"><?php echo $totalOpportunities; ?> Total</span>
                </div>
            </div>

            <?php if (empty($opportunities)): ?>
                <div class="text-center py-5">
                    <i class="bi bi-briefcase text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">No Volunteer Opportunities Available</h4>
                    <p class="text-muted">Check back later for new volunteer opportunities.</p>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($opportunities as $opp): ?>
                    <div class="col-lg-6 mb-4">
                        <div class="opportunity-card h-100">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <div>
                                    <h5 class="mb-1"><?php echo htmlspecialchars($opp['title']); ?></h5>
                                    <span class="badge bg-secondary category-badge"><?php echo ucfirst($opp['category']); ?></span>
                                </div>
                                <div class="text-end">
                                    <?php if ($opp['my_application_status']): ?>
                                        <span class="badge bg-<?php
                                            echo $opp['my_application_status'] === 'approved' ? 'success' :
                                                ($opp['my_application_status'] === 'rejected' ? 'danger' : 'warning');
                                        ?> status-badge">
                                            <?php echo ucfirst($opp['my_application_status']); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <p class="text-muted mb-3"><?php echo htmlspecialchars($opp['description']); ?></p>

                            <div class="mb-3">
                                <div class="row text-sm">
                                    <div class="col-6">
                                        <strong>Contact:</strong><br>
                                        <small><?php echo htmlspecialchars($opp['contact_name'] ?? 'Not specified'); ?></small>
                                    </div>
                                    <div class="col-6">
                                        <strong>Schedule:</strong><br>
                                        <small><?php echo ucfirst(str_replace('_', ' ', $opp['schedule_type'])); ?></small>
                                    </div>
                                </div>
                            </div>

                            <?php if ($opp['time_commitment']): ?>
                            <div class="mb-2">
                                <strong>Time Commitment:</strong>
                                <small><?php echo htmlspecialchars($opp['time_commitment']); ?></small>
                            </div>
                            <?php endif; ?>

                            <?php if ($opp['location']): ?>
                            <div class="mb-2">
                                <strong>Location:</strong>
                                <small><?php echo htmlspecialchars($opp['location']); ?></small>
                            </div>
                            <?php endif; ?>

                            <?php if ($opp['required_skills']): ?>
                            <div class="mb-2">
                                <strong>Required Skills:</strong>
                                <div class="skills-list">
                                    <?php
                                    $skills = json_decode($opp['required_skills'], true);
                                    if ($skills) {
                                        foreach ($skills as $skill) {
                                            echo '<span class="skill-tag">' . htmlspecialchars(trim($skill)) . '</span>';
                                        }
                                    }
                                    ?>
                                </div>
                            </div>
                            <?php endif; ?>

                            <div class="mt-3 d-flex justify-content-between align-items-center">
                                <div>
                                    <?php if ($opp['max_volunteers']): ?>
                                        <small class="text-muted">
                                            <?php echo $opp['filled_positions']; ?>/<?php echo $opp['max_volunteers']; ?> filled
                                        </small>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <button class="btn btn-outline-info btn-sm me-2"
                                            onclick="viewOpportunityDetails(<?php echo $opp['id']; ?>)">
                                        <i class="bi bi-eye"></i> View Details
                                    </button>
                                    <?php if (!$opp['my_application_status']): ?>
                                        <?php if ($opp['max_volunteers'] && $opp['filled_positions'] >= $opp['max_volunteers']): ?>
                                            <span class="badge bg-secondary">Position Filled</span>
                                        <?php else: ?>
                                            <button class="btn btn-primary btn-sm apply-btn"
                                                    data-opportunity-id="<?php echo $opp['id']; ?>"
                                                    data-opportunity-title="<?php echo htmlspecialchars($opp['title']); ?>"
                                                    onclick="applyForOpportunity(<?php echo $opp['id']; ?>, <?php echo json_encode($opp['title']); ?>)">
                                                <i class="bi bi-hand-thumbs-up"></i> Apply Now
                                            </button>
                                        <?php endif; ?>
                                    <?php elseif ($opp['my_application_status'] === 'pending'): ?>
                                        <button class="btn btn-sm btn-outline-danger withdraw-btn"
                                                data-application-id="<?php echo $opp['my_application_id']; ?>"
                                                data-opportunity-title="<?php echo htmlspecialchars($opp['title']); ?>">
                                            <i class="bi bi-x-circle"></i> Withdraw
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <!-- Pagination Info -->
                    <div class="pagination-info text-center mt-4">
                        Showing <?php echo min($offset + 1, $totalOpportunities); ?>-<?php echo min($offset + $opportunitiesPerPage, $totalOpportunities); ?> of <?php echo $totalOpportunities; ?> opportunities
                    </div>

                    <div class="d-flex justify-content-center mt-3">
                        <nav aria-label="Opportunities pagination">
                            <ul class="pagination">
                                <!-- Previous Page -->
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">
                                            <i class="bi bi-chevron-left"></i> Previous
                                        </span>
                                    </li>
                                <?php endif; ?>

                                <!-- Page Numbers -->
                                <?php
                                $startPage = max(1, $page - 2);
                                $endPage = min($totalPages, $page + 2);

                                if ($startPage > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">1</a>
                                    </li>
                                    <?php if ($startPage > 2): ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>

                                <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($endPage < $totalPages): ?>
                                    <?php if ($endPage < $totalPages - 1): ?>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                    <?php endif; ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $totalPages; ?>"><?php echo $totalPages; ?></a>
                                    </li>
                                <?php endif; ?>

                                <!-- Next Page -->
                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </a>
                                    </li>
                                <?php else: ?>
                                    <li class="page-item disabled">
                                        <span class="page-link">
                                            Next <i class="bi bi-chevron-right"></i>
                                        </span>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    </div>
                <?php endif; ?>

            <?php endif; ?>
        </div>
    </div>

    <!-- Apply for Opportunity Modal -->
    <div class="modal fade" id="applyModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form method="POST">
                    <div class="modal-header">
                        <h5 class="modal-title">Apply for Volunteer Opportunity</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="opportunity_id" id="apply_opportunity_id">

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            You are applying for: <strong id="apply_opportunity_title"></strong>
                        </div>

                        <div class="mb-3">
                            <label for="application_message" class="form-label">Why are you interested in this opportunity? *</label>
                            <textarea class="form-control" name="application_message" rows="4" required
                                      placeholder="Tell us why you're interested and what you hope to contribute..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="availability" class="form-label">Your Availability</label>
                            <textarea class="form-control" name="availability" rows="3"
                                      placeholder="When are you available? (e.g., weekends, evenings, specific days)"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="relevant_experience" class="form-label">Relevant Experience</label>
                            <textarea class="form-control" name="relevant_experience" rows="3"
                                      placeholder="Describe any relevant experience, skills, or qualifications..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="apply_for_opportunity" class="btn btn-primary">
                            <i class="bi bi-send"></i> Submit Application
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Opportunity Details Modal -->
    <div class="modal fade" id="detailsModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Volunteer Opportunity Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="detailsModalContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Ensure DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, Bootstrap version:', bootstrap.Modal ? 'Available' : 'Not available');
        });

        function applyForOpportunity(opportunityId, title) {
            console.log('Apply button clicked for opportunity:', opportunityId, title);

            // Set the form values
            document.getElementById('apply_opportunity_id').value = opportunityId;
            document.getElementById('apply_opportunity_title').textContent = title;

            // Show modal using Bootstrap 5 data attributes (more reliable)
            const modalElement = document.getElementById('applyModal');
            if (modalElement) {
                // Try multiple approaches
                try {
                    // Method 1: Bootstrap 5 Modal class
                    const modal = new bootstrap.Modal(modalElement);
                    modal.show();
                } catch (e1) {
                    console.log('Method 1 failed, trying method 2:', e1);
                    try {
                        // Method 2: Direct Bootstrap modal show
                        bootstrap.Modal.getOrCreateInstance(modalElement).show();
                    } catch (e2) {
                        console.log('Method 2 failed, trying method 3:', e2);
                        try {
                            // Method 3: Using data-bs-toggle approach
                            modalElement.classList.add('show');
                            modalElement.style.display = 'block';
                            modalElement.setAttribute('aria-modal', 'true');
                            modalElement.setAttribute('role', 'dialog');
                            document.body.classList.add('modal-open');

                            // Add backdrop
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            backdrop.id = 'modal-backdrop-temp';
                            document.body.appendChild(backdrop);

                            console.log('Manual modal show applied');
                        } catch (e3) {
                            console.error('All methods failed:', e3);
                            alert('Unable to open application form. Please refresh the page and try again.');
                        }
                    }
                }
            } else {
                console.error('Modal element not found');
                alert('Application form not available. Please refresh the page.');
            }
        }

        function withdrawApplication(applicationId, title) {
            console.log('Withdraw function called with:', applicationId, title);

            if (!applicationId) {
                console.error('No application ID provided');
                alert('Error: No application ID found. Please refresh the page and try again.');
                return;
            }

            if (confirm(`Are you sure you want to withdraw your application for "${title}"?`)) {
                try {
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.innerHTML = `
                        <input type="hidden" name="application_id" value="${applicationId}">
                        <input type="hidden" name="withdraw_application" value="1">
                    `;
                    document.body.appendChild(form);
                    console.log('Submitting withdrawal form for application ID:', applicationId);
                    form.submit();
                } catch (error) {
                    console.error('Error submitting withdrawal form:', error);
                    alert('Error submitting withdrawal. Please try again.');
                }
            }
        }

        // Function to manually close modal if needed
        function closeModal() {
            const modalElement = document.getElementById('applyModal');
            const backdrop = document.getElementById('modal-backdrop-temp');

            if (modalElement) {
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                modalElement.removeAttribute('aria-modal');
                modalElement.removeAttribute('role');
            }

            if (backdrop) {
                backdrop.remove();
            }

            document.body.classList.remove('modal-open');
        }

        // Function to view opportunity details
        function viewOpportunityDetails(opportunityId) {
            console.log('View details clicked for opportunity:', opportunityId);

            const modalElement = document.getElementById('detailsModal');
            const contentElement = document.getElementById('detailsModalContent');

            // Show loading spinner
            contentElement.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2">Loading opportunity details...</p>
                </div>
            `;

            // Show modal
            const modal = new bootstrap.Modal(modalElement);
            modal.show();

            // Fetch opportunity details
            fetch('ajax/get_opportunity_details.php?id=' + opportunityId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const opp = data.opportunity;
                        contentElement.innerHTML = `
                            <div class="opportunity-details">
                                <h4>${opp.title}</h4>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>Category:</strong> ${opp.category || 'General'}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Department:</strong> ${opp.department || 'Not specified'}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <strong>Description:</strong>
                                    <p class="mt-2">${opp.description}</p>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <strong>Contact Person:</strong><br>
                                        ${opp.contact_name || 'Not specified'}
                                        ${opp.contact_email ? '<br><small class="text-muted">' + opp.contact_email + '</small>' : ''}
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Schedule:</strong><br>
                                        ${opp.schedule_type ? opp.schedule_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Not specified'}
                                    </div>
                                </div>
                                ${opp.time_commitment ? '<div class="mb-3"><strong>Time Commitment:</strong> ' + opp.time_commitment + '</div>' : ''}
                                ${opp.location ? '<div class="mb-3"><strong>Location:</strong> ' + opp.location + '</div>' : ''}
                                ${opp.required_skills ? '<div class="mb-3"><strong>Required Skills:</strong><br>' + opp.required_skills.split(',').map(skill => '<span class="badge bg-secondary me-1">' + skill.trim() + '</span>').join('') + '</div>' : ''}
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong>Positions:</strong> ${opp.filled_positions}/${opp.max_volunteers || '∞'} filled
                                    </div>
                                    <div class="col-md-6">
                                        <strong>Status:</strong> <span class="badge bg-${opp.status === 'active' ? 'success' : 'secondary'}">${opp.status}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    } else {
                        contentElement.innerHTML = `
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i>
                                Error loading opportunity details: ${data.message || 'Unknown error'}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error fetching opportunity details:', error);
                    contentElement.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                            Error loading opportunity details. Please try again.
                        </div>
                    `;
                });
        }

        // Add event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Setting up event listeners...');

            // Add click event to close button if manual modal is used
            const closeButtons = document.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(button => {
                button.addEventListener('click', closeModal);
            });

            // Alternative event listener approach for Apply buttons
            const applyButtons = document.querySelectorAll('.apply-btn');
            console.log('Found apply buttons:', applyButtons.length);

            applyButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Apply button clicked via event listener');

                    const opportunityId = this.getAttribute('data-opportunity-id');
                    const opportunityTitle = this.getAttribute('data-opportunity-title');

                    console.log('Data from button:', opportunityId, opportunityTitle);

                    if (opportunityId && opportunityTitle) {
                        applyForOpportunity(parseInt(opportunityId), opportunityTitle);
                    } else {
                        console.error('Missing data attributes on button');
                    }
                });
            });

            // Event listeners for Withdraw buttons
            const withdrawButtons = document.querySelectorAll('.withdraw-btn');
            console.log('Found withdraw buttons:', withdrawButtons.length);

            withdrawButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Withdraw button clicked via event listener');

                    const applicationId = this.getAttribute('data-application-id');
                    const opportunityTitle = this.getAttribute('data-opportunity-title');

                    console.log('Data from withdraw button:', applicationId, opportunityTitle);

                    if (applicationId && opportunityTitle) {
                        withdrawApplication(parseInt(applicationId), opportunityTitle);
                    } else {
                        console.error('Missing data attributes on withdraw button');
                        alert('Error: Missing application data. Please refresh the page and try again.');
                    }
                });
            });
        });
    </script>

    <!-- Footer -->
    <?php include 'includes/footer.php'; ?>
</body>
</html>
