<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../includes/admin_email_notifications.php';

$message = '';
$error = '';

// Handle admin user creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
    try {
        // Check if creating from existing member (checkbox checked)
        $creation_type = isset($_POST['creation_type']) && $_POST['creation_type'] === 'member' ? 'member' : 'new';
        $role = $_POST['role'] ?? '';

        if ($creation_type === 'member' && !empty($_POST['selected_member'])) {
            // Creating admin from existing member
            $member_id = (int)$_POST['selected_member'];

            // Get member details
            $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ? AND status = 'active'");
            $stmt->execute([$member_id]);
            $member = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$member) {
                throw new Exception('Selected member not found or inactive.');
            }

            $username = trim($_POST['username']) ?: explode('@', $member['email'])[0];
            $email = $member['email'];
            $full_name = $member['full_name'] ?: ($member['first_name'] . ' ' . $member['last_name']);
            $password = $_POST['password'];

            // Validate required fields
            if (empty($password) || empty($role)) {
                throw new Exception('Password and role are required.');
            }
        } else {
            // Creating new admin user
            $username = trim($_POST['username']);
            $email = trim($_POST['email']);
            $password = $_POST['password'];
            $full_name = trim($_POST['full_name']);

            // Validate input
            if (empty($username) || empty($email) || empty($password) || empty($role)) {
                throw new Exception('Username, email, password, and role are required.');
            }
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address.');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Password must be at least 6 characters long.');
        }
        
        // Check if username or email already exists
        $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        if ($stmt->fetch()) {
            throw new Exception('Username or email already exists.');
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new admin with default_role
        $stmt = $pdo->prepare("
            INSERT INTO admins (username, email, password, full_name, default_role, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$username, $email, $hashed_password, $full_name, $role]);

        $new_admin_id = $pdo->lastInsertId();

        // Also create role assignment in user_role_assignments table
        try {
            // Get role ID from user_roles table
            $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = ?");
            $stmt->execute([$role]);
            $role_id = $stmt->fetchColumn();

            if ($role_id) {
                // Create role assignment
                $stmt = $pdo->prepare("
                    INSERT INTO user_role_assignments (user_id, role_id, assigned_by, assigned_at, is_active)
                    VALUES (?, ?, ?, NOW(), 1)
                ");
                $stmt->execute([$new_admin_id, $role_id, $_SESSION['admin_id']]);
            }
        } catch (Exception $e) {
            // Log error but don't fail user creation
            error_log("Failed to create role assignment: " . $e->getMessage());
        }

        // Send admin user creation notification email
        try {
            $emailSent = sendAdminUserCreationNotification($new_admin_id, $_SESSION['admin_id'], $password);
            if ($emailSent) {
                $message = "Admin user '{$username}' created successfully! Welcome email sent.";
            } else {
                $message = "Admin user '{$username}' created successfully! (Note: Welcome email could not be sent)";
            }
        } catch (Exception $e) {
            error_log("Failed to send admin creation notification: " . $e->getMessage());
            $message = "Admin user '{$username}' created successfully! (Note: Welcome email could not be sent)";
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle admin user deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_admin') {
    try {
        $admin_id = $_POST['admin_id'] ?? '';

        if (empty($admin_id)) {
            throw new Exception('Admin ID is required for deletion.');
        }

        // Prevent self-deletion
        if ($admin_id == $_SESSION['admin_id']) {
            throw new Exception('You cannot delete your own account.');
        }

        // Get admin info before deletion for message
        $stmt = $pdo->prepare("SELECT username FROM admins WHERE id = ?");
        $stmt->execute([$admin_id]);
        $admin_info = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$admin_info) {
            throw new Exception('Admin user not found.');
        }

        // Begin transaction for safe deletion
        $pdo->beginTransaction();

        try {
            // Delete from user_role_assignments first (foreign key constraint)
            $stmt = $pdo->prepare("DELETE FROM user_role_assignments WHERE user_id = ?");
            $stmt->execute([$admin_id]);

            // Delete from event_assignments if exists
            $stmt = $pdo->prepare("DELETE FROM event_assignments WHERE user_id = ?");
            $stmt->execute([$admin_id]);

            // Delete from session_assignments if exists
            $stmt = $pdo->prepare("DELETE FROM session_assignments WHERE user_id = ?");
            $stmt->execute([$admin_id]);

            // Finally delete the admin user
            $stmt = $pdo->prepare("DELETE FROM admins WHERE id = ?");
            $stmt->execute([$admin_id]);

            $pdo->commit();
            $message = "Admin user '{$admin_info['username']}' has been deleted successfully.";

        } catch (Exception $e) {
            $pdo->rollBack();
            throw new Exception('Failed to delete admin user: ' . $e->getMessage());
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle bulk admin creation for testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_admins'])) {
    try {
        $test_admins = [
            ['username' => 'coordinator1', 'email' => '<EMAIL>', 'full_name' => 'Sarah Johnson', 'role' => 'event_coordinator'],
            ['username' => 'coordinator2', 'email' => '<EMAIL>', 'full_name' => 'Michael Davis', 'role' => 'event_coordinator'],
            ['username' => 'moderator1', 'email' => '<EMAIL>', 'full_name' => 'Emily Wilson', 'role' => 'session_moderator'],
            ['username' => 'moderator2', 'email' => '<EMAIL>', 'full_name' => 'David Brown', 'role' => 'session_moderator'],
            ['username' => 'staff1', 'email' => '<EMAIL>', 'full_name' => 'Lisa Anderson', 'role' => 'staff'],
            ['username' => 'staff2', 'email' => '<EMAIL>', 'full_name' => 'James Miller', 'role' => 'staff'],
            ['username' => 'organizer1', 'email' => '<EMAIL>', 'full_name' => 'Rachel Green', 'role' => 'organizer'],
            ['username' => 'limitedadmin1', 'email' => '<EMAIL>', 'full_name' => 'Mark Thompson', 'role' => 'limited_admin'],
        ];
        
        $created_count = 0;
        $default_password = 'password123'; // Simple password for testing
        $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
        
        foreach ($test_admins as $admin) {
            // Check if user already exists
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
            $stmt->execute([$admin['username'], $admin['email']]);
            
            if (!$stmt->fetch()) {
                // Create the admin user with default_role
                $stmt = $pdo->prepare("
                    INSERT INTO admins (username, email, password, full_name, default_role, created_at)
                    VALUES (?, ?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$admin['username'], $admin['email'], $hashed_password, $admin['full_name'], $admin['role']]);

                $new_admin_id = $pdo->lastInsertId();

                // Also create role assignment
                try {
                    $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = ?");
                    $stmt->execute([$admin['role']]);
                    $role_id = $stmt->fetchColumn();

                    if ($role_id) {
                        $stmt = $pdo->prepare("
                            INSERT INTO user_role_assignments (user_id, role_id, assigned_by, assigned_at, is_active)
                            VALUES (?, ?, ?, NOW(), 1)
                        ");
                        $stmt->execute([$new_admin_id, $role_id, $_SESSION['admin_id']]);
                    }
                } catch (Exception $e) {
                    error_log("Failed to create role assignment for test user: " . $e->getMessage());
                }

                // Send admin user creation notification email for test users
                try {
                    sendAdminUserCreationNotification($new_admin_id, $_SESSION['admin_id'], $default_password);
                } catch (Exception $e) {
                    error_log("Failed to send test admin creation notification: " . $e->getMessage());
                }

                $created_count++;
            }
        }
        
        $message = "Created {$created_count} test admin users successfully! Default password: {$default_password}";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get existing admin users
$stmt = $pdo->query("SELECT id, username, email, full_name, created_at FROM admins ORDER BY created_at DESC");
$existing_admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all members for member selection dropdown
try {
    $stmt = $pdo->prepare("
        SELECT id,
               COALESCE(full_name, CONCAT(first_name, ' ', last_name), email) as full_name,
               email,
               phone_number
        FROM members
        WHERE status = 'active'
        ORDER BY full_name
    ");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $members = [];
    error_log("Error fetching members: " . $e->getMessage());
}

// Page title and header info
$page_title = 'Create Admin Users';
$page_header = 'Create Admin Users';
$page_description = 'Create new administrative users with hierarchical role assignments. For advanced role management, use the RBAC Management System.';

// Include header
include 'includes/header.php';
?>

<style>
.admin-header {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 10px;
}
.assignment-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    position: relative;
}
.assignment-card:hover {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}
.btn-group-vertical .btn {
    margin-bottom: 2px;
}
.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
.assignment-card .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card admin-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-person-plus"></i> Create Admin Users
                        </h2>
                        <p class="text-white-50 mb-0">Create new administrative users with hierarchical role assignments</p>
                    </div>
                    <div>
                        <a href="setup_rbac_system.php" class="btn btn-outline-light">
                            <i class="bi bi-shield-check"></i> Back to RBAC
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>



<!-- Manual Admin Creation -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus"></i> Create New Admin User
                </h5>
                <small class="text-muted">Create a new administrative user with a hierarchical role assignment</small>
            </div>
            <div class="card-body">
                <form method="POST">
                    <!-- Create from existing member checkbox -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="create_from_member" name="creation_type" value="member">
                            <label class="form-check-label" for="create_from_member">
                                Create admin from existing member
                            </label>
                        </div>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i> Check this box to create an admin account from an existing church member
                        </div>
                    </div>

                    <!-- Existing Member Selection -->
                    <div class="mb-3" id="member_selection" style="display: none;">
                        <label for="selected_member" class="form-label">Select Member *</label>
                        <select class="form-select" id="selected_member" name="selected_member">
                            <option value="">Choose a member...</option>
                            <?php foreach ($members as $member): ?>
                                <option value="<?php echo $member['id']; ?>"
                                        data-name="<?php echo htmlspecialchars($member['full_name']); ?>"
                                        data-email="<?php echo htmlspecialchars($member['email']); ?>">
                                    <?php echo htmlspecialchars($member['full_name']); ?>
                                    <?php if ($member['email']): ?>
                                        (<?php echo htmlspecialchars($member['email']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">
                            <i class="bi bi-info-circle"></i> Select an existing member to convert to admin user
                        </div>
                    </div>

                    <!-- New User Fields -->
                    <div id="new_user_fields">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>

                        <div class="mb-3">
                            <label for="full_name" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="full_name" name="full_name">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="role" class="form-label">Administrative Role *</label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">Select administrative role...</option>

                            <!-- Hierarchical Role Structure (Lowest to Highest Privilege) -->
                            <optgroup label="Basic Access">
                                <option value="staff">Staff Member</option>
                            </optgroup>

                            <optgroup label="Specialized Roles">
                                <option value="session_moderator">Session Moderator</option>
                                <option value="event_coordinator">Event Coordinator</option>
                                <option value="organizer">Event Organizer</option>
                            </optgroup>

                            <optgroup label="Administrative Roles">
                                <option value="limited_admin">Limited Administrator</option>
                                <option value="super_admin">Super Administrator</option>
                            </optgroup>
                        </select>
                        <div class="form-text">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Basic Access:</strong><br>
                                    • <strong>Staff Member:</strong> Basic system access, limited functions
                                </div>
                                <div class="col-md-6">
                                    <strong>Specialized Roles:</strong><br>
                                    • <strong>Session Moderator:</strong> Manage sessions and attendance<br>
                                    • <strong>Event Coordinator:</strong> Coordinate events and activities<br>
                                    • <strong>Event Organizer:</strong> Organize and plan events
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <strong>Administrative Roles:</strong><br>
                                    • <strong>Limited Administrator:</strong> Most admin functions with some restrictions<br>
                                    • <strong>Super Administrator:</strong> Full system access and control
                                </div>
                            </div>
                            <div class="alert alert-info mt-2 mb-0">
                                <small><i class="bi bi-info-circle"></i> <strong>Note:</strong> For advanced role management, multiple role assignments, and granular permissions, use the <a href="setup_rbac_system.php" class="alert-link">RBAC Management System</a>.</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password *</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>
                    
                    <button type="submit" name="create_admin" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Create Admin User
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i> Existing Admin Users (<?php echo count($existing_admins); ?>)
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($existing_admins)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                        <p>No admin users found.</p>
                    </div>
                <?php else: ?>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($existing_admins as $admin): ?>
                            <div class="assignment-card">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong><?php echo htmlspecialchars($admin['username']); ?></strong>
                                        <?php if ($admin['email']): ?>
                                            <small class="text-muted">(<?php echo htmlspecialchars($admin['email']); ?>)</small>
                                        <?php endif; ?>
                                        <?php if ($admin['full_name']): ?>
                                            <br>
                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($admin['full_name']); ?></span>
                                        <?php endif; ?>
                                        <br>
                                        <small class="text-muted">
                                            <i class="bi bi-person-badge"></i> ID: <?php echo $admin['id']; ?>
                                        </small>
                                        <br>
                                        <small class="text-muted">
                                            <i class="bi bi-calendar-plus"></i> Created: <?php echo date('M j, Y g:i A', strtotime($admin['created_at'])); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <!-- View/Edit User -->
                                            <a href="edit_admin.php?id=<?php echo $admin['id']; ?>" class="btn btn-outline-primary btn-sm">
                                                <i class="bi bi-pencil"></i> Edit
                                            </a>

                                            <!-- Assign Role -->
                                            <a href="setup_rbac_system.php#assign-role" class="btn btn-outline-success btn-sm">
                                                <i class="bi bi-shield-plus"></i> Assign Role
                                            </a>

                                            <!-- Delete User (if not current user) -->
                                            <?php if ($admin['id'] != $_SESSION['admin_id']): ?>
                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('Delete admin user <?php echo htmlspecialchars($admin['username']); ?>? This action cannot be undone.')">
                                                    <input type="hidden" name="action" value="delete_admin">
                                                    <input type="hidden" name="admin_id" value="<?php echo $admin['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                                        <i class="bi bi-trash"></i> Delete
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" disabled title="Cannot delete yourself">
                                                    <i class="bi bi-shield-check"></i> Current User
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Information -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> About Admin User Management
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>How Admin Users Work:</h6>
                        <ul>
                            <li><strong>Admin Users</strong> are created through this interface or the registration system</li>
                            <li><strong>Roles</strong> are assigned separately through RBAC Management</li>
                            <li><strong>Login</strong> is done through the same login.php page for all users</li>
                            <li><strong>Dashboard Routing</strong> happens automatically based on assigned roles</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Next Steps:</h6>
                        <ol>
                            <li>Create admin users here (or use quick test users)</li>
                            <li>Go to <a href="setup_rbac_system.php">RBAC Management</a></li>
                            <li>Assign roles to the users</li>
                            <li>Test with <a href="system_test_dashboard.php">Role Simulation</a></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Handle creation type toggle
document.addEventListener('DOMContentLoaded', function() {
    const createFromMemberCheckbox = document.getElementById('create_from_member');
    const memberSelection = document.getElementById('member_selection');
    const newUserFields = document.getElementById('new_user_fields');
    const selectedMemberDropdown = document.getElementById('selected_member');
    const usernameField = document.getElementById('username');
    const emailField = document.getElementById('email');
    const fullNameField = document.getElementById('full_name');

    function toggleCreationType() {
        if (createFromMemberCheckbox.checked) {
            // Show member selection, hide manual fields for name and email
            memberSelection.style.display = 'block';

            // Hide only the full name and email fields, keep username visible
            emailField.parentElement.style.display = 'none';
            fullNameField.parentElement.style.display = 'none';

            // Make member selection required
            selectedMemberDropdown.required = true;
            emailField.required = false;
            fullNameField.required = false;

            // Clear the hidden fields
            emailField.value = '';
            fullNameField.value = '';
        } else {
            // Hide member selection, show all manual fields
            memberSelection.style.display = 'none';

            // Show all manual input fields
            emailField.parentElement.style.display = 'block';
            fullNameField.parentElement.style.display = 'block';

            // Make manual fields required
            selectedMemberDropdown.required = false;
            usernameField.required = true;
            emailField.required = true;

            // Clear member selection
            selectedMemberDropdown.value = '';
        }
    }

    // Handle member selection - auto-populate fields
    selectedMemberDropdown.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            const memberName = selectedOption.dataset.name;
            const memberEmail = selectedOption.dataset.email;

            // Auto-fill the hidden fields with member data
            emailField.value = memberEmail;
            fullNameField.value = memberName;

            // Suggest username based on email prefix, but allow manual editing
            if (!usernameField.value) {
                usernameField.value = memberEmail.split('@')[0];
            }
        } else {
            // Clear fields when no member selected
            emailField.value = '';
            fullNameField.value = '';
        }
    });

    // Add event listener for checkbox
    createFromMemberCheckbox.addEventListener('change', toggleCreationType);

    // Initialize
    toggleCreationType();
});
</script>

<?php include 'includes/footer.php'; ?>
