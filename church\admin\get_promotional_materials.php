<?php
/**
 * Get Promotional Materials for Event
 * 
 * Returns promotional materials and files for a specific event
 */

session_start();

// Include configuration
require_once '../config.php';
require_once 'includes/auth_check.php';

header('Content-Type: application/json');

$event_id = filter_input(INPUT_GET, 'event_id', FILTER_VALIDATE_INT);

if (!$event_id) {
    echo json_encode(['success' => false, 'message' => 'Invalid event ID']);
    exit();
}

try {
    // Get all files for the event
    $stmt = $pdo->prepare("
        SELECT id, file_name, file_path, file_type, file_size, file_category,
               is_header_banner, alt_text, display_order, upload_date
        FROM event_files
        WHERE event_id = ?
        ORDER BY is_header_banner DESC, display_order ASC, upload_date DESC
    ");
    $stmt->execute([$event_id]);
    $files = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format file data
    $promotional_materials = [];
    $documents = [];
    $header_banner = null;
    
    foreach ($files as $file) {
        // Simplify path conversion - just use the stored path as-is for admin context
        $web_path = $file['file_path'];

        // Log the original path for debugging
        error_log("PROMOTIONAL MATERIALS - Original path: " . $web_path);

        $file_data = [
            'id' => $file['id'],
            'name' => $file['file_name'],
            'path' => $web_path,
            'type' => $file['file_type'],
            'size' => formatFileSize($file['file_size']),
            'size_bytes' => $file['file_size'],
            'category' => $file['file_category'],
            'is_header_banner' => (bool)$file['is_header_banner'],
            'alt_text' => $file['alt_text'],
            'display_order' => $file['display_order'],
            'upload_date' => $file['upload_date'],
            'is_image' => strpos($file['file_type'], 'image/') === 0,
            'thumbnail' => null
        ];
        
        // Add thumbnail path for images
        if ($file_data['is_image']) {
            // Create thumbnail path by replacing 'promotional' with 'thumbnails' and adding 'thumb_' prefix
            $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($file['file_path'])) . '/thumb_' . basename($file['file_path']);

            if (file_exists($thumbnail_path)) {
                $file_data['thumbnail'] = $thumbnail_path;
                error_log("PROMOTIONAL MATERIALS - Thumbnail found: " . $thumbnail_path);
            } else {
                error_log("PROMOTIONAL MATERIALS - Thumbnail not found: " . $thumbnail_path);
            }
        }
        
        // Categorize files
        if ($file['is_header_banner']) {
            $header_banner = $file_data;
        }
        
        if ($file['file_category'] === 'promotional') {
            $promotional_materials[] = $file_data;
        } else {
            $documents[] = $file_data;
        }
    }
    
    echo json_encode([
        'success' => true,
        'header_banner' => $header_banner,
        'promotional_materials' => $promotional_materials,
        'documents' => $documents,
        'total_files' => count($files)
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error retrieving files: ' . $e->getMessage()]);
}

/**
 * Format file size in human readable format
 */
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
