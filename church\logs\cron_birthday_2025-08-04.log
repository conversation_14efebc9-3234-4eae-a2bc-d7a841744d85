[2025-08-04 10:28:47] Request received: format=json, cron_key=fac***
[2025-08-04 10:28:47] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"52640","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754296127.345941,"REQUEST_TIME":1754296127}
[2025-08-04 10:28:47] Access granted: via cron key
[2025-08-04 10:28:47] Starting birthday reminders cron job
[2025-08-04 10:28:47] Database connection successful. Found 2 total members.
[2025-08-04 10:29:26] Request received: format=json, cron_key=fac***
[2025-08-04 10:29:26] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"52721","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754296166.615868,"REQUEST_TIME":1754296166}
[2025-08-04 10:29:26] Access granted: via cron key
[2025-08-04 10:29:26] Starting birthday reminders cron job
[2025-08-04 10:29:26] Database connection successful. Found 2 total members.
[2025-08-04 10:43:57] Request received: format=json, cron_key=fac***
[2025-08-04 10:43:57] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53273","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297037.919911,"REQUEST_TIME":1754297037}
[2025-08-04 10:43:57] Access granted: via cron key
[2025-08-04 10:43:57] Starting birthday reminders cron job
[2025-08-04 10:43:57] Database connection successful. Found 2 total members.
[2025-08-04 10:48:55] Request received: format=json, cron_key=fac***
[2025-08-04 10:48:55] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53489","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297335.443941,"REQUEST_TIME":1754297335}
[2025-08-04 10:48:55] Access granted: via cron key
[2025-08-04 10:48:55] Starting birthday reminders cron job
[2025-08-04 10:48:55] Database connection successful. Found 2 total members.
[2025-08-04 10:49:12] Request received: format=json, cron_key=fac***
[2025-08-04 10:49:12] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53532","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297352.11208,"REQUEST_TIME":1754297352}
[2025-08-04 10:49:12] Access granted: via cron key
[2025-08-04 10:49:12] Starting birthday reminders cron job
[2025-08-04 10:49:12] Database connection successful. Found 2 total members.
[2025-08-04 10:52:04] Request received: format=json, cron_key=fac***
[2025-08-04 10:52:04] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53695","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297524.357087,"REQUEST_TIME":1754297524}
[2025-08-04 10:52:04] Access granted: via cron key
[2025-08-04 10:52:04] Starting birthday reminders cron job
[2025-08-04 10:52:04] Database connection successful. Found 2 total members.
[2025-08-04 10:53:52] Request received: format=json, cron_key=fac***
[2025-08-04 10:53:52] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53928","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297632.21314,"REQUEST_TIME":1754297632}
[2025-08-04 10:53:52] Access granted: via cron key
[2025-08-04 10:53:52] Starting birthday reminders cron job
[2025-08-04 10:53:52] Database connection successful. Found 2 total members.
[2025-08-04 10:54:14] Request received: format=json, cron_key=fac***
[2025-08-04 10:54:14] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53985","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297654.724732,"REQUEST_TIME":1754297654}
[2025-08-04 10:54:14] Access granted: via cron key
[2025-08-04 10:54:14] Starting birthday reminders cron job
[2025-08-04 10:54:14] Database connection successful. Found 2 total members.
[2025-08-04 10:55:13] Request received: format=json, cron_key=fac***
[2025-08-04 10:55:13] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54088","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297713.530499,"REQUEST_TIME":1754297713}
[2025-08-04 10:55:13] Access granted: via cron key
[2025-08-04 10:55:13] Starting birthday reminders cron job
[2025-08-04 10:55:13] Database connection successful. Found 2 total members.
[2025-08-04 10:56:48] Request received: format=json, cron_key=fac***
[2025-08-04 10:56:48] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54211","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297808.024242,"REQUEST_TIME":1754297808}
[2025-08-04 10:56:48] Access granted: via cron key
[2025-08-04 10:56:48] Starting birthday reminders cron job
[2025-08-04 10:56:48] Database connection successful. Found 2 total members.
[2025-08-04 10:56:51] Request received: format=json, cron_key=fac***
[2025-08-04 10:56:51] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54245","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297811.738893,"REQUEST_TIME":1754297811}
[2025-08-04 10:56:51] Access granted: via cron key
[2025-08-04 10:56:51] Starting birthday reminders cron job
[2025-08-04 10:56:51] Database connection successful. Found 2 total members.
[2025-08-04 10:57:02] Request received: format=json, cron_key=fac***
[2025-08-04 10:57:02] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54286","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297822.56939,"REQUEST_TIME":1754297822}
[2025-08-04 10:57:02] Access granted: via cron key
[2025-08-04 10:57:02] Starting birthday reminders cron job
[2025-08-04 10:57:02] Database connection successful. Found 2 total members.
[2025-08-04 10:57:45] Request received: format=json, cron_key=fac***
[2025-08-04 10:57:45] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54356","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297865.155901,"REQUEST_TIME":1754297865}
[2025-08-04 10:57:45] Access granted: via cron key
[2025-08-04 10:57:45] Starting birthday reminders cron job
[2025-08-04 10:57:45] Database connection successful. Found 2 total members.
[2025-08-04 10:58:51] Request received: format=json, cron_key=fac***
[2025-08-04 10:58:51] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54537","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297931.318993,"REQUEST_TIME":1754297931}
[2025-08-04 10:58:51] Access granted: via cron key
[2025-08-04 10:58:51] Starting birthday reminders cron job
[2025-08-04 10:58:51] Database connection successful. Found 2 total members.
[2025-08-04 10:58:54] Request received: format=json, cron_key=fac***
[2025-08-04 10:58:54] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54567","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297934.19773,"REQUEST_TIME":1754297934}
[2025-08-04 10:58:54] Access granted: via cron key
[2025-08-04 10:58:54] Starting birthday reminders cron job
[2025-08-04 10:58:54] Database connection successful. Found 2 total members.
[2025-08-04 10:59:41] Request received: format=json, cron_key=fac***
[2025-08-04 10:59:41] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54645","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754297981.261507,"REQUEST_TIME":1754297981}
[2025-08-04 10:59:41] Access granted: via cron key
[2025-08-04 10:59:41] Starting birthday reminders cron job
[2025-08-04 10:59:41] Database connection successful. Found 2 total members.
[2025-08-04 11:03:45] Request received: format=json, cron_key=fac***
[2025-08-04 11:03:45] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"54988","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754298225.137204,"REQUEST_TIME":1754298225}
[2025-08-04 11:03:45] Access granted: via cron key
[2025-08-04 11:03:45] Starting birthday reminders cron job
[2025-08-04 11:03:45] Database connection successful. Found 2 total members.
[2025-08-04 11:05:28] Request received: format=json, cron_key=fac***
[2025-08-04 11:05:28] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"55109","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754298328.7708,"REQUEST_TIME":1754298328}
[2025-08-04 11:05:28] Access granted: via cron key
[2025-08-04 11:05:28] Starting birthday reminders cron job
[2025-08-04 11:05:28] Database connection successful. Found 2 total members.
[2025-08-04 11:18:57] Request received: format=json, cron_key=fac***
[2025-08-04 11:18:57] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"55840","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299137.49923,"REQUEST_TIME":1754299137}
[2025-08-04 11:18:57] Access granted: via cron key
[2025-08-04 11:18:57] Starting birthday reminders cron job
[2025-08-04 11:18:57] Database connection successful. Found 2 total members.
[2025-08-04 11:20:28] Request received: format=json, cron_key=fac***
[2025-08-04 11:20:28] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"55956","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299228.880678,"REQUEST_TIME":1754299228}
[2025-08-04 11:20:28] Access granted: via cron key
[2025-08-04 11:20:28] Starting birthday reminders cron job
[2025-08-04 11:20:28] Database connection successful. Found 2 total members.
[2025-08-04 11:24:53] Request received: format=json, cron_key=fac***
[2025-08-04 11:24:53] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56189","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299493.856081,"REQUEST_TIME":1754299493}
[2025-08-04 11:24:53] Access granted: via cron key
[2025-08-04 11:24:53] Starting birthday reminders cron job
[2025-08-04 11:24:53] Database connection successful. Found 2 total members.
[2025-08-04 11:24:59] Request received: format=json, cron_key=fac***
[2025-08-04 11:24:59] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56253","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299499.408084,"REQUEST_TIME":1754299499}
[2025-08-04 11:24:59] Access granted: via cron key
[2025-08-04 11:24:59] Starting birthday reminders cron job
[2025-08-04 11:24:59] Database connection successful. Found 2 total members.
[2025-08-04 11:26:21] Request received: format=json, cron_key=fac***
[2025-08-04 11:26:21] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56353","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299581.368474,"REQUEST_TIME":1754299581}
[2025-08-04 11:26:21] Access granted: via cron key
[2025-08-04 11:26:21] Starting birthday reminders cron job
[2025-08-04 11:26:21] Database connection successful. Found 2 total members.
[2025-08-04 11:28:36] Request received: format=json, cron_key=fac***
[2025-08-04 11:28:36] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56525","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299716.332363,"REQUEST_TIME":1754299716}
[2025-08-04 11:28:36] Access granted: via cron key
[2025-08-04 11:28:36] Starting birthday reminders cron job
[2025-08-04 11:28:36] Database connection successful. Found 2 total members.
[2025-08-04 11:29:42] Request received: format=json, cron_key=fac***
[2025-08-04 11:29:42] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56612","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299782.359162,"REQUEST_TIME":1754299782}
[2025-08-04 11:29:42] Access granted: via cron key
[2025-08-04 11:29:42] Starting birthday reminders cron job
[2025-08-04 11:29:42] Database connection successful. Found 2 total members.
[2025-08-04 11:29:49] Request received: format=json, cron_key=fac***
[2025-08-04 11:29:49] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56654","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299789.230088,"REQUEST_TIME":1754299789}
[2025-08-04 11:29:49] Access granted: via cron key
[2025-08-04 11:29:49] Starting birthday reminders cron job
[2025-08-04 11:29:49] Database connection successful. Found 2 total members.
[2025-08-04 11:30:24] Request received: format=json, cron_key=fac***
[2025-08-04 11:30:24] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56726","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299824.657902,"REQUEST_TIME":1754299824}
[2025-08-04 11:30:24] Access granted: via cron key
[2025-08-04 11:30:24] Starting birthday reminders cron job
[2025-08-04 11:30:24] Database connection successful. Found 2 total members.
[2025-08-04 11:30:42] Request received: format=json, cron_key=fac***
[2025-08-04 11:30:42] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56776","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299842.691895,"REQUEST_TIME":1754299842}
[2025-08-04 11:30:42] Access granted: via cron key
[2025-08-04 11:30:42] Starting birthday reminders cron job
[2025-08-04 11:30:42] Database connection successful. Found 2 total members.
[2025-08-04 11:30:47] Request received: format=json, cron_key=fac***
[2025-08-04 11:30:47] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56811","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299847.657003,"REQUEST_TIME":1754299847}
[2025-08-04 11:30:47] Access granted: via cron key
[2025-08-04 11:30:47] Starting birthday reminders cron job
[2025-08-04 11:30:47] Database connection successful. Found 2 total members.
[2025-08-04 11:31:05] Request received: format=json, cron_key=fac***
[2025-08-04 11:31:05] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56898","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299865.50607,"REQUEST_TIME":1754299865}
[2025-08-04 11:31:05] Access granted: via cron key
[2025-08-04 11:31:05] Starting birthday reminders cron job
[2025-08-04 11:31:05] Database connection successful. Found 2 total members.
[2025-08-04 11:31:06] Request received: format=json, cron_key=fac***
[2025-08-04 11:31:06] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56901","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299866.088815,"REQUEST_TIME":1754299866}
[2025-08-04 11:31:06] Access granted: via cron key
[2025-08-04 11:31:06] Starting birthday reminders cron job
[2025-08-04 11:31:06] Database connection successful. Found 2 total members.
[2025-08-04 11:31:11] Request received: format=json, cron_key=fac***
[2025-08-04 11:31:11] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56945","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299871.786881,"REQUEST_TIME":1754299871}
[2025-08-04 11:31:11] Access granted: via cron key
[2025-08-04 11:31:11] Starting birthday reminders cron job
[2025-08-04 11:31:11] Database connection successful. Found 2 total members.
[2025-08-04 11:31:19] Request received: format=json, cron_key=fac***
[2025-08-04 11:31:19] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"56989","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299879.113725,"REQUEST_TIME":1754299879}
[2025-08-04 11:31:19] Access granted: via cron key
[2025-08-04 11:31:19] Starting birthday reminders cron job
[2025-08-04 11:31:19] Database connection successful. Found 2 total members.
[2025-08-04 11:32:19] Request received: format=json, cron_key=fac***
[2025-08-04 11:32:19] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"57078","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299939.718173,"REQUEST_TIME":1754299939}
[2025-08-04 11:32:19] Access granted: via cron key
[2025-08-04 11:32:19] Starting birthday reminders cron job
[2025-08-04 11:32:19] Database connection successful. Found 2 total members.
[2025-08-04 11:32:23] Request received: format=json, cron_key=fac***
[2025-08-04 11:32:23] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"57109","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299943.28028,"REQUEST_TIME":1754299943}
[2025-08-04 11:32:23] Access granted: via cron key
[2025-08-04 11:32:23] Starting birthday reminders cron job
[2025-08-04 11:32:23] Database connection successful. Found 2 total members.
[2025-08-04 11:32:33] Request received: format=json, cron_key=fac***
[2025-08-04 11:32:33] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"57148","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754299953.695384,"REQUEST_TIME":1754299953}
[2025-08-04 11:32:33] Access granted: via cron key
[2025-08-04 11:32:33] Starting birthday reminders cron job
[2025-08-04 11:32:33] Database connection successful. Found 2 total members.
[2025-08-04 11:34:11] Request received: format=json, cron_key=fac***
[2025-08-04 11:34:11] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"57258","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754300051.415951,"REQUEST_TIME":1754300051}
[2025-08-04 11:34:11] Access granted: via cron key
[2025-08-04 11:34:11] Starting birthday reminders cron job
[2025-08-04 11:34:11] Database connection successful. Found 2 total members.
[2025-08-04 11:45:57] Request received: format=, cron_key=not set
[2025-08-04 11:45:57] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"57777","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754300757.105222,"REQUEST_TIME":1754300757}
[2025-08-04 11:48:12] Request received: format=json, cron_key=fac***
[2025-08-04 11:48:12] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"58096","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754300892.535856,"REQUEST_TIME":1754300892}
[2025-08-04 11:48:12] Access granted: via cron key
[2025-08-04 11:48:12] Starting birthday reminders cron job
[2025-08-04 11:48:12] Database connection successful. Found 2 total members.
[2025-08-04 11:48:31] Request received: format=, cron_key=not set
[2025-08-04 11:48:31] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"58130","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754300911.598615,"REQUEST_TIME":1754300911}
[2025-08-04 11:49:03] Request received: format=, cron_key=not set
[2025-08-04 11:49:03] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"58172","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754300943.827289,"REQUEST_TIME":1754300943}
[2025-08-04 11:50:49] Request received: format=, cron_key=not set
[2025-08-04 11:50:49] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_REFERER":"http:\/\/localhost\/campaign\/church\/admin\/members.php","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"58371","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754301049.473369,"REQUEST_TIME":1754301049}
[2025-08-04 11:50:49] Access granted: via admin referer
[2025-08-04 11:50:49] Starting birthday reminders cron job
[2025-08-04 11:50:49] Database connection successful. Found 2 total members.
[2025-08-04 11:50:49] Uncaught Exception: Call to undefined method BirthdayReminder::sendReminders() in C:\xampp\htdocs\campaign\church\birthday_reminders.php on line 241
[2025-08-04 11:50:56] Request received: format=json, cron_key=fac***
[2025-08-04 11:50:56] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"58437","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754301056.095948,"REQUEST_TIME":1754301056}
[2025-08-04 11:50:56] Access granted: via cron key
[2025-08-04 11:50:56] Starting birthday reminders cron job
[2025-08-04 11:50:56] Database connection successful. Found 2 total members.
[2025-08-04 12:02:39] Request received: format=, cron_key=not set
[2025-08-04 12:02:39] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_REFERER":"http:\/\/localhost\/campaign\/church\/admin\/send_birthday_emails.php","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"59262","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754301759.868247,"REQUEST_TIME":1754301759}
[2025-08-04 12:02:39] Access granted: via admin referer
[2025-08-04 12:02:39] Starting birthday reminders cron job
[2025-08-04 12:02:39] Database connection successful. Found 2 total members.
[2025-08-04 12:02:39] Uncaught Exception: Call to undefined method BirthdayReminder::sendReminders() in C:\xampp\htdocs\campaign\church\birthday_reminders.php on line 241
[2025-08-04 12:03:17] Request received: format=json, cron_key=fac***
[2025-08-04 12:03:17] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"59371","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754301797.112414,"REQUEST_TIME":1754301797}
[2025-08-04 12:03:17] Access granted: via cron key
[2025-08-04 12:03:17] Starting birthday reminders cron job
[2025-08-04 12:03:17] Database connection successful. Found 2 total members.
[2025-08-04 12:05:55] Request received: format=json, cron_key=fac***
[2025-08-04 12:05:55] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"59501","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754301955.084703,"REQUEST_TIME":1754301955}
[2025-08-04 12:05:55] Access granted: via cron key
[2025-08-04 12:05:55] Starting birthday reminders cron job
[2025-08-04 12:05:55] Database connection successful. Found 2 total members.
[2025-08-04 12:07:29] Request received: format=json, cron_key=fac***
[2025-08-04 12:07:29] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"59574","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302049.717862,"REQUEST_TIME":1754302049}
[2025-08-04 12:07:29] Access granted: via cron key
[2025-08-04 12:07:29] Starting birthday reminders cron job
[2025-08-04 12:07:29] Database connection successful. Found 2 total members.
[2025-08-04 12:12:27] Request received: format=json, cron_key=fac***
[2025-08-04 12:12:27] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60004","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302347.756297,"REQUEST_TIME":1754302347}
[2025-08-04 12:12:27] Access granted: via cron key
[2025-08-04 12:12:27] Starting birthday reminders cron job
[2025-08-04 12:12:27] Database connection successful. Found 2 total members.
[2025-08-04 12:12:49] Request received: format=json, cron_key=fac***
[2025-08-04 12:12:49] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60055","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302369.656964,"REQUEST_TIME":1754302369}
[2025-08-04 12:12:49] Access granted: via cron key
[2025-08-04 12:12:49] Starting birthday reminders cron job
[2025-08-04 12:12:49] Database connection successful. Found 2 total members.
[2025-08-04 12:17:00] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:00] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60339","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302620.106813,"REQUEST_TIME":1754302620}
[2025-08-04 12:17:00] Access granted: via cron key
[2025-08-04 12:17:00] Starting birthday reminders cron job
[2025-08-04 12:17:00] Database connection successful. Found 2 total members.
[2025-08-04 12:17:27] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:27] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60395","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302647.20849,"REQUEST_TIME":1754302647}
[2025-08-04 12:17:27] Access granted: via cron key
[2025-08-04 12:17:27] Starting birthday reminders cron job
[2025-08-04 12:17:27] Database connection successful. Found 2 total members.
[2025-08-04 12:17:34] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:34] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60434","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302654.542506,"REQUEST_TIME":1754302654}
[2025-08-04 12:17:34] Access granted: via cron key
[2025-08-04 12:17:34] Starting birthday reminders cron job
[2025-08-04 12:17:34] Database connection successful. Found 2 total members.
[2025-08-04 12:17:37] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:37] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60465","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302657.722807,"REQUEST_TIME":1754302657}
[2025-08-04 12:17:37] Access granted: via cron key
[2025-08-04 12:17:37] Starting birthday reminders cron job
[2025-08-04 12:17:37] Database connection successful. Found 2 total members.
[2025-08-04 12:17:41] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:41] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60496","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302661.531026,"REQUEST_TIME":1754302661}
[2025-08-04 12:17:41] Access granted: via cron key
[2025-08-04 12:17:41] Starting birthday reminders cron job
[2025-08-04 12:17:41] Database connection successful. Found 2 total members.
[2025-08-04 12:17:48] Request received: format=json, cron_key=fac***
[2025-08-04 12:17:48] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"60535","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754302668.767461,"REQUEST_TIME":1754302668}
[2025-08-04 12:17:48] Access granted: via cron key
[2025-08-04 12:17:48] Starting birthday reminders cron job
[2025-08-04 12:17:48] Database connection successful. Found 2 total members.
[2025-08-04 12:24:39] Request received: format=, cron_key=not set
[2025-08-04 12:24:39] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_REFERER":"http:\/\/localhost\/campaign\/church\/admin\/send_birthday_emails.php","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"60921","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754303079.92022,"REQUEST_TIME":1754303079}
[2025-08-04 12:24:39] Access granted: via admin referer
[2025-08-04 12:24:39] Starting birthday reminders cron job
[2025-08-04 12:24:39] Database connection successful. Found 2 total members.
[2025-08-04 12:24:39] Uncaught Exception: Call to undefined method BirthdayReminder::sendReminders() in C:\xampp\htdocs\campaign\church\birthday_reminders.php on line 241
[2025-08-04 12:26:56] Request received: format=, cron_key=not set
[2025-08-04 12:26:56] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"same-origin","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_REFERER":"http:\/\/localhost\/campaign\/church\/admin\/email_templates.php","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"61020","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754303215.993351,"REQUEST_TIME":1754303215}
[2025-08-04 12:26:56] Access granted: via admin referer
[2025-08-04 12:26:56] Starting birthday reminders cron job
[2025-08-04 12:26:56] Database connection successful. Found 2 total members.
[2025-08-04 12:26:56] Uncaught Exception: Call to undefined method BirthdayReminder::sendReminders() in C:\xampp\htdocs\campaign\church\birthday_reminders.php on line 241
[2025-08-04 12:29:06] Request received: format=json, cron_key=fac***
[2025-08-04 12:29:06] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61242","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303346.790211,"REQUEST_TIME":1754303346}
[2025-08-04 12:29:06] Access granted: via cron key
[2025-08-04 12:29:06] Starting birthday reminders cron job
[2025-08-04 12:29:06] Database connection successful. Found 2 total members.
[2025-08-04 12:30:31] Request received: format=, cron_key=not set
[2025-08-04 12:30:31] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/admin\/send_birthday_notification.php","REMOTE_PORT":"61348","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/admin\/send_birthday_notification.php","SCRIPT_NAME":"\/campaign\/church\/admin\/send_birthday_notification.php","PHP_SELF":"\/campaign\/church\/admin\/send_birthday_notification.php","REQUEST_TIME_FLOAT":1754303431.258528,"REQUEST_TIME":1754303431}
[2025-08-04 12:30:45] Request received: format=json, cron_key=not set
[2025-08-04 12:30:45] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=v4q41loujd1rmns1p6vv6idirr","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61376","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303445.559763,"REQUEST_TIME":1754303445}
[2025-08-04 12:30:45] Access granted: via format=json
[2025-08-04 12:30:45] Starting birthday reminders cron job
[2025-08-04 12:30:45] Database connection successful. Found 2 total members.
[2025-08-04 12:30:53] Request received: format=json, cron_key=not set
[2025-08-04 12:30:53] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"curl\/8.12.1","HTTP_ACCEPT":"*\/*","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61401","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303453.646221,"REQUEST_TIME":1754303453}
[2025-08-04 12:30:53] Access granted: via format=json
[2025-08-04 12:30:53] Starting birthday reminders cron job
[2025-08-04 12:30:53] Database connection successful. Found 2 total members.
[2025-08-04 12:31:03] Request received: format=, cron_key=not set
[2025-08-04 12:31:03] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"curl\/8.12.1","HTTP_ACCEPT":"*\/*","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61418","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303463.584689,"REQUEST_TIME":1754303463}
[2025-08-04 12:32:24] Request received: format=json, cron_key=fac***
[2025-08-04 12:32:24] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61563","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303544.002888,"REQUEST_TIME":1754303544}
[2025-08-04 12:32:24] Access granted: via cron key
[2025-08-04 12:32:24] Starting birthday reminders cron job
[2025-08-04 12:32:24] Database connection successful. Found 2 total members.
[2025-08-04 12:32:33] Request received: format=json, cron_key=fac***
[2025-08-04 12:32:33] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"curl\/8.12.1","HTTP_ACCEPT":"*\/*","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"61579","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754303553.3597,"REQUEST_TIME":1754303553}
[2025-08-04 12:32:33] Access granted: via cron key
[2025-08-04 12:32:33] Starting birthday reminders cron job
[2025-08-04 12:32:33] Database connection successful. Found 2 total members.
[2025-08-04 12:40:33] Request received: format=json, cron_key=fac***
[2025-08-04 12:40:33] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"62067","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754304033.600351,"REQUEST_TIME":1754304033}
[2025-08-04 12:40:33] Access granted: via cron key
[2025-08-04 12:40:33] Starting birthday reminders cron job
[2025-08-04 12:40:33] Database connection successful. Found 2 total members.
[2025-08-04 12:42:18] Request received: format=json, cron_key=fac***
[2025-08-04 12:42:18] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"62259","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754304138.821152,"REQUEST_TIME":1754304138}
[2025-08-04 12:42:18] Access granted: via cron key
[2025-08-04 12:42:18] Starting birthday reminders cron job
[2025-08-04 12:42:18] Database connection successful. Found 2 total members.
[2025-08-04 12:44:43] Request received: format=json, cron_key=fac***
[2025-08-04 12:44:43] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"62498","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754304283.075654,"REQUEST_TIME":1754304283}
[2025-08-04 12:44:43] Access granted: via cron key
[2025-08-04 12:44:43] Starting birthday reminders cron job
[2025-08-04 12:44:43] Database connection successful. Found 2 total members.
[2025-08-04 12:47:07] Request received: format=json, cron_key=fac***
[2025-08-04 12:47:07] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"62724","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754304427.425295,"REQUEST_TIME":1754304427}
[2025-08-04 12:47:07] Access granted: via cron key
[2025-08-04 12:47:07] Starting birthday reminders cron job
[2025-08-04 12:47:07] Database connection successful. Found 2 total members.
[2025-08-04 12:47:35] Request received: format=json, cron_key=fac***
[2025-08-04 12:47:35] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"62818","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754304455.627933,"REQUEST_TIME":1754304455}
[2025-08-04 12:47:35] Access granted: via cron key
[2025-08-04 12:47:35] Starting birthday reminders cron job
[2025-08-04 12:47:35] Database connection successful. Found 2 total members.
[2025-08-04 13:46:41] Request received: format=json, cron_key=fac***
[2025-08-04 13:46:41] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"64315","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754308001.326945,"REQUEST_TIME":1754308001}
[2025-08-04 13:46:41] Access granted: via cron key
[2025-08-04 13:46:41] Starting birthday reminders cron job
[2025-08-04 13:46:41] Database connection successful. Found 2 total members.
[2025-08-04 13:47:44] Request received: format=json, cron_key=fac***
[2025-08-04 13:47:44] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"64388","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754308064.639868,"REQUEST_TIME":1754308064}
[2025-08-04 13:47:44] Access granted: via cron key
[2025-08-04 13:47:44] Starting birthday reminders cron job
[2025-08-04 13:47:44] Database connection successful. Found 2 total members.
[2025-08-04 14:25:43] Request received: format=json, cron_key=fac***
[2025-08-04 14:25:43] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"50249","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754310343.370687,"REQUEST_TIME":1754310343}
[2025-08-04 14:25:43] Access granted: via cron key
[2025-08-04 14:25:43] Starting birthday reminders cron job
[2025-08-04 14:25:43] Database connection successful. Found 2 total members.
[2025-08-04 14:54:57] Request received: format=json, cron_key=fac***
[2025-08-04 14:54:57] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"51858","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754312097.230429,"REQUEST_TIME":1754312097}
[2025-08-04 14:54:57] Access granted: via cron key
[2025-08-04 14:54:57] Starting birthday reminders cron job
[2025-08-04 14:54:57] Database connection successful. Found 2 total members.
[2025-08-04 15:30:07] Request received: format=json, cron_key=fac***
[2025-08-04 15:30:07] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53246","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754314207.727233,"REQUEST_TIME":1754314207}
[2025-08-04 15:30:07] Access granted: via cron key
[2025-08-04 15:30:07] Starting birthday reminders cron job
[2025-08-04 15:30:07] Database connection successful. Found 2 total members.
[2025-08-04 15:31:17] Request received: format=json, cron_key=fac***
[2025-08-04 15:31:17] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"53358","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1754314277.042088,"REQUEST_TIME":1754314277}
[2025-08-04 15:31:17] Access granted: via cron key
[2025-08-04 15:31:17] Starting birthday reminders cron job
[2025-08-04 15:31:17] Database connection successful. Found 2 total members.
