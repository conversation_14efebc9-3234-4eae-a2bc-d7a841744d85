<?php
/**
 * Mobile Navigation Component - Safe Version
 * Provides responsive mobile/tablet navigation completely separate from desktop sidebar
 * This version includes comprehensive error handling and fallbacks
 */

// Safe function to check if a function exists and call it with fallback
function safe_call($function_name, $args = [], $fallback = '') {
    if (function_exists($function_name)) {
        return call_user_func_array($function_name, $args);
    }
    return $fallback;
}

// Safe function to check page access
function safe_can_access($page) {
    if (function_exists('canAccessPage')) {
        return canAccessPage($page);
    }
    return true; // Fallback: allow access if RBAC not available
}

// Safe admin URL builder
function safe_admin_url($page) {
    if (function_exists('admin_url_for')) {
        return admin_url_for($page);
    }
    if (defined('ADMIN_URL')) {
        return ADMIN_URL . '/' . $page;
    }
    return $page;
}

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Get safe values
$admin_title = safe_call('get_admin_title', [], 'Admin Panel');
$member_term = safe_call('get_member_term', [false], 'Member');
$member_term_plural = safe_call('get_member_term', [true], 'Members');
$event_term = safe_call('get_event_term', [false], 'Event');
$event_term_plural = safe_call('get_event_term', [true], 'Events');
?>

<!-- Mobile Navigation - Only visible on mobile/tablet -->
<div class="mobile-navigation d-lg-none">
    <!-- Mobile Header Bar -->
    <div class="mobile-nav-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-auto">
                    <button class="btn btn-link mobile-menu-toggle" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNavDrawer" aria-controls="mobileNavDrawer">
                        <i class="bi bi-list fs-4 text-white"></i>
                    </button>
                </div>
                <div class="col">
                    <h6 class="mobile-nav-title mb-0 text-white"><?php echo htmlspecialchars($admin_title); ?></h6>
                </div>
                <div class="col-auto">
                    <!-- Quick QR Scanner Access -->
                    <a href="<?php echo safe_admin_url('staff_qr_scanner.php'); ?>" class="btn btn-link text-white" title="QR Scanner">
                        <i class="bi bi-qr-code-scan fs-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Drawer -->
    <div class="offcanvas offcanvas-start mobile-nav-drawer" tabindex="-1" id="mobileNavDrawer" aria-labelledby="mobileNavDrawerLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileNavDrawerLabel">
                <i class="bi bi-grid-3x3-gap-fill me-2"></i>
                <?php echo htmlspecialchars($admin_title); ?>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Quick Actions Section -->
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-lightning-charge-fill me-2"></i>Quick Actions
                </h6>
                <div class="mobile-nav-items">
                    <a href="<?php echo safe_admin_url('dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="<?php echo safe_admin_url('staff_qr_scanner.php'); ?>" class="mobile-nav-item">
                        <i class="bi bi-qr-code-scan"></i>
                        <span>QR Scanner</span>
                    </a>
                    <a href="<?php echo safe_admin_url('mobile_checkin.php'); ?>" class="mobile-nav-item">
                        <i class="bi bi-check-circle"></i>
                        <span>Mobile Check-in</span>
                    </a>
                    <?php if (safe_can_access('pwa/index.php')): ?>
                    <a href="<?php echo safe_admin_url('pwa/'); ?>" class="mobile-nav-item" target="_blank">
                        <i class="bi bi-phone"></i>
                        <span>Mobile PWA</span>
                        <span class="badge bg-success ms-auto">NEW</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Member Management Section -->
            <?php if (safe_can_access('members.php') || safe_can_access('add_member.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-people-fill me-2"></i><?php echo htmlspecialchars($member_term_plural); ?>
                </h6>
                <div class="mobile-nav-items">
                    <?php if (safe_can_access('members.php')): ?>
                    <a href="<?php echo safe_admin_url('members.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'members.php' ? 'active' : ''; ?>">
                        <i class="bi bi-people"></i>
                        <span>View <?php echo htmlspecialchars($member_term_plural); ?></span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('add_member.php')): ?>
                    <a href="<?php echo safe_admin_url('add_member.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'add_member.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-plus"></i>
                        <span>Add <?php echo htmlspecialchars($member_term); ?></span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('family_management.php')): ?>
                    <a href="<?php echo safe_admin_url('family_management.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'family_management.php' ? 'active' : ''; ?>">
                        <i class="bi bi-house-heart"></i>
                        <span>Family Management</span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('requests.php')): ?>
                    <a href="<?php echo safe_admin_url('requests.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'requests.php' ? 'active' : ''; ?>">
                        <i class="bi bi-chat-heart"></i>
                        <span>Requests</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Event Management Section -->
            <?php if (safe_can_access('events.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-calendar-event-fill me-2"></i><?php echo htmlspecialchars($event_term_plural); ?>
                </h6>
                <div class="mobile-nav-items">
                    <?php if (safe_can_access('events.php')): ?>
                    <a href="<?php echo safe_admin_url('events.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'events.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar-event"></i>
                        <span>View <?php echo htmlspecialchars($event_term_plural); ?></span>
                    </a>
                    <?php endif; ?>

                    <?php if (safe_can_access('event_attendance.php')): ?>
                    <a href="<?php echo safe_admin_url('event_attendance.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_attendance.php' ? 'active' : ''; ?>">
                        <i class="bi bi-check2-square"></i>
                        <span>Attendance</span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('realtime_dashboard.php')): ?>
                    <a href="<?php echo safe_admin_url('realtime_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'realtime_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-broadcast"></i>
                        <span>Live Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Communications Section -->
            <?php if (safe_can_access('email_templates.php') || safe_can_access('bulk_email.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-envelope-fill me-2"></i>Communications
                </h6>
                <div class="mobile-nav-items">
                    <?php if (safe_can_access('email_templates.php')): ?>
                    <a href="<?php echo safe_admin_url('email_templates.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'email_templates.php' ? 'active' : ''; ?>">
                        <i class="bi bi-file-earmark-text"></i>
                        <span>Email Templates</span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('bulk_email.php')): ?>
                    <a href="<?php echo safe_admin_url('bulk_email.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'bulk_email.php' ? 'active' : ''; ?>">
                        <i class="bi bi-send"></i>
                        <span>Send Emails</span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('birthday.php')): ?>
                    <a href="<?php echo safe_admin_url('birthday.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'birthday.php' ? 'active' : ''; ?>">
                        <i class="bi bi-gift"></i>
                        <span>Birthday Messages</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Analytics Section -->
            <?php if (safe_can_access('universal_ai_dashboard.php') || safe_can_access('universal_analytics_dashboard.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-graph-up me-2"></i>Analytics
                </h6>
                <div class="mobile-nav-items">
                    <?php if (safe_can_access('universal_ai_dashboard.php')): ?>
                    <a href="<?php echo safe_admin_url('universal_ai_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_ai_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-robot"></i>
                        <span>AI Predictions</span>
                    </a>
                    <?php endif; ?>
                    <?php if (safe_can_access('universal_analytics_dashboard.php')): ?>
                    <a href="<?php echo safe_admin_url('universal_analytics_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_analytics_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-bar-chart"></i>
                        <span>Analytics Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Settings Section -->
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-gear-fill me-2"></i>Settings
                </h6>
                <div class="mobile-nav-items">
                    <a href="<?php echo safe_admin_url('profile.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'profile.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-circle"></i>
                        <span>My Profile</span>
                    </a>
                    <?php if (safe_can_access('universal_organization_setup.php')): ?>
                    <a href="<?php echo safe_admin_url('universal_organization_setup.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_organization_setup.php' ? 'active' : ''; ?>">
                        <i class="bi bi-building"></i>
                        <span>Organization Setup</span>
                    </a>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation (phones only) -->
    <div class="mobile-bottom-nav d-md-none">
        <div class="container-fluid">
            <div class="row text-center">
                <div class="col">
                    <a href="<?php echo safe_admin_url('dashboard.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo safe_admin_url('staff_qr_scanner.php'); ?>" class="mobile-bottom-nav-item">
                        <i class="bi bi-qr-code-scan"></i>
                        <span>Scanner</span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo safe_admin_url('members.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'members.php' ? 'active' : ''; ?>">
                        <i class="bi bi-people"></i>
                        <span><?php echo htmlspecialchars($member_term_plural); ?></span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo safe_admin_url('events.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'events.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar-event"></i>
                        <span><?php echo htmlspecialchars($event_term_plural); ?></span>
                    </a>
                </div>
                <div class="col">
                    <button class="mobile-bottom-nav-item" data-bs-toggle="offcanvas" data-bs-target="#mobileNavDrawer">
                        <i class="bi bi-grid-3x3-gap"></i>
                        <span>More</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
