<?php
/**
 * Role-Based Access Control (RBAC) System
 * Critical security implementation for dashboard access control
 */

class RBACAccessControl {
    private $pdo;
    private $user_id;
    private $user_roles = [];
    private $user_permissions = [];
    
    // Define role hierarchy (lower number = higher privilege)
    private $role_hierarchy = [
        'super_admin' => 1,
        'limited_admin' => 2,
        'event_coordinator' => 3,
        'session_moderator' => 4,
        'staff' => 5
    ];
    
    // Define page access permissions
    private $page_permissions = [
        // Super Admin Only Pages
        'setup_rbac_system.php' => ['super_admin'],
        'create_admin_users.php' => ['super_admin'],
        'manage_user_permissions.php' => ['super_admin'],
        'setup_granular_permissions_system.php' => ['super_admin'],
        'system_test_dashboard.php' => ['super_admin'],
        'super_admin_dashboard.php' => ['super_admin'],
        'super_admin_navigation.php' => ['super_admin'],
        'initialize_rbac_database.php' => ['super_admin'],
        'test_super_admin.php' => ['super_admin'],

        // Admin Pages (Super Admin + Limited Admin + Event Staff)
        'dashboard.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'],
        'assignment_dashboard.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'],
        'notifications.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'session_moderator', 'staff'],
        'members.php' => ['super_admin', 'limited_admin'],
        'add_member.php' => ['super_admin', 'limited_admin'],
        'edit_member.php' => ['super_admin', 'limited_admin'],
        'view_member.php' => ['super_admin', 'limited_admin'],
        'settings.php' => ['super_admin', 'limited_admin'],
        'profile.php' => ['super_admin', 'limited_admin'],

        // Member Management (Super Admin + Limited Admin)
        'volunteer_opportunities.php' => ['super_admin', 'limited_admin'],
        'requests.php' => ['super_admin', 'limited_admin'],
        'family_management.php' => ['super_admin', 'limited_admin'],
        'member_skills.php' => ['super_admin', 'limited_admin'],
        
        // Event Management (Super Admin + Event Coordinators + Organizers)
        'events.php' => ['super_admin', 'event_coordinator', 'organizer'],
        'event_sessions.php' => ['super_admin', 'event_coordinator', 'organizer'],
        'event_attendance.php' => ['super_admin', 'event_coordinator', 'organizer'],
        'event_coordinator_dashboard.php' => ['super_admin', 'event_coordinator'],
        'event_categories.php' => ['super_admin', 'event_coordinator'],
        'event_reports.php' => ['super_admin', 'event_coordinator', 'organizer'],

        // Universal Platform (All Admin Roles)
        'universal_ai_dashboard.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator'],
        'universal_analytics_dashboard.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator'],
        'universal_organization_setup.php' => ['super_admin', 'limited_admin'],
        'realtime_dashboard.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator'],

        // PWA and Mobile Access
        'pwa/index.php' => ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator'],

        // Session Management (Super Admin + Session Moderators)
        'session_moderator_dashboard.php' => ['super_admin', 'session_moderator'],
        'session_attendance.php' => ['super_admin', 'session_moderator'],
        
        // Staff Functions
        'staff_dashboard.php' => ['super_admin', 'staff'],

        // Email Management (Super Admin only for most, Limited Admin for basic)
        'bulk_email.php' => ['super_admin', 'limited_admin'],
        'email_scheduler.php' => ['super_admin', 'limited_admin'],
        'contacts.php' => ['super_admin', 'limited_admin'],
        'contact_groups.php' => ['super_admin', 'limited_admin'],
        'birthday.php' => ['super_admin', 'limited_admin'],
        'email_templates.php' => ['super_admin', 'limited_admin'],
        'email_analytics.php' => ['super_admin', 'limited_admin'],

        // SMS Management (Super Admin only)
        'bulk_sms.php' => ['super_admin'],
        'single_sms.php' => ['super_admin'],
        'sms_templates.php' => ['super_admin'],
        'sms_analytics.php' => ['super_admin'],
        'whatsapp_messages.php' => ['super_admin'],

        // Appearance & Settings (Super Admin only for advanced, Limited Admin for basic)
        'appearance_settings.php' => ['super_admin'],
        'branding_settings.php' => ['super_admin'],
        'logo_management_consolidated.php' => ['super_admin'],

        // Donations & Finance (Super Admin only)
        'donations.php' => ['super_admin'],
        'gift_management.php' => ['super_admin'],
        'payment_integration.php' => ['super_admin'],
        'enhanced_donate.php' => ['super_admin'],
        'setup_enhanced_donations.php' => ['super_admin'],
        'setup_birthday_gift_integration.php' => ['super_admin'],
        'check_payment_sdks.php' => ['super_admin'],
        'payment_tables.php' => ['super_admin'],

        // Additional Email Management Pages
        'send_birthday_emails.php' => ['super_admin', 'limited_admin'],
        'test_birthday_email.php' => ['super_admin', 'limited_admin'],
        'send_birthday_notification.php' => ['super_admin', 'limited_admin'],
        'automated_email_templates.php' => ['super_admin', 'limited_admin'],

        // Additional Integration Pages
        'calendar_integration.php' => ['super_admin'],
        'social_media_integration.php' => ['super_admin'],

        // Additional Account Pages
        'custom_fields.php' => ['super_admin'],
        'security_audit.php' => ['super_admin'],
        'security_settings.php' => ['super_admin'],
        'backup_management.php' => ['super_admin'],

        // Public/Common Pages (accessible to all authenticated users)
        'login.php' => ['public'],
        'logout.php' => ['public'],
        'forgot_password.php' => ['public'],
        'reset_password.php' => ['public']
    ];
    
    public function __construct($pdo, $user_id = null) {
        $this->pdo = $pdo;
        $this->user_id = $user_id ?? $_SESSION['admin_id'] ?? null;
        
        if ($this->user_id) {
            $this->loadUserRoles();
        }
    }
    
    /**
     * Load user roles and permissions
     */
    private function loadUserRoles() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ur.role_name, ur.hierarchy_level, ur.role_display_name
                FROM user_role_assignments ura
                JOIN user_roles ur ON ura.role_id = ur.id
                WHERE ura.user_id = ? AND ura.is_active = 1
                AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                ORDER BY ur.hierarchy_level ASC
            ");
            $stmt->execute([$this->user_id]);
            $this->user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("RBAC Error loading user roles: " . $e->getMessage());
            $this->user_roles = [];
        }
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role_name) {
        foreach ($this->user_roles as $role) {
            if ($role['role_name'] === $role_name) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get user's highest privilege role
     */
    public function getPrimaryRole() {
        if (empty($this->user_roles)) {
            return null;
        }
        
        // Return role with lowest hierarchy level (highest privilege)
        $primary_role = $this->user_roles[0];
        return $primary_role['role_name'];
    }
    
    /**
     * Check if user can access a specific page (Enhanced with granular permissions)
     */
    public function canAccessPage($page_name) {
        // Normalize the page name for lookup
        $original_page_name = $page_name;
        $basename_page_name = basename($page_name);

        // SUPER ADMIN BYPASS: Super admins have access to ALL pages
        if ($this->isSuperAdmin()) {
            return true;
        }

        // First check individual granular permissions
        if ($this->hasIndividualPermissionForPage($basename_page_name)) {
            return true;
        }

        // Try to find permissions using original path first, then basename
        $permissions_key = null;
        if (isset($this->page_permissions[$original_page_name])) {
            $permissions_key = $original_page_name;
        } elseif (isset($this->page_permissions[$basename_page_name])) {
            $permissions_key = $basename_page_name;
        }

        // Fallback to role-based permissions
        if ($permissions_key === null) {
            return false;
        }

        $required_roles = $this->page_permissions[$permissions_key];

        // Public pages are accessible to all
        if (in_array('public', $required_roles)) {
            return true;
        }

        // Check if user has any of the required roles
        foreach ($this->user_roles as $user_role) {
            if (in_array($user_role['role_name'], $required_roles)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if user has individual permission for a specific page
     */
    private function hasIndividualPermissionForPage($page_name) {
        // SUPER ADMIN BYPASS: Super admins have all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        try {
            // First check if granular permissions tables exist
            if (!$this->granularTablesExist()) {
                return false; // Fall back to legacy system
            }

            $stmt = $this->pdo->prepare("
                SELECT COUNT(*)
                FROM user_individual_permissions uip
                JOIN granular_permissions gp ON uip.permission_id = gp.id
                WHERE uip.user_id = ?
                AND gp.page_file = ?
                AND uip.is_active = 1
                AND gp.is_active = 1
            ");
            $stmt->execute([$this->user_id, $page_name]);

            return $stmt->fetchColumn() > 0;

        } catch (Exception $e) {
            error_log("Error checking individual page permission: " . $e->getMessage());
            return false; // Fall back to legacy system
        }
    }

    /**
     * Check if granular permission tables exist
     */
    private function granularTablesExist() {
        try {
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'granular_permissions'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Check if user has a specific granular permission
     */
    public function hasPermission($permission_key) {
        // SUPER ADMIN BYPASS: Super admins have all permissions
        if ($this->isSuperAdmin()) {
            return true;
        }

        try {
            // First check if granular permissions tables exist
            if (!$this->granularTablesExist()) {
                return false; // Fall back to legacy system
            }

            $stmt = $this->pdo->prepare("
                SELECT COUNT(*)
                FROM user_individual_permissions uip
                JOIN granular_permissions gp ON uip.permission_id = gp.id
                WHERE uip.user_id = ?
                AND gp.permission_key = ?
                AND uip.is_active = 1
                AND gp.is_active = 1
            ");
            $stmt->execute([$this->user_id, $permission_key]);

            return $stmt->fetchColumn() > 0;

        } catch (Exception $e) {
            error_log("Error checking granular permission: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all individual permissions for the current user
     */
    public function getUserIndividualPermissions() {
        try {
            // First check if granular permissions tables exist
            if (!$this->granularTablesExist()) {
                return []; // Return empty array if tables don't exist
            }

            $stmt = $this->pdo->prepare("
                SELECT gp.permission_key, gp.permission_name, pc.category_display_name
                FROM user_individual_permissions uip
                JOIN granular_permissions gp ON uip.permission_id = gp.id
                JOIN permission_categories pc ON gp.category_id = pc.id
                WHERE uip.user_id = ?
                AND uip.is_active = 1
                AND gp.is_active = 1
                ORDER BY pc.sort_order, gp.sort_order
            ");
            $stmt->execute([$this->user_id]);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log("Error getting user individual permissions: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Enforce page access control
     */
    public function enforcePageAccess($page_name = null) {
        if (!$page_name) {
            $page_name = basename($_SERVER['PHP_SELF']);
        }
        
        if (!$this->canAccessPage($page_name)) {
            $this->redirectToAccessDenied();
        }
    }
    
    /**
     * Get appropriate dashboard for user's role
     */
    public function getDefaultDashboard() {
        $primary_role = $this->getPrimaryRole();

        switch ($primary_role) {
            case 'super_admin':
                return 'dashboard.php'; // Redirect super admin to original dashboard
            case 'limited_admin':
                return 'dashboard.php';
            case 'event_coordinator':
                return 'event_coordinator_dashboard.php';
            case 'organizer':
                return 'assignment_dashboard.php'; // Organizers see their assigned events
            case 'session_moderator':
                return 'session_moderator_dashboard.php';
            case 'staff':
                return 'staff_dashboard.php';
            default:
                return 'access_denied.php';
        }
    }
    
    /**
     * Get sidebar navigation items based on user role
     */
    public function getSidebarItems() {
        $primary_role = $this->getPrimaryRole();
        
        switch ($primary_role) {
            case 'super_admin':
                return $this->getSuperAdminSidebar();
            case 'limited_admin':
                return $this->getLimitedAdminSidebar();
            case 'event_coordinator':
                return $this->getEventCoordinatorSidebar();
            case 'session_moderator':
                return $this->getSessionModeratorSidebar();
            case 'staff':
                return $this->getStaffSidebar();
            default:
                return [];
        }
    }
    
    /**
     * Super Admin - Full Access Sidebar
     */
    private function getSuperAdminSidebar() {
        return [
            'dashboard' => [
                'title' => 'Dashboard',
                'url' => 'dashboard.php',
                'icon' => 'bi-speedometer2'
            ],
            'members' => [
                'title' => 'Member Management',
                'icon' => 'fas fa-users',
                'items' => [
                    ['title' => 'Members', 'url' => 'members.php', 'icon' => 'fas fa-users'],
                    ['title' => 'Add Member', 'url' => 'add_member.php', 'icon' => 'fas fa-user-plus'],
                    ['title' => 'Volunteer Opportunities', 'url' => 'volunteer_opportunities.php', 'icon' => 'fas fa-hands-helping'],
                    ['title' => 'Requests', 'url' => 'requests.php', 'icon' => 'fas fa-heart'],
                    ['title' => 'Family Management', 'url' => 'family_management.php', 'icon' => 'fas fa-users'],
                    ['title' => 'Member Skills', 'url' => 'member_skills.php', 'icon' => 'fas fa-tools'],
                ]
            ],
            'events' => [
                'title' => 'Events Management',
                'icon' => 'fas fa-calendar-alt',
                'items' => [
                    ['title' => 'Events', 'url' => 'events.php', 'icon' => 'fas fa-calendar-alt'],
                    ['title' => 'Event Attendance', 'url' => 'event_attendance.php', 'icon' => 'fas fa-calendar-check'],
                    ['title' => 'Event Categories', 'url' => 'event_categories.php', 'icon' => 'bi bi-tags'],
                    ['title' => 'Event Reports', 'url' => 'event_reports.php', 'icon' => 'bi bi-file-earmark-bar-graph'],
                ]
            ],
            'universal_platform' => [
                'title' => 'Universal Platform',
                'icon' => 'bi bi-robot',
                'items' => [
                    ['title' => 'AI Predictions', 'url' => 'universal_ai_dashboard.php', 'icon' => 'bi bi-robot'],
                    ['title' => 'Universal Analytics', 'url' => 'universal_analytics_dashboard.php', 'icon' => 'bi bi-graph-up'],
                    ['title' => 'Real-Time Dashboard', 'url' => 'realtime_dashboard.php', 'icon' => 'bi bi-broadcast'],
                    ['title' => 'Mobile PWA', 'url' => 'pwa/', 'icon' => 'bi bi-phone', 'target' => '_blank'],
                    ['title' => 'Organization Setup', 'url' => 'universal_organization_setup.php', 'icon' => 'bi bi-gear'],
                ]
            ],
            'role_dashboards' => [
                'title' => 'Role Dashboards',
                'icon' => 'bi bi-speedometer2',
                'items' => [
                    ['title' => 'Super Admin', 'url' => 'super_admin_dashboard.php', 'icon' => 'bi bi-speedometer2'],
                    ['title' => 'Event Coordinator', 'url' => 'event_coordinator_dashboard.php', 'icon' => 'bi bi-calendar-event'],
                    ['title' => 'Session Moderator', 'url' => 'session_moderator_dashboard.php', 'icon' => 'bi bi-person-video2'],
                    ['title' => 'Staff Dashboard', 'url' => 'staff_dashboard.php', 'icon' => 'bi bi-person-check'],
                ]
            ],
            'system_admin' => [
                'title' => 'System Administration',
                'icon' => 'bi bi-gear-wide-connected',
                'items' => [
                    ['title' => 'RBAC Management', 'url' => 'setup_rbac_system.php', 'icon' => 'bi bi-shield-check'],
                    ['title' => 'Create Admin Users', 'url' => 'create_admin_users.php', 'icon' => 'bi bi-person-plus'],
                    ['title' => 'System Testing', 'url' => 'system_test_dashboard.php', 'icon' => 'bi bi-bug'],
                    ['title' => 'Navigation Guide', 'url' => 'super_admin_navigation.php', 'icon' => 'bi bi-compass'],
                ]
            ],
            'email_management' => [
                'title' => 'Email Management',
                'icon' => 'bi bi-envelope-fill',
                'items' => [
                    ['title' => 'Bulk Email', 'url' => 'bulk_email.php', 'icon' => 'bi bi-envelope-fill'],
                    ['title' => 'Email Scheduler', 'url' => 'email_scheduler.php', 'icon' => 'bi bi-calendar-event'],
                    ['title' => 'Contacts', 'url' => 'contacts.php', 'icon' => 'bi bi-person-lines-fill'],
                    ['title' => 'Contact Groups', 'url' => 'contact_groups.php', 'icon' => 'bi bi-folder'],
                    ['title' => 'Birthday Messages', 'url' => 'birthday.php', 'icon' => 'bi bi-gift'],
                    ['title' => 'Send Birthday Emails', 'url' => 'send_birthday_emails.php', 'icon' => 'bi bi-envelope-paper'],
                    ['title' => 'Birthday Notifications', 'url' => 'send_birthday_notification.php', 'icon' => 'bi bi-bell'],
                    ['title' => 'Email Templates', 'url' => 'email_templates.php', 'icon' => 'bi bi-envelope'],
                    ['title' => 'Automated Templates', 'url' => 'automated_email_templates.php', 'icon' => 'bi bi-clock'],
                    ['title' => 'WhatsApp Templates', 'url' => 'whatsapp_templates.php', 'icon' => 'bi bi-whatsapp'],
                    ['title' => 'WhatsApp Messages', 'url' => 'whatsapp_messages.php', 'icon' => 'bi bi-whatsapp'],
                    ['title' => 'Email Analytics', 'url' => 'email_analytics.php', 'icon' => 'bi bi-graph-up'],
                    ['title' => 'About Shortcodes', 'url' => 'about_shortcodes.php', 'icon' => 'bi bi-info-circle'],
                ]
            ],
            'sms_management' => [
                'title' => 'SMS Management',
                'icon' => 'bi bi-chat-text-fill',
                'items' => [
                    ['title' => 'Single SMS', 'url' => 'single_sms.php', 'icon' => 'bi bi-chat-text'],
                    ['title' => 'Bulk SMS', 'url' => 'bulk_sms.php', 'icon' => 'bi bi-chat-text-fill'],
                    ['title' => 'SMS Campaigns', 'url' => 'sms_campaigns.php', 'icon' => 'bi bi-megaphone'],
                    ['title' => 'SMS Templates', 'url' => 'sms_templates.php', 'icon' => 'bi bi-file-text'],
                    ['title' => 'SMS Analytics', 'url' => 'sms_analytics.php', 'icon' => 'bi bi-graph-up'],
                ]
            ],
            'integrations' => [
                'title' => 'Integrations',
                'icon' => 'bi bi-puzzle-fill',
                'items' => [
                    ['title' => 'Calendar Integration', 'url' => 'calendar_integration.php', 'icon' => 'bi bi-calendar-check'],
                    ['title' => 'Social Media', 'url' => 'social_media_integration.php', 'icon' => 'bi bi-share'],
                    ['title' => 'SMS Integration', 'url' => 'sms_integration.php', 'icon' => 'bi bi-chat-dots'],
                    ['title' => 'Payment Integration', 'url' => 'payment_integration.php', 'icon' => 'bi bi-credit-card'],
                    ['title' => 'Donations', 'url' => 'donations.php', 'icon' => 'bi bi-cash-coin'],
                    ['title' => 'Gift Management', 'url' => 'gift_management.php', 'icon' => 'bi bi-gift-fill'],
                    ['title' => 'Send Gift to Member', 'url' => 'send_gift_to_user.php', 'icon' => 'bi bi-send-fill'],
                    ['title' => 'Enhanced Donations', 'url' => 'enhanced_donate.php', 'icon' => 'bi bi-heart-fill'],
                ]
            ],
            'account' => [
                'title' => 'Account',
                'icon' => 'bi bi-gear-fill',
                'items' => [
                    ['title' => 'Settings', 'url' => 'settings.php', 'icon' => 'bi bi-gear-fill'],
                    ['title' => 'Appearance', 'url' => 'appearance_settings.php', 'icon' => 'bi bi-palette'],
                    ['title' => 'Custom Fields', 'url' => 'custom_fields.php', 'icon' => 'bi bi-ui-checks-grid'],
                    ['title' => 'Logo Management', 'url' => 'logo_management_consolidated.php', 'icon' => 'bi bi-image'],
                    ['title' => 'Security Setup', 'url' => 'setup_security.php', 'icon' => 'bi bi-shield-plus'],
                    ['title' => 'Security Audit', 'url' => 'security_audit.php', 'icon' => 'bi bi-shield-lock'],
                    ['title' => 'Security Settings', 'url' => 'security_settings.php', 'icon' => 'bi bi-shield-check'],
                    ['title' => 'Database Backup', 'url' => 'backup_management.php', 'icon' => 'bi bi-hdd-stack'],
                    ['title' => 'My Profile', 'url' => 'profile.php', 'icon' => 'bi bi-person-circle'],
                ]
            ]
        ];
    }
    
    /**
     * Limited Admin - Basic Admin Functions Only
     */
    private function getLimitedAdminSidebar() {
        return [
            'dashboard' => [
                'title' => 'Dashboard',
                'url' => 'dashboard.php',
                'icon' => 'bi-speedometer2'
            ],
            'members' => [
                'title' => 'Member Management',
                'icon' => 'bi-people',
                'items' => [
                    ['title' => 'Members', 'url' => 'members.php', 'icon' => 'bi-people'],
                    ['title' => 'Add Member', 'url' => 'add_member.php', 'icon' => 'bi-person-plus'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Event Coordinator - Event Management Only
     */
    private function getEventCoordinatorSidebar() {
        return [
            'dashboard' => [
                'title' => 'Event Coordinator Dashboard',
                'url' => 'event_coordinator_dashboard.php',
                'icon' => 'bi-calendar-event'
            ],
            'events' => [
                'title' => 'My Events',
                'icon' => 'bi-calendar',
                'items' => [
                    ['title' => 'Assigned Events', 'url' => 'events.php', 'icon' => 'bi-calendar'],
                    ['title' => 'Event Sessions', 'url' => 'event_sessions.php', 'icon' => 'bi-calendar-week'],
                    ['title' => 'Event Attendance', 'url' => 'event_attendance.php', 'icon' => 'bi-check-square'],
                ]
            ],
            'universal_platform' => [
                'title' => 'Universal Platform',
                'icon' => 'bi-robot',
                'items' => [
                    ['title' => 'AI Predictions', 'url' => 'universal_ai_dashboard.php', 'icon' => 'bi-robot'],
                    ['title' => 'Universal Analytics', 'url' => 'universal_analytics_dashboard.php', 'icon' => 'bi-graph-up'],
                    ['title' => 'Real-Time Dashboard', 'url' => 'realtime_dashboard.php', 'icon' => 'bi-broadcast'],
                    ['title' => 'Mobile PWA', 'url' => 'pwa/', 'icon' => 'bi-phone', 'target' => '_blank'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Session Moderator - Session Management Only
     */
    private function getSessionModeratorSidebar() {
        return [
            'dashboard' => [
                'title' => 'Session Moderator Dashboard',
                'url' => 'session_moderator_dashboard.php',
                'icon' => 'bi-person-video2'
            ],
            'sessions' => [
                'title' => 'My Sessions',
                'icon' => 'bi-calendar-week',
                'items' => [
                    ['title' => 'Assigned Sessions', 'url' => 'session_moderator_dashboard.php', 'icon' => 'bi-calendar-week'],
                    ['title' => 'Session Attendance', 'url' => 'session_attendance.php', 'icon' => 'bi-check-square'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Staff - Basic Check-in Only
     */
    private function getStaffSidebar() {
        return [
            'dashboard' => [
                'title' => 'Staff Dashboard',
                'url' => 'staff_dashboard.php',
                'icon' => 'bi-person-check'
            ],
            'checkin' => [
                'title' => 'Check-in Functions',
                'icon' => 'bi-check-circle',
                'items' => [
                    ['title' => 'Member Check-in', 'url' => 'staff_dashboard.php', 'icon' => 'bi-check-circle'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Redirect to access denied page
     */
    private function redirectToAccessDenied() {
        header("Location: access_denied.php?reason=insufficient_permissions");
        exit();
    }
    
    /**
     * Get all user roles for display
     */
    public function getUserRoles() {
        return $this->user_roles;
    }
    
    /**
     * Check if user is super admin
     */
    public function isSuperAdmin() {
        return $this->hasRole('super_admin');
    }
    
    /**
     * Check if user has admin privileges (super_admin or limited_admin)
     */
    public function isAdmin() {
        return $this->hasRole('super_admin') || $this->hasRole('limited_admin');
    }
}

// Global RBAC instance
if (!isset($rbac)) {
    $rbac = new RBACAccessControl($pdo);
}
?>
