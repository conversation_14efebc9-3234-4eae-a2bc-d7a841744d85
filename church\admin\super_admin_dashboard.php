<?php
session_start();

// Include route protection (this will handle login check and role verification)
require_once 'includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

// Include the configuration file and RBAC system
require_once __DIR__ . '/../config.php';
require_once 'includes/rbac_system.php';

// Initialize RBAC system
$rbac = new RBACSystem($pdo);

// Check if RBAC system is initialized
if (!$rbac->isInitialized()) {
    // Redirect to RBAC initialization if not initialized
    header("Location: initialize_rbac_database.php");
    exit();
}

// Require Super Admin role
$rbac->requireRole('super_admin', 'access_denied.php');

// Log dashboard access
$rbac->logDashboardAccess('super_admin_dashboard.php');

$message = '';
$error = '';

// Handle dashboard actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'impersonate_role') {
            $role_name = $_POST['role_name'] ?? '';
            if (!empty($role_name)) {
                $_SESSION['impersonating_role'] = $role_name;
                $message = "Now viewing system as: " . ucfirst(str_replace('_', ' ', $role_name));
            }
        } elseif ($_POST['action'] === 'stop_impersonation') {
            unset($_SESSION['impersonating_role']);
            $message = "Stopped role impersonation. Back to Super Admin view.";
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get comprehensive system statistics
$system_stats = [];

// Total events and sessions
$stmt = $pdo->query("SELECT COUNT(*) FROM events WHERE is_active = 1");
$system_stats['total_events'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM event_sessions WHERE status = 'active'");
$system_stats['total_sessions'] = $stmt->fetchColumn();

// Total users and role assignments
$stmt = $pdo->query("SELECT COUNT(*) FROM admins");
$system_stats['total_users'] = $stmt->fetchColumn();

$stmt = $pdo->query("SELECT COUNT(*) FROM user_role_assignments WHERE is_active = 1");
$system_stats['active_role_assignments'] = $stmt->fetchColumn();

// Recent dashboard access
$stmt = $pdo->prepare("
    SELECT COUNT(*) FROM dashboard_access_log
    WHERE access_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
");
$stmt->execute();
$system_stats['recent_dashboard_access'] = $stmt->fetchColumn();

// Event and session statistics
$stmt = $pdo->query("
    SELECT 
        COUNT(*) as total_rsvps,
        COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as total_attended,
        COUNT(CASE WHEN actually_attended = 0 THEN 1 END) as total_no_show
    FROM (
        SELECT actually_attended FROM event_rsvps WHERE status = 'attending'
        UNION ALL
        SELECT actually_attended FROM event_rsvps_guests WHERE status = 'attending'
    ) combined_rsvps
");
$attendance_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all events with session counts
$stmt = $pdo->query("
    SELECT 
        e.*,
        COUNT(es.id) as session_count,
        COUNT(CASE WHEN es.status = 'active' THEN 1 END) as active_sessions,
        (SELECT COUNT(*) FROM event_rsvps WHERE event_id = e.id AND status = 'attending') +
        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = e.id AND status = 'attending') as total_rsvps
    FROM events e
    LEFT JOIN event_sessions es ON e.id = es.event_id
    WHERE e.is_active = 1
    GROUP BY e.id
    ORDER BY e.event_date DESC
    LIMIT 10
");
$recent_events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get role-specific dashboard activity
$stmt = $pdo->query("
    SELECT
        ur.role_display_name,
        ur.dashboard_route,
        COUNT(dal.id) as access_count,
        MAX(dal.access_time) as last_access,
        COUNT(DISTINCT dal.user_id) as unique_users
    FROM user_roles ur
    LEFT JOIN dashboard_access_log dal ON ur.role_name = dal.role_name
        AND dal.access_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY ur.id
    ORDER BY ur.hierarchy_level ASC
");
$dashboard_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent user activity
$stmt = $pdo->query("
    SELECT 
        a.username,
        ur.role_display_name,
        dal.dashboard_accessed,
        dal.access_time,
        dal.actions_performed
    FROM dashboard_access_log dal
    JOIN admins a ON dal.user_id = a.id
    LEFT JOIN user_role_assignments ura ON a.id = ura.user_id AND ura.is_active = 1
    LEFT JOIN user_roles ur ON ura.role_id = ur.id
    ORDER BY dal.access_time DESC
    LIMIT 20
");
$recent_activity = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get system alerts (events needing attention)
$alerts = [];

// Events with no sessions
$stmt = $pdo->query("
    SELECT e.id, e.title, e.event_date
    FROM events e
    LEFT JOIN event_sessions es ON e.id = es.event_id
    WHERE e.is_active = 1 AND es.id IS NULL
    AND e.event_date >= CURDATE()
");
$events_no_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
if (!empty($events_no_sessions)) {
    $alerts[] = [
        'type' => 'warning',
        'title' => 'Events Without Sessions',
        'message' => count($events_no_sessions) . ' upcoming events have no sessions configured',
        'action' => 'event_sessions.php',
        'action_text' => 'Add Sessions'
    ];
}

// Sessions without moderators
$stmt = $pdo->query("
    SELECT es.id, es.session_title, e.title as event_title
    FROM event_sessions es
    JOIN events e ON es.event_id = e.id
    LEFT JOIN session_assignments sa ON es.id = sa.session_id AND sa.is_active = 1
    WHERE es.status = 'active' AND sa.id IS NULL
    AND es.start_datetime >= NOW()
");
$sessions_no_moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
if (!empty($sessions_no_moderators)) {
    $alerts[] = [
        'type' => 'info',
        'title' => 'Sessions Without Moderators',
        'message' => count($sessions_no_moderators) . ' upcoming sessions have no assigned moderators',
        'action' => 'setup_rbac_system.php',
        'action_text' => 'Assign Moderators'
    ];
}

// Users without roles
$stmt = $pdo->query("
    SELECT a.id, a.username
    FROM admins a
    LEFT JOIN user_role_assignments ura ON a.id = ura.user_id AND ura.is_active = 1
    WHERE ura.id IS NULL
");
$users_no_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
if (!empty($users_no_roles)) {
    $alerts[] = [
        'type' => 'warning',
        'title' => 'Users Without Roles',
        'message' => count($users_no_roles) . ' users have no assigned roles',
        'action' => 'setup_rbac_system.php',
        'action_text' => 'Assign Roles'
    ];
}

// Page title and header info
$page_title = 'Super Admin Dashboard';
$page_header = 'Super Admin Dashboard';
$page_description = 'Complete system oversight and multi-tier dashboard management';

// Include header
include 'includes/header.php';
?>

<style>
.super-admin-header {
    background: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    color: white;
    border-radius: 10px;
}
.dashboard-card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.alert-card {
    border-left: 4px solid;
    background-color: #f8f9fa;
}
.alert-card.warning { border-left-color: #ffc107; }
.alert-card.info { border-left-color: #17a2b8; }
.alert-card.danger { border-left-color: #dc3545; }
.activity-item {
    border-bottom: 1px solid #dee2e6;
    padding: 10px 0;
}
.activity-item:last-child {
    border-bottom: none;
}
.dashboard-link {
    display: block;
    padding: 15px;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
}
.dashboard-link:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-2px);
}
.impersonation-banner {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    color: white;
    padding: 10px;
    text-align: center;
    border-radius: 5px;
    margin-bottom: 20px;
}
</style>

<!-- Role Impersonation Banner -->
<?php if (isset($_SESSION['impersonating_role'])): ?>
    <div class="impersonation-banner">
        <strong><i class="bi bi-eye"></i> ROLE IMPERSONATION ACTIVE</strong>
        - Viewing system as: <?php echo ucfirst(str_replace('_', ' ', $_SESSION['impersonating_role'])); ?>
        <form method="POST" style="display: inline; margin-left: 15px;">
            <input type="hidden" name="action" value="stop_impersonation">
            <button type="submit" class="btn btn-sm btn-outline-light">
                <i class="bi bi-x"></i> Stop Impersonation
            </button>
        </form>
    </div>
<?php endif; ?>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card super-admin-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <!-- Description removed to avoid duplication with header -->
                    </div>
                    <div class="text-end">
                        <div class="btn-group">
                            <button class="btn btn-outline-light" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <a href="setup_rbac_system.php" class="btn btn-outline-light">
                                <i class="bi bi-gear"></i> RBAC Settings
                            </a>
                            <a href="super_admin_navigation.php" class="btn btn-outline-light">
                                <i class="bi bi-compass"></i> Navigation Guide
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- System Overview Metrics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card metric-card">
            <div class="card-body">
                <h5 class="card-title text-white mb-3">
                    <i class="bi bi-graph-up"></i> System Overview
                    <small class="text-white-50 ms-2">Real-time system metrics</small>
                </h5>
                <div class="row text-center">
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($system_stats['total_events']); ?></h3>
                        <small class="text-white-50">Active Events</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($system_stats['total_sessions']); ?></h3>
                        <small class="text-white-50">Active Sessions</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($system_stats['total_users']); ?></h3>
                        <small class="text-white-50">Total Users</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($system_stats['active_role_assignments']); ?></h3>
                        <small class="text-white-50">Role Assignments</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($attendance_stats['total_rsvps'] ?? 0); ?></h3>
                        <small class="text-white-50">Total RSVPs</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white"><?php echo number_format($system_stats['recent_dashboard_access']); ?></h3>
                        <small class="text-white-50">24h Dashboard Access</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Alerts -->
<?php if (!empty($alerts)): ?>
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-exclamation-triangle text-warning"></i> System Alerts
                    <span class="badge bg-warning ms-2"><?php echo count($alerts); ?></span>
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($alerts as $alert): ?>
                        <div class="col-md-4 mb-3">
                            <div class="alert-card <?php echo $alert['type']; ?> p-3">
                                <h6 class="fw-bold"><?php echo htmlspecialchars($alert['title']); ?></h6>
                                <p class="mb-2"><?php echo htmlspecialchars($alert['message']); ?></p>
                                <a href="<?php echo htmlspecialchars($alert['action']); ?>" class="btn btn-sm btn-outline-primary">
                                    <?php echo htmlspecialchars($alert['action_text']); ?>
                                </a>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Multi-Tier Dashboard Access -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-speedometer2"></i> Multi-Tier Dashboard Access
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php foreach ($dashboard_activity as $dashboard): ?>
                        <div class="col-md-4 mb-3">
                            <a href="<?php echo htmlspecialchars($dashboard['dashboard_route']); ?>" class="dashboard-link text-decoration-none">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="fw-bold text-primary">
                                            <?php echo htmlspecialchars($dashboard['role_display_name']); ?>
                                        </h6>
                                        <small class="text-muted">
                                            <?php echo $dashboard['access_count']; ?> accesses (7 days)
                                            <br>
                                            <?php echo $dashboard['unique_users']; ?> unique users
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <i class="bi bi-arrow-right text-primary"></i>
                                        <?php if ($dashboard['last_access']): ?>
                                            <br>
                                            <small class="text-muted">
                                                Last: <?php echo date('M j, g:i A', strtotime($dashboard['last_access'])); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Role Impersonation -->
                <div class="mt-3 pt-3 border-top">
                    <h6 class="text-muted mb-2">
                        <i class="bi bi-eye"></i> Role Impersonation (Testing)
                    </h6>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="action" value="impersonate_role">
                        <div class="input-group" style="max-width: 400px;">
                            <select class="form-select" name="role_name" required>
                                <option value="">Select role to impersonate...</option>
                                <option value="event_coordinator">Event Coordinator</option>
                                <option value="organizer">Event Organizer</option>
                                <option value="session_moderator">Session Moderator</option>
                                <option value="staff">Staff</option>
                            </select>
                            <button type="submit" class="btn btn-outline-warning">
                                <i class="bi bi-eye"></i> Impersonate
                            </button>
                        </div>
                    </form>
                    <small class="text-muted d-block mt-1">
                        View the system from another role's perspective for testing and support
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Events and Activity -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Recent Events
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_events)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                        <p>No active events found.</p>
                        <a href="events.php" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Create Event
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Event</th>
                                    <th>Date</th>
                                    <th>Sessions</th>
                                    <th>RSVPs</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_events as $event): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($event['title']); ?></strong>
                                            <?php if ($event['location']): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                                            <br>
                                            <small class="text-muted">
                                                <?php echo date('g:i A', strtotime($event['event_date'])); ?>
                                            </small>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary"><?php echo $event['active_sessions']; ?></span>
                                            /
                                            <span class="text-muted"><?php echo $event['session_count']; ?></span>
                                        </td>
                                        <td>
                                            <strong><?php echo number_format($event['total_rsvps']); ?></strong>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="event_attendance_detail.php?event_id=<?php echo $event['id']; ?>"
                                                   class="btn btn-outline-primary" title="Event Attendance">
                                                    <i class="bi bi-list-check"></i>
                                                </a>
                                                <a href="multi_session_dashboard.php?event_id=<?php echo $event['id']; ?>"
                                                   class="btn btn-outline-success" title="Multi-Session Dashboard">
                                                    <i class="bi bi-speedometer2"></i>
                                                </a>
                                                <a href="cross_session_attendance.php?event_id=<?php echo $event['id']; ?>"
                                                   class="btn btn-outline-warning" title="Cross-Session Operations">
                                                    <i class="bi bi-diagram-3"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="event_attendance.php" class="btn btn-outline-primary">
                            <i class="bi bi-list"></i> View All Events
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-activity"></i> Recent Activity
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($recent_activity)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-clock-history fs-3 d-block mb-2"></i>
                        <p>No recent activity.</p>
                    </div>
                <?php else: ?>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach (array_slice($recent_activity, 0, 10) as $activity): ?>
                            <div class="activity-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <strong><?php echo htmlspecialchars($activity['username']); ?></strong>
                                        <?php if ($activity['role_display_name']): ?>
                                            <span class="badge bg-secondary ms-1">
                                                <?php echo htmlspecialchars($activity['role_display_name']); ?>
                                            </span>
                                        <?php endif; ?>
                                        <br>
                                        <small class="text-muted">
                                            Accessed: <?php echo str_replace('_', ' ', $activity['dashboard_accessed']); ?>
                                        </small>
                                        <?php if ($activity['actions_performed'] > 0): ?>
                                            <br>
                                            <small class="text-success">
                                                <?php echo $activity['actions_performed']; ?> actions performed
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo date('M j, g:i A', strtotime($activity['access_time'])); ?>
                                    </small>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    <div class="text-center mt-3">
                        <button class="btn btn-outline-secondary btn-sm" onclick="loadMoreActivity()">
                            <i class="bi bi-arrow-down"></i> Load More
                        </button>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card dashboard-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <div class="d-grid">
                            <a href="events.php" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i><br>
                                <small>Create Event</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid">
                            <a href="setup_rbac_system.php" class="btn btn-warning">
                                <i class="bi bi-person-plus"></i><br>
                                <small>Manage Users</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid">
                            <a href="event_attendance.php" class="btn btn-success">
                                <i class="bi bi-list-check"></i><br>
                                <small>Event Attendance</small>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid">
                            <button class="btn btn-info" onclick="exportSystemData()">
                                <i class="bi bi-download"></i><br>
                                <small>Export Data</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid">
                            <button class="btn btn-secondary" onclick="showSystemHealth()">
                                <i class="bi bi-heart-pulse"></i><br>
                                <small>System Health</small>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i><br>
                                <small>Refresh All</small>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Dashboard functionality
function refreshDashboard() {
    const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;

    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i><br><small>Refreshing...</small>';
    refreshBtn.disabled = true;

    // Add spinning animation
    const style = document.createElement('style');
    style.textContent = '.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    document.head.appendChild(style);

    setTimeout(() => {
        location.reload();
    }, 1000);
}

function exportSystemData() {
    if (confirm('Export comprehensive system data including events, sessions, attendance, and user activity?')) {
        window.open('export_system_data.php', '_blank');
    }
}

function showSystemHealth() {
    // In real implementation, this would show a modal with detailed system health metrics
    alert('System Health Monitor\n\n' +
          '✅ Database: Connected\n' +
          '✅ RBAC System: Active\n' +
          '✅ Dashboard Access: Normal\n' +
          '✅ Session Management: Operational\n' +
          '⚠️ Alerts: <?php echo count($alerts); ?> items need attention');
}

function loadMoreActivity() {
    const loadMoreBtn = document.querySelector('button[onclick="loadMoreActivity()"]');
    const originalText = loadMoreBtn.innerHTML;

    loadMoreBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> Loading...';
    loadMoreBtn.disabled = true;

    // Get current activity count
    const activityContainer = document.querySelector('.card-body div[style*="max-height: 400px"]');
    const currentItems = activityContainer.querySelectorAll('.activity-item').length;

    // Make AJAX request to load more activity
    fetch('ajax/load_more_activity.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            offset: currentItems,
            limit: 10
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.activities.length > 0) {
            // Append new activities
            data.activities.forEach(activity => {
                const activityHtml = `
                    <div class="activity-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong>${activity.username}</strong>
                                ${activity.role_display_name ? `<span class="badge bg-secondary ms-1">${activity.role_display_name}</span>` : ''}
                                <br>
                                <small class="text-muted">
                                    Accessed: ${activity.dashboard_accessed.replace(/_/g, ' ')}
                                </small>
                                ${activity.actions_performed > 0 ? `
                                    <br>
                                    <small class="text-success">
                                        ${activity.actions_performed} actions performed
                                    </small>
                                ` : ''}
                            </div>
                            <small class="text-muted">
                                ${activity.access_time}
                            </small>
                        </div>
                    </div>
                `;
                activityContainer.insertAdjacentHTML('beforeend', activityHtml);
            });

            // Hide load more button if no more activities
            if (data.activities.length < 10) {
                loadMoreBtn.style.display = 'none';
            } else {
                loadMoreBtn.innerHTML = originalText;
                loadMoreBtn.disabled = false;
            }
        } else {
            // No more activities
            loadMoreBtn.style.display = 'none';
        }
    })
    .catch(error => {
        console.error('Error loading more activity:', error);
        loadMoreBtn.innerHTML = originalText;
        loadMoreBtn.disabled = false;
        alert('Error loading more activity. Please try again.');
    });
}

// Auto-refresh every 5 minutes
setInterval(function() {
    // Only refresh if user is active (has interacted recently)
    if (document.hasFocus()) {
        location.reload();
    }
}, 300000); // 5 minutes

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Add tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Show last refresh time
    const now = new Date();
    const refreshTime = document.createElement('small');
    refreshTime.className = 'text-muted';
    refreshTime.textContent = `Last updated: ${now.toLocaleTimeString()}`;
    document.querySelector('.super-admin-header .card-body').appendChild(refreshTime);
});
</script>

<?php include 'includes/footer.php'; ?>
