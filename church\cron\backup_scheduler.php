<?php
/**
 * Backup Scheduler Cron Job
 * This script should be run every hour to check for scheduled backups
 *
 * Dynamic Cron Job Command (run hourly):
 * 0 * * * * wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/church/cron/backup_scheduler.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Security check to allow for CLI execution and web requests
if (php_sapi_name() !== 'cli') {
    // Only check for cron_key when running via web request
    if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
        header('HTTP/1.0 403 Forbidden');
        exit('Access Denied');
    }
}

// Include configuration
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/DatabaseBackupManager.php';

// Log function
function logMessage($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logFile = __DIR__ . '/../logs/backup_scheduler.log';
    
    // Ensure log directory exists
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    logMessage("Backup scheduler started");
    
    // Initialize backup manager
    $backupManager = new DatabaseBackupManager($pdo);
    
    // Get scheduled backups that need to run
    $scheduledBackups = $backupManager->getScheduledBackups();
    
    if (empty($scheduledBackups)) {
        logMessage("No scheduled backups found");
        exit(0);
    }
    
    logMessage("Found " . count($scheduledBackups) . " scheduled backup(s) to process");
    
    foreach ($scheduledBackups as $config) {
        try {
            logMessage("Processing backup configuration: " . $config['name'] . " (ID: " . $config['id'] . ")");
            
            // Check if backup has already run today for this configuration
            if (hasBackupRunToday($pdo, $config['id'], $config['schedule_type'])) {
                logMessage("Backup already run today for configuration: " . $config['name']);
                continue;
            }
            
            // Create backup
            $result = $backupManager->createBackup($config['id'], false, null);
            
            if ($result['success']) {
                logMessage("Backup completed successfully: " . $result['filename']);
            } else {
                logMessage("Backup failed: " . $result['message']);
            }
            
        } catch (Exception $e) {
            logMessage("Error processing backup configuration " . $config['id'] . ": " . $e->getMessage());
        }
    }
    
    logMessage("Backup scheduler completed");
    
} catch (Exception $e) {
    logMessage("Fatal error in backup scheduler: " . $e->getMessage());
    exit(1);
}

/**
 * Check if backup has already run today for the given configuration
 */
function hasBackupRunToday($pdo, $configId, $scheduleType) {
    try {
        $today = date('Y-m-d');
        
        // For daily backups, check if any backup ran today
        if ($scheduleType === 'daily') {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM backup_history 
                WHERE configuration_id = ? 
                AND DATE(started_at) = ? 
                AND status IN ('completed', 'running')
            ");
            $stmt->execute([$configId, $today]);
            return $stmt->fetchColumn() > 0;
        }
        
        // For weekly backups, check if backup ran this week
        if ($scheduleType === 'weekly') {
            $weekStart = date('Y-m-d', strtotime('monday this week'));
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM backup_history 
                WHERE configuration_id = ? 
                AND DATE(started_at) >= ? 
                AND status IN ('completed', 'running')
            ");
            $stmt->execute([$configId, $weekStart]);
            return $stmt->fetchColumn() > 0;
        }
        
        // For monthly backups, check if backup ran this month
        if ($scheduleType === 'monthly') {
            $monthStart = date('Y-m-01');
            $stmt = $pdo->prepare("
                SELECT COUNT(*) 
                FROM backup_history 
                WHERE configuration_id = ? 
                AND DATE(started_at) >= ? 
                AND status IN ('completed', 'running')
            ");
            $stmt->execute([$configId, $monthStart]);
            return $stmt->fetchColumn() > 0;
        }
        
        return false;
        
    } catch (Exception $e) {
        logMessage("Error checking if backup has run: " . $e->getMessage());
        return false;
    }
}
?>
