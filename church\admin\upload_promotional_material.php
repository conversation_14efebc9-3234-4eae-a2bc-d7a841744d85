<?php
/**
 * Promotional Material Upload Handler
 *
 * Handles AJAX uploads of promotional materials for events
 */

// Include AJAX session handler for proper session management
require_once 'includes/ajax-session-handler.php';

// Include thumbnail utilities
require_once 'includes/thumbnail-utils.php';

// Validate admin session
$sessionValidation = handleAjaxSessionValidation();

// Include configuration safely
try {
    $pdo = includeConfigForAjax();
} catch (Exception $e) {
    error_log("PROMOTIONAL UPLOAD ERROR - Config include failed: " . $e->getMessage());
    sendAjaxResponse([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
}

// Debug logging
error_log("PROMOTIONAL UPLOAD - Session admin_id: " . ($_SESSION['admin_id'] ?? 'NOT SET'));
error_log("PROMOTIONAL UPLOAD - POST data: " . print_r($_POST, true));
error_log("PROMOTIONAL UPLOAD - FILES data: " . print_r($_FILES, true));

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    error_log("PROMOTIONAL UPLOAD ERROR - Invalid request method: " . $_SERVER['REQUEST_METHOD']);
    sendAjaxResponse(['success' => false, 'message' => 'Invalid request method']);
}

$event_id = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
$is_header_banner = filter_input(INPUT_POST, 'is_header_banner', FILTER_VALIDATE_BOOLEAN);
$alt_text = filter_input(INPUT_POST, 'alt_text', FILTER_SANITIZE_STRING);

error_log("PROMOTIONAL UPLOAD - Event ID: " . ($event_id ?? 'NULL'));
error_log("PROMOTIONAL UPLOAD - Is header banner: " . ($is_header_banner ? 'true' : 'false'));

if (!$event_id) {
    error_log("PROMOTIONAL UPLOAD ERROR - Invalid event ID");
    sendAjaxResponse(['success' => false, 'message' => 'Invalid event ID']);
}

if (!isset($_FILES['promotional_file'])) {
    error_log("PROMOTIONAL UPLOAD ERROR - No promotional_file in FILES array");
    sendAjaxResponse(['success' => false, 'message' => 'No file uploaded - promotional_file not found']);
}

if ($_FILES['promotional_file']['error'] !== UPLOAD_ERR_OK) {
    $upload_error = $_FILES['promotional_file']['error'];
    error_log("PROMOTIONAL UPLOAD ERROR - Upload error code: $upload_error");
    sendAjaxResponse(['success' => false, 'message' => 'File upload error (code: ' . $upload_error . ')']);
}

try {
    $file = $_FILES['promotional_file'];
    $file_name = $file['name'];
    $file_tmp = $file['tmp_name'];
    $file_size = $file['size'];
    $file_type = $file['type'];
    $file_extension = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

    error_log("PROMOTIONAL UPLOAD - File details: name='$file_name', type='$file_type', extension='$file_extension', size=$file_size");

    // Validate file type
    $allowed_types = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'
    ];
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf'];

    if (!in_array($file_type, $allowed_types) || !in_array($file_extension, $allowed_extensions)) {
        error_log("PROMOTIONAL UPLOAD ERROR - Invalid file type: '$file_type' or extension: '$file_extension'");
        sendAjaxResponse(['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, GIF, and PDF files are allowed.']);
    }

    error_log("PROMOTIONAL UPLOAD - File type validation passed");

    // Validate file size (15MB max)
    $max_size = 15 * 1024 * 1024;
    if ($file_size > $max_size) {
        sendAjaxResponse(['success' => false, 'message' => 'File too large. Maximum size is 15MB.']);
    }

    // Create upload directories using absolute server paths
    $base_dir = __DIR__ . '/../uploads/events/';
    $promotional_dir = $base_dir . 'promotional/';
    $thumbnails_dir = $base_dir . 'thumbnails/';

    error_log("PROMOTIONAL UPLOAD - Base dir: '$base_dir'");
    error_log("PROMOTIONAL UPLOAD - Promotional dir: '$promotional_dir'");
    error_log("PROMOTIONAL UPLOAD - Thumbnails dir: '$thumbnails_dir'");

    foreach ([$base_dir, $promotional_dir, $thumbnails_dir] as $dir) {
        if (!is_dir($dir)) {
            error_log("PROMOTIONAL UPLOAD - Creating directory: '$dir'");
            if (!mkdir($dir, 0755, true)) {
                error_log("PROMOTIONAL UPLOAD ERROR - Failed to create directory: '$dir'");
                sendAjaxResponse(['success' => false, 'message' => "Failed to create upload directory: " . basename($dir)]);
            }
        } else {
            error_log("PROMOTIONAL UPLOAD - Directory exists: '$dir'");
        }

        // Check if directory is writable
        if (!is_writable($dir)) {
            error_log("PROMOTIONAL UPLOAD ERROR - Directory not writable: '$dir'");
            sendAjaxResponse(['success' => false, 'message' => "Upload directory is not writable: " . basename($dir)]);
        }
    }

    // Sanitize original filename
    $original_filename = pathinfo($file_name, PATHINFO_FILENAME);
    $safe_filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $original_filename);
    $safe_filename = preg_replace('/_+/', '_', $safe_filename); // Replace multiple underscores with single
    $safe_filename = trim($safe_filename, '_'); // Remove leading/trailing underscores

    // Ensure we have a valid filename
    if (empty($safe_filename)) {
        $safe_filename = 'file_' . time();
    }

    // Generate unique filename
    $unique_name = 'event_' . $event_id . '_' . time() . '_' . $safe_filename . '.' . $file_extension;

    // Log filename processing for debugging
    error_log("UPLOAD DEBUG - Original: '$file_name', Safe: '$safe_filename', Unique: '$unique_name'");
    
    // Determine file category and target directory
    $file_category = in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif']) ? 'promotional' : 'document';
    $target_dir = ($file_category === 'promotional') ? $promotional_dir : $base_dir;
    $file_path = $target_dir . $unique_name;

    // Move uploaded file
    error_log("UPLOAD DEBUG - Attempting to move from '$file_tmp' to '$file_path'");

    // Check if source file exists
    if (!file_exists($file_tmp)) {
        error_log("UPLOAD ERROR - Temporary file not found: '$file_tmp'");
        sendAjaxResponse(['success' => false, 'message' => "Temporary file not found for $file_name"]);
    }

    // Check if target directory is writable
    if (!is_writable($target_dir)) {
        error_log("UPLOAD ERROR - Target directory not writable: '$target_dir'");
        sendAjaxResponse(['success' => false, 'message' => "Target directory is not writable: $target_dir"]);
    }

    if (!move_uploaded_file($file_tmp, $file_path)) {
        $error = error_get_last();
        $error_msg = $error ? $error['message'] : 'Unknown error';
        error_log("UPLOAD ERROR - Failed to move file: $error_msg");
        sendAjaxResponse(['success' => false, 'message' => "Failed to save $file_name: $error_msg"]);
    }

    error_log("UPLOAD SUCCESS - File moved to: '$file_path'");

    // Generate thumbnail for images
    $thumbnail_path = null;
    if ($file_category === 'promotional') {
        try {
            // Check available memory before thumbnail generation
            $memory_limit = ini_get('memory_limit');
            $memory_usage = memory_get_usage(true);
            error_log("THUMBNAIL - Memory usage before: " . number_format($memory_usage / 1024 / 1024, 2) . "MB (limit: $memory_limit)");

            $thumbnail_path = generateThumbnail($file_path, $thumbnails_dir, $unique_name);

            if ($thumbnail_path) {
                error_log("THUMBNAIL - Generated successfully: " . basename($thumbnail_path));
            } else {
                error_log("THUMBNAIL - Generation failed but no exception thrown");
            }
        } catch (Exception $e) {
            error_log("Thumbnail generation error: " . $e->getMessage());
            error_log("Error file: " . $e->getFile() . " line: " . $e->getLine());
            // Continue without thumbnail if there's an error
        }
    }

    // If this is set as header banner, unset any existing header banner for this event
    if ($is_header_banner) {
        $stmt = $pdo->prepare("UPDATE event_files SET is_header_banner = 0 WHERE event_id = ?");
        $stmt->execute([$event_id]);
    }

    // Convert full server path to relative path for database storage
    // Remove the admin directory part and convert to admin-relative path
    $admin_dir = __DIR__ . '/';
    $relative_file_path = str_replace($admin_dir, '../', $file_path);

    error_log("UPLOAD DEBUG - Full file path: '$file_path'");
    error_log("UPLOAD DEBUG - Admin dir: '$admin_dir'");
    error_log("UPLOAD DEBUG - Storing relative path in DB: '$relative_file_path'");

    // Save to database
    $stmt = $pdo->prepare("
        INSERT INTO event_files (event_id, file_name, file_path, file_type, file_size,
                               uploaded_by, file_category, is_header_banner, alt_text, display_order)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");

    // Get next display order
    $order_stmt = $pdo->prepare("SELECT COALESCE(MAX(display_order), 0) + 1 FROM event_files WHERE event_id = ?");
    $order_stmt->execute([$event_id]);
    $display_order = (int)$order_stmt->fetchColumn();

    $stmt->execute([
        $event_id,
        $file_name,
        $relative_file_path,  // Store relative path instead of full path
        $file_type,
        $file_size,
        $_SESSION['admin_id'],
        $file_category,
        $is_header_banner ? 1 : 0,
        $alt_text,
        $display_order
    ]);

    $file_id = $pdo->lastInsertId();

    sendAjaxResponse([
        'success' => true,
        'message' => 'File uploaded successfully',
        'file' => [
            'id' => $file_id,
            'name' => $file_name,
            'category' => $file_category,
            'is_header_banner' => $is_header_banner,
            'thumbnail' => $thumbnail_path,
            'file_path' => $file_path
        ]
    ]);

} catch (Exception $e) {
    // Log the error for debugging with more details
    error_log("Promotional material upload error: " . $e->getMessage());
    error_log("Error file: " . $e->getFile() . " line: " . $e->getLine());
    error_log("Stack trace: " . $e->getTraceAsString());
    error_log("Upload file: " . ($file_name ?? 'unknown') . " | Event ID: " . ($event_id ?? 'unknown'));

    // Clean up uploaded file if there was an error
    if (isset($file_path) && file_exists($file_path)) {
        unlink($file_path);
    }

    sendAjaxResponse(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
}

?>
