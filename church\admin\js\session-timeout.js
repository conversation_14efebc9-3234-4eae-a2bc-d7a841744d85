/**
 * Session Timeout Handler
 * 
 * This script handles automatic logout after a period of inactivity.
 * It tracks user activity and redirects to the logout page after the timeout period.
 */

(function() {
    // Configuration - use server-provided values if available
    const config = window.sessionConfig || {};
    const timeoutDuration = config.timeoutDuration || 2 * 60 * 60 * 1000; // 2 hours in milliseconds (fallback)
    const warningTime = 5 * 60 * 1000;     // Show warning 5 minutes before timeout

    // Get admin URL dynamically
    const adminUrl = (typeof ADMIN_URL !== 'undefined') ? ADMIN_URL :
                     (config.adminUrl || window.location.pathname.replace(/\/[^\/]*$/, ''));

    const logoutUrl = config.logoutUrl || adminUrl + '/logout.php?timeout=1';
    const warningMessage = 'Your session is about to expire due to inactivity. You will be logged out in 5 minutes.';
    const heartbeatInterval = 5 * 60 * 1000; // Update server every 5 minutes
    const heartbeatUrl = adminUrl + '/ajax/update_activity.php';
    
    // Variables to track timeout
    let timeoutId;
    let warningId;
    let heartbeatId;
    let warningShown = false;
    let countdownInterval;
    
    // Create warning modal if it doesn't exist
    function createWarningModal() {
        // Check if modal already exists
        if (document.getElementById('session-timeout-modal')) {
            return document.getElementById('session-timeout-modal');
        }
        
        // Create modal elements
        const modal = document.createElement('div');
        modal.id = 'session-timeout-modal';
        modal.className = 'modal fade';
        modal.setAttribute('tabindex', '-1');
        modal.setAttribute('aria-hidden', 'true');
        
        // Modal HTML
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-warning">
                        <h5 class="modal-title">Session Timeout Warning</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>${warningMessage}</p>
                        <div class="text-center">
                            <div class="progress mb-3">
                                <div id="session-timer-bar" class="progress-bar bg-danger" role="progressbar" style="width: 100%"></div>
                            </div>
                            <span id="session-timeout-countdown">300</span> seconds remaining
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button id="session-continue-btn" type="button" class="btn btn-primary" data-bs-dismiss="modal">Continue Session</button>
                        <button id="session-logout-btn" type="button" class="btn btn-outline-secondary">Logout Now</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add modal to document
        document.body.appendChild(modal);
        
        // Add event listeners
        document.getElementById('session-continue-btn').addEventListener('click', resetTimeout);
        document.getElementById('session-logout-btn').addEventListener('click', logout);
        
        return modal;
    }
    
    // Start countdown timer in the warning modal
    function startCountdown() {
        // Clear any existing interval
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        
        let countdown = Math.floor(warningTime / 1000);
        const countdownElement = document.getElementById('session-timeout-countdown');
        const progressBar = document.getElementById('session-timer-bar');
        
        // Set initial values
        if (countdownElement) countdownElement.textContent = countdown;
        if (progressBar) progressBar.style.width = '100%';
        
        countdownInterval = setInterval(() => {
            countdown -= 1;
            
            if (countdownElement) countdownElement.textContent = countdown;
            
            // Update progress bar
            if (progressBar) {
                progressBar.style.width = (countdown / (warningTime / 1000) * 100) + '%';
            }
            
            if (countdown <= 0) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
        }, 1000);
        
        return countdownInterval;
    }
    
    // Show the warning modal
    function showWarningModal() {
        warningShown = true;
        
        // Create modal if it doesn't exist
        const modalElement = createWarningModal();
        
        // Initialize Bootstrap modal
        const modal = bootstrap.Modal.getOrCreateInstance(modalElement);
        modal.show();
        
        // Start countdown
        startCountdown();
        
        // Set final timeout to logout
        clearTimeout(timeoutId);
        timeoutId = setTimeout(logout, warningTime);
    }
    
    // Send heartbeat to server to update last activity time
    function sendHeartbeat() {
        fetch(heartbeatUrl, {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Session heartbeat successful, remaining time: ' + data.remainingTime + 's');
            } else {
                console.warn('Session heartbeat failed:', data.message);
                if (data.status === 'not_logged_in') {
                    // Already logged out, redirect to login page
                    window.location.href = adminUrl + '/login.php';
                }
            }
        })
        .catch(error => {
            console.error('Session heartbeat error:', error);
        });
    }
    
    // Reset the session timeout
    function resetTimeout() {
        // Clear existing timeouts
        clearTimeout(timeoutId);
        clearTimeout(warningId);
        
        // Hide warning modal if shown
        if (warningShown) {
            const modalElement = document.getElementById('session-timeout-modal');
            if (modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                }
            }
            
            // Clear countdown interval
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
            
            warningShown = false;
        }
        
        // Send heartbeat to server
        sendHeartbeat();
        
        // Set new timeouts
        warningId = setTimeout(showWarningModal, timeoutDuration - warningTime);
        timeoutId = setTimeout(logout, timeoutDuration);
    }
    
    // Redirect to logout page
    function logout() {
        window.location.href = logoutUrl;
    }
    
    // Activity events to monitor
    const activityEvents = [
        'mousedown', 'mousemove', 'keydown',
        'scroll', 'touchstart', 'click', 'keypress'
    ];
    
    // Add event listeners for user activity
    function addActivityListeners() {
        activityEvents.forEach(eventName => {
            document.addEventListener(eventName, resetTimeout);
        });
    }
    
    // Start the heartbeat interval
    function startHeartbeat() {
        // Send initial heartbeat
        sendHeartbeat();
        
        // Set up regular heartbeat
        heartbeatId = setInterval(sendHeartbeat, heartbeatInterval);
    }
    
    // Stop the heartbeat interval
    function stopHeartbeat() {
        if (heartbeatId) {
            clearInterval(heartbeatId);
            heartbeatId = null;
        }
    }
    
    // Initialize session timeout
    function init() {
        // Create the warning modal
        createWarningModal();
        
        // Add activity listeners
        addActivityListeners();
        
        // Start heartbeat
        startHeartbeat();
        
        // Start initial timeout
        resetTimeout();
        
        console.log('Session timeout initialized: ' + timeoutDuration/1000 + ' seconds');
        
        // Add window unload handler to clean up
        window.addEventListener('beforeunload', function() {
            stopHeartbeat();
            clearTimeout(timeoutId);
            clearTimeout(warningId);
        });
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})(); 