<?php
session_start();

// Include route protection (this will handle login check and role verification)
require_once 'includes/route_protection.php';

// Protect this page - Super Admin only
protectSuperAdminRoute();

// Include the configuration file
require_once '../config.php';
require_once '../includes/admin_email_notifications.php';

$message = '';
$error = '';

// Handle RBAC system setup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'setup_rbac') {
            // Read and execute the SQL setup file
            $sql_file = __DIR__ . '/setup_role_system.sql';
            if (!file_exists($sql_file)) {
                throw new Exception('SQL setup file not found');
            }
            
            $sql_content = file_get_contents($sql_file);
            $statements = explode(';', $sql_content);
            
            $pdo->beginTransaction();
            
            $executed_count = 0;
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $executed_count++;
                    } catch (PDOException $e) {
                        // Log but continue - some statements might fail if tables already exist
                        error_log("RBAC Setup - Statement failed: " . $e->getMessage());
                    }
                }
            }
            
            // Assign super_admin role to current admin
            $current_admin_id = $_SESSION['admin_id'];
            
            // Get super_admin role ID
            $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = 'super_admin'");
            $stmt->execute();
            $super_admin_role_id = $stmt->fetchColumn();
            
            if ($super_admin_role_id) {
                // Assign super_admin role to current user
                $stmt = $pdo->prepare("
                    INSERT INTO user_role_assignments (user_id, role_id, assigned_by) 
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE is_active = 1
                ");
                $stmt->execute([$current_admin_id, $super_admin_role_id, $current_admin_id]);
            }
            
            $pdo->commit();
            $message = "RBAC system setup completed successfully! Executed {$executed_count} SQL statements. You now have Super Admin access.";

            // If setup was required, redirect to Super Admin Dashboard after a delay
            if (isset($_GET['setup_required'])) {
                echo "<script>
                    setTimeout(function() {
                        window.location.href = 'super_admin_dashboard.php';
                    }, 3000);
                </script>";
            }
            
        } elseif ($_POST['action'] === 'assign_role') {
            $user_id = $_POST['user_id'] ?? '';
            $role_id = $_POST['role_id'] ?? '';
            $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
            
            if (empty($user_id) || empty($role_id)) {
                throw new Exception('User ID and Role ID are required');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by, expires_at) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                assigned_by = VALUES(assigned_by),
                expires_at = VALUES(expires_at),
                is_active = 1
            ");
            $stmt->execute([$user_id, $role_id, $_SESSION['admin_id'], $expires_at]);

            // Send role assignment notification email
            try {
                $emailSent = sendAdminRoleAssignmentNotification($user_id, [$role_id], $_SESSION['admin_id']);
                if ($emailSent) {
                    $message = "Role assigned successfully! Notification email sent.";
                } else {
                    $message = "Role assigned successfully! (Note: Notification email could not be sent)";
                }
            } catch (Exception $e) {
                error_log("Failed to send role assignment notification: " . $e->getMessage());
                $message = "Role assigned successfully! (Note: Notification email could not be sent)";
            }
            
        } elseif ($_POST['action'] === 'assign_event') {
            $user_id = $_POST['user_id'] ?? '';
            $event_id = $_POST['event_id'] ?? '';
            $role_type = $_POST['role_type'] ?? '';

            if (empty($user_id) || empty($event_id) || empty($role_type)) {
                throw new Exception('All fields are required');
            }

            $stmt = $pdo->prepare("
                INSERT INTO event_assignments (user_id, event_id, role_type, assigned_by)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE is_active = 1
            ");
            $stmt->execute([$user_id, $event_id, $role_type, $_SESSION['admin_id']]);

            // Send event assignment notification email
            require_once '../includes/admin_email_notifications.php';
            try {
                $emailSent = sendEventAssignmentNotification($user_id, $event_id, $role_type, $_SESSION['admin_id']);
                if ($emailSent) {
                    $message = "Event assignment completed successfully! Notification email sent.";
                } else {
                    $message = "Event assignment completed successfully! (Note: Notification email could not be sent)";
                }
            } catch (Exception $e) {
                error_log("Failed to send event assignment notification: " . $e->getMessage());
                $message = "Event assignment completed successfully! (Note: Notification email could not be sent)";
            }
            
        } elseif ($_POST['action'] === 'assign_session') {
            $user_id = $_POST['user_id'] ?? '';
            $session_id = $_POST['session_id'] ?? '';

            if (empty($user_id) || empty($session_id)) {
                throw new Exception('User ID and Session ID are required');
            }

            $stmt = $pdo->prepare("
                INSERT INTO session_assignments (user_id, session_id, assigned_by)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE is_active = 1
            ");
            $stmt->execute([$user_id, $session_id, $_SESSION['admin_id']]);

            // Send session assignment notification email
            require_once '../includes/admin_email_notifications.php';
            try {
                error_log("DEBUG: Attempting to send session assignment notification for user_id: $user_id, session_id: $session_id, assigned_by: " . $_SESSION['admin_id']);
                $emailSent = sendSessionAssignmentNotification($user_id, $session_id, $_SESSION['admin_id']);
                error_log("DEBUG: Session assignment notification result: " . ($emailSent ? 'SUCCESS' : 'FAILED'));
                if ($emailSent) {
                    $message = "Session assignment completed successfully! Notification email sent.";
                } else {
                    $message = "Session assignment completed successfully! (Note: Notification email could not be sent)";
                }
            } catch (Exception $e) {
                error_log("Failed to send session assignment notification: " . $e->getMessage());
                error_log("DEBUG: Session assignment notification exception: " . $e->getTraceAsString());
                $message = "Session assignment completed successfully! (Note: Notification email could not be sent)";
            }

        } elseif ($_POST['action'] === 'delete_role_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            // PROTECTION: Check if this is a super admin role assignment
            $stmt = $pdo->prepare("
                SELECT ura.user_id, ur.role_name, a.username, a.email
                FROM user_role_assignments ura
                JOIN user_roles ur ON ura.role_id = ur.id
                JOIN admins a ON ura.user_id = a.id
                WHERE ura.id = ?
            ");
            $stmt->execute([$assignment_id]);
            $assignment_info = $stmt->fetch();

            if ($assignment_info && $assignment_info['role_name'] === 'super_admin') {
                // Check if this is the primary super admin (<EMAIL> or superadmin)
                if ($assignment_info['email'] === '<EMAIL>' || $assignment_info['username'] === 'superadmin') {
                    throw new Exception('Cannot remove super admin role from the primary super admin user. This user must always have super admin access to prevent system lockout.');
                }

                // Check if this would leave no super admins
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as super_admin_count
                    FROM user_role_assignments ura
                    JOIN user_roles ur ON ura.role_id = ur.id
                    WHERE ur.role_name = 'super_admin' AND ura.is_active = 1 AND ura.id != ?
                ");
                $stmt->execute([$assignment_id]);
                $count_result = $stmt->fetch();

                if ($count_result['super_admin_count'] == 0) {
                    throw new Exception('Cannot remove the last super admin role assignment. At least one super admin must remain to manage the system.');
                }
            }

            $stmt = $pdo->prepare("UPDATE user_role_assignments SET is_active = 0 WHERE id = ?");
            $stmt->execute([$assignment_id]);

            $message = "Role assignment removed successfully!";

        } elseif ($_POST['action'] === 'delete_session_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE session_assignments SET is_active = 0 WHERE id = ?");
            $stmt->execute([$assignment_id]);

            $message = "Session assignment removed successfully!";

        } elseif ($_POST['action'] === 'delete_event_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE event_assignments SET is_active = 0 WHERE id = ?");
            $stmt->execute([$assignment_id]);

            $message = "Event assignment removed successfully!";

        } elseif ($_POST['action'] === 'assign_event') {
            $user_id = $_POST['user_id'] ?? '';
            $event_id = $_POST['event_id'] ?? '';
            $role_type = $_POST['role_type'] ?? '';

            if (empty($user_id) || empty($event_id) || empty($role_type)) {
                throw new Exception('User, event, and role type are required');
            }

            // Check if assignment already exists
            $stmt = $pdo->prepare("
                SELECT id FROM event_assignments
                WHERE user_id = ? AND event_id = ? AND role_type = ? AND is_active = 1
            ");
            $stmt->execute([$user_id, $event_id, $role_type]);

            if ($stmt->fetch()) {
                throw new Exception('User is already assigned to this event with this role');
            }

            // Create event assignment
            $stmt = $pdo->prepare("
                INSERT INTO event_assignments (user_id, event_id, role_type, assigned_at, is_active)
                VALUES (?, ?, ?, NOW(), 1)
            ");
            $stmt->execute([$user_id, $event_id, $role_type]);

            // Send event assignment notification email
            require_once '../includes/admin_email_notifications.php';
            try {
                $emailSent = sendEventAssignmentNotification($user_id, $event_id, $role_type, $_SESSION['admin_id']);
                if ($emailSent) {
                    $message = "Event assignment created successfully! Notification email sent.";
                } else {
                    $message = "Event assignment created successfully! (Note: Notification email could not be sent)";
                }
            } catch (Exception $e) {
                error_log("Failed to send event assignment notification: " . $e->getMessage());
                $message = "Event assignment created successfully! (Note: Notification email could not be sent)";
            }

        } elseif ($_POST['action'] === 'extend_role_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';
            $new_expiry = $_POST['new_expiry'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE user_role_assignments SET expires_at = ? WHERE id = ?");
            $stmt->execute([$new_expiry ?: null, $assignment_id]);

            $message = $new_expiry ? "Role assignment extended successfully!" : "Role assignment set to permanent!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Check if RBAC system is already set up
$rbac_setup = false;
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_roles");
    $role_count = $stmt->fetchColumn();
    $rbac_setup = ($role_count > 0);
} catch (PDOException $e) {
    $rbac_setup = false;
}

// Get all admins for role assignment
$admins = [];
try {
    $stmt = $pdo->query("SELECT id, username, email FROM admins ORDER BY username");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
}

// Get all roles
$roles = [];
if ($rbac_setup) {
    $stmt = $pdo->query("SELECT * FROM user_roles ORDER BY hierarchy_level ASC");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get all events
$events = [];
try {
    $stmt = $pdo->query("SELECT id, title, event_date FROM events WHERE is_active = 1 ORDER BY event_date DESC");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
}

// Get all active future sessions (not expired)
// Filter sessions based on user's event assignments if not super admin
$sessions = [];
try {
    // Check if current user is super admin
    $is_super_admin = ($_SESSION['admin_role'] ?? '') === 'super_admin';

    if ($is_super_admin) {
        // Super admin can see all sessions
        $stmt = $pdo->query("
            SELECT es.id, es.session_title, es.start_datetime, es.end_datetime, e.title as event_title, e.id as event_id
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            WHERE es.status = 'active'
            AND e.is_active = 1
            AND (es.end_datetime > NOW() OR (es.end_datetime IS NULL AND es.start_datetime > NOW()))
            ORDER BY es.start_datetime ASC
        ");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Regular admins only see sessions from events they are assigned to
        $current_user_id = $_SESSION['admin_id'] ?? 0;
        $stmt = $pdo->prepare("
            SELECT DISTINCT es.id, es.session_title, es.start_datetime, es.end_datetime, e.title as event_title, e.id as event_id
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            JOIN event_assignments ea ON e.id = ea.event_id
            WHERE es.status = 'active'
            AND e.is_active = 1
            AND ea.user_id = ?
            AND ea.is_active = 1
            AND (es.end_datetime > NOW() OR (es.end_datetime IS NULL AND es.start_datetime > NOW()))
            ORDER BY es.start_datetime ASC
        ");
        $stmt->execute([$current_user_id]);
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // Tables might not exist yet
    $sessions = [];
}

// Get current role assignments
$current_assignments = [];
if ($rbac_setup) {
    try {
        $stmt = $pdo->query("
            SELECT ura.id, ura.user_id, ura.role_id, ura.assigned_at, ura.expires_at,
                   ur.role_display_name, ur.role_name, a.username, a.email
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            JOIN admins a ON ura.user_id = a.id
            WHERE ura.is_active = 1
            ORDER BY a.username, ur.hierarchy_level
        ");
        $current_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Handle error
    }
}

// Page title and header info
$page_title = 'RBAC System Setup';
$page_header = 'Role-Based Access Control Setup';
$page_description = 'Configure hierarchical dashboard roles and permissions';

// Include header
include 'includes/header.php';
?>

<style>
.setup-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}
.setup-card.completed {
    border-color: #28a745;
    background-color: #f8fff9;
}
.role-hierarchy {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.assignment-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    position: relative;
}
.assignment-card:hover {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}
.btn-group-vertical .btn {
    margin-bottom: 2px;
}
.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
.assignment-card .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
.modal-body .alert {
    margin-bottom: 1rem;
}
.expired-session {
    opacity: 0.7;
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
}
.expired-session:hover {
    background-color: #e9ecef !important;
    border-color: #adb5bd !important;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="bi bi-shield-check"></i> RBAC System Setup</h2>
                <p class="text-muted mb-0">Configure Role-Based Access Control for Multi-Tier Dashboard Architecture</p>
            </div>
            <div>
                <a href="event_attendance.php" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Events
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Setup Required Message -->
<?php if (isset($_GET['setup_required'])): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>RBAC System Setup Required!</strong>
        <p class="mb-0">The Role-Based Access Control system must be initialized before you can access the Super Admin Dashboard. Please click the "Initialize RBAC System" button below.</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- RBAC System Status -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card setup-card <?php echo $rbac_setup ? 'completed' : ''; ?>">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-<?php echo $rbac_setup ? 'check-circle text-success' : 'exclamation-triangle text-warning'; ?>"></i>
                    RBAC System Status
                </h5>
            </div>
            <div class="card-body">
                <?php if ($rbac_setup): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <strong>RBAC System is Active</strong>
                        <p class="mb-0">The role-based access control system is set up and running. You can now assign roles and manage permissions.</p>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>RBAC System Not Set Up</strong>
                        <p class="mb-2">The role-based access control system needs to be initialized. This will create the necessary database tables and default roles.</p>
                        <a href="initialize_rbac_database.php" class="btn btn-warning">
                            <i class="bi bi-gear"></i> Initialize RBAC System
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($rbac_setup): ?>
<!-- Role Hierarchy Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card role-hierarchy">
            <div class="card-body">
                <h5 class="card-title text-white mb-3">
                    <i class="bi bi-diagram-3"></i> Dashboard Hierarchy
                </h5>
                <div class="row text-center">
                    <?php foreach ($roles as $role): ?>
                        <div class="col-md-<?php echo 12 / count($roles); ?>">
                            <div class="mb-2">
                                <i class="bi bi-person-badge fs-2 d-block"></i>
                                <strong><?php echo htmlspecialchars($role['role_display_name']); ?></strong>
                                <br>
                                <small>Level <?php echo $role['hierarchy_level']; ?></small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-white-50">
                        <i class="bi bi-info-circle"></i>
                        Lower hierarchy levels have higher privileges. Super Admin (Level 1) can access all dashboards.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role Assignment -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus"></i> Assign User Roles
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="assign_role">
                    <div class="mb-3">
                        <label class="form-label">Select User</label>
                        <select class="form-select" name="user_id" required>
                            <option value="">Choose user...</option>
                            <?php foreach ($admins as $admin): ?>
                                <option value="<?php echo $admin['id']; ?>">
                                    <?php echo htmlspecialchars($admin['username']); ?>
                                    <?php if ($admin['email']): ?>
                                        (<?php echo htmlspecialchars($admin['email']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Role</label>
                        <select class="form-select" name="role_id" required>
                            <option value="">Choose role...</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>">
                                    <?php echo htmlspecialchars($role['role_display_name']); ?>
                                    (Level <?php echo $role['hierarchy_level']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expires At (Optional)</label>
                        <input type="datetime-local" class="form-control" name="expires_at">
                        <small class="text-muted">Leave empty for permanent assignment</small>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus"></i> Assign Role
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> Current Role Assignments
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($current_assignments)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                        <p>No role assignments found.</p>
                    </div>
                <?php else: ?>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($current_assignments as $assignment): ?>
                            <div class="assignment-card">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong><?php echo htmlspecialchars($assignment['username']); ?></strong>
                                        <?php if ($assignment['email']): ?>
                                            <small class="text-muted">(<?php echo htmlspecialchars($assignment['email']); ?>)</small>
                                        <?php endif; ?>
                                        <br>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($assignment['role_display_name']); ?></span>
                                        <br>
                                        <small class="text-muted">
                                            Assigned: <?php echo date('M j, Y g:i A', strtotime($assignment['assigned_at'])); ?>
                                        </small>
                                        <?php if ($assignment['expires_at']): ?>
                                            <br>
                                            <small class="text-warning">
                                                <i class="bi bi-clock"></i> Expires: <?php echo date('M j, Y g:i A', strtotime($assignment['expires_at'])); ?>
                                            </small>
                                        <?php else: ?>
                                            <br>
                                            <small class="text-success">
                                                <i class="bi bi-infinity"></i> Permanent
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <!-- Extend/Modify Assignment -->
                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#extendRoleModal<?php echo $assignment['id']; ?>">
                                                <i class="bi bi-clock-history"></i> Extend
                                            </button>

                                            <!-- Delete Assignment -->
                                            <?php
                                            // PROTECTION: Hide remove button for primary super admin
                                            $is_primary_super_admin = ($assignment['role_name'] === 'super_admin' &&
                                                                     ($assignment['email'] === '<EMAIL>' || $assignment['username'] === 'superadmin'));
                                            ?>
                                            <?php if (!$is_primary_super_admin): ?>
                                                <form method="POST" style="display: inline;"
                                                      onsubmit="return confirm('Remove role assignment for <?php echo htmlspecialchars($assignment['username']); ?>?')">
                                                    <input type="hidden" name="action" value="delete_role_assignment">
                                                    <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                                    <button type="submit" class="btn btn-outline-danger btn-sm">
                                                        <i class="bi bi-trash"></i> Remove
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-outline-secondary btn-sm" disabled title="Primary super admin cannot be removed">
                                                    <i class="bi bi-shield-lock"></i> Protected
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Extend Role Modal -->
                            <div class="modal fade" id="extendRoleModal<?php echo $assignment['id']; ?>" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">
                                                <i class="bi bi-clock-history"></i> Extend Role Assignment
                                            </h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <input type="hidden" name="action" value="extend_role_assignment">
                                                <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">

                                                <p><strong>User:</strong> <?php echo htmlspecialchars($assignment['username']); ?></p>
                                                <p><strong>Role:</strong> <?php echo htmlspecialchars($assignment['role_display_name']); ?></p>

                                                <div class="mb-3">
                                                    <label class="form-label">New Expiry Date (Optional)</label>
                                                    <input type="datetime-local" class="form-control" name="new_expiry"
                                                           value="<?php echo $assignment['expires_at'] ? date('Y-m-d\TH:i', strtotime($assignment['expires_at'])) : ''; ?>">
                                                    <small class="text-muted">Leave empty to make permanent</small>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="bi bi-check"></i> Update Assignment
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Event Assignment Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-check"></i> Event Assignments for Coordinators & Organizers
                </h5>
            </div>
            <div class="card-body">
                <?php
                // Get all events for assignment
                $events = [];
                try {
                    $stmt = $pdo->query("
                        SELECT e.id, e.title, e.event_date, e.location,
                               COUNT(es.id) as session_count
                        FROM events e
                        LEFT JOIN event_sessions es ON e.id = es.event_id
                        WHERE e.is_active = 1
                        GROUP BY e.id, e.title, e.event_date, e.location
                        ORDER BY e.event_date DESC
                    ");
                    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (PDOException $e) {
                    $events = [];
                }
                ?>

                <?php if (empty($events)): ?>
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> No Events Available</h6>
                        <p class="mb-2">No events found for assignment. Create events first to assign coordinators and organizers.</p>
                        <div class="mt-3">
                            <a href="events.php" class="btn btn-primary">
                                <i class="bi bi-calendar-plus"></i> Create Events
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person-plus"></i> Assign Event Roles</h6>
                            <form method="POST">
                                <input type="hidden" name="action" value="assign_event">

                                <div class="mb-3">
                                    <label for="event_user_id" class="form-label">Select User</label>
                                    <select class="form-select" id="event_user_id" name="user_id" required>
                                        <option value="">Choose a user...</option>
                                        <?php foreach ($admins as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($user['email']): ?>
                                                    (<?php echo htmlspecialchars($user['email']); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="event_id" class="form-label">Select Event</label>
                                    <select class="form-select" id="event_id" name="event_id" required>
                                        <option value="">Choose an event...</option>
                                        <?php foreach ($events as $event): ?>
                                            <option value="<?php echo $event['id']; ?>">
                                                <?php echo htmlspecialchars($event['title']); ?>
                                                (<?php echo date('M j, Y', strtotime($event['event_date'])); ?>)
                                                - <?php echo $event['session_count']; ?> sessions
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="role_type" class="form-label">Role Type</label>
                                    <select class="form-select" id="role_type" name="role_type" required>
                                        <option value="">Choose role...</option>
                                        <option value="coordinator">Event Coordinator</option>
                                        <option value="organizer">Event Organizer</option>
                                    </select>
                                </div>

                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-plus-circle"></i> Assign Event Role
                                </button>
                            </form>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="bi bi-list-check"></i> Current Event Assignments</h6>
                            <?php
                            // Get current event assignments
                            try {
                                $stmt = $pdo->query("
                                    SELECT ea.id, ea.user_id, ea.event_id, ea.role_type, a.username, a.email,
                                           e.title as event_title, e.event_date, e.location,
                                           ea.assigned_at, ea.is_active
                                    FROM event_assignments ea
                                    JOIN admins a ON ea.user_id = a.id
                                    JOIN events e ON ea.event_id = e.id
                                    WHERE ea.is_active = 1
                                    ORDER BY e.event_date DESC, a.username
                                ");
                                $event_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (PDOException $e) {
                                $event_assignments = [];
                            }
                            ?>

                            <?php if (empty($event_assignments)): ?>
                                <div class="text-center text-muted py-3">
                                    <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                                    <p>No event assignments found.</p>
                                </div>
                            <?php else: ?>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach ($event_assignments as $assignment): ?>
                                        <div class="assignment-card">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <strong><?php echo htmlspecialchars($assignment['username']); ?></strong>
                                                    <?php if ($assignment['email']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($assignment['email']); ?></small>
                                                    <?php endif; ?>
                                                    <br>
                                                    <span class="badge <?php echo $assignment['role_type'] === 'coordinator' ? 'bg-primary' : 'bg-success'; ?>">
                                                        <?php echo ucfirst($assignment['role_type']); ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-calendar-event"></i> <?php echo htmlspecialchars($assignment['event_title']); ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-info">
                                                        <i class="bi bi-clock"></i> <?php echo date('M j, Y', strtotime($assignment['event_date'])); ?>
                                                        <?php if ($assignment['location']): ?>
                                                            | <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($assignment['location']); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                                        <!-- View Event Details -->
                                                        <a href="event_attendance_detail.php?event_id=<?php echo $assignment['event_id']; ?>"
                                                           class="btn btn-outline-info btn-sm" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>

                                                        <!-- Remove Assignment -->
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Remove event assignment for <?php echo htmlspecialchars($assignment['username']); ?>?')">
                                                            <input type="hidden" name="action" value="delete_event_assignment">
                                                            <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                                <i class="bi bi-trash"></i> Remove
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Session Assignment Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Session Assignments for Session Moderators
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($sessions)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>No Future Sessions Available</strong>
                        <p class="mb-2">No active future sessions found. Only upcoming sessions can be assigned to moderators. Expired or past sessions are not available for new assignments.</p>
                        <div class="d-flex gap-2">
                            <a href="event_sessions.php" class="btn btn-info">
                                <i class="bi bi-plus-circle"></i> Create New Sessions
                            </a>
                            <a href="events.php" class="btn btn-outline-info">
                                <i class="bi bi-calendar-plus"></i> Create Events
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person-check"></i> Assign Session to Moderator</h6>
                            <form method="POST">
                                <input type="hidden" name="action" value="assign_session">
                                <div class="mb-3">
                                    <label class="form-label">Select Session Moderator</label>
                                    <select class="form-select" name="user_id" required>
                                        <option value="">Choose moderator...</option>
                                        <?php
                                        // Get ALL admin users (not just those with roles assigned)
                                        // This allows super admin to assign sessions to any user
                                        $stmt = $pdo->query("
                                            SELECT DISTINCT a.id, a.username, a.email,
                                                   COALESCE(ur.role_display_name, 'No Role Assigned') as role_display_name,
                                                   COALESCE(ur.hierarchy_level, 999) as hierarchy_level
                                            FROM admins a
                                            LEFT JOIN user_role_assignments ura ON a.id = ura.user_id AND ura.is_active = 1
                                            LEFT JOIN user_roles ur ON ura.role_id = ur.id
                                            ORDER BY COALESCE(ur.hierarchy_level, 999) ASC, a.username
                                        ");
                                        $moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                        ?>
                                        <?php foreach ($moderators as $moderator): ?>
                                            <option value="<?php echo $moderator['id']; ?>">
                                                <?php echo htmlspecialchars($moderator['username']); ?>
                                                (<?php echo htmlspecialchars($moderator['role_display_name']); ?>)
                                                <?php if ($moderator['email']): ?>
                                                    - <?php echo htmlspecialchars($moderator['email']); ?>
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if (empty($moderators)): ?>
                                        <small class="text-muted">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            No admin users found. Create admin users first.
                                        </small>
                                    <?php else: ?>
                                        <small class="text-info">
                                            <i class="bi bi-info-circle"></i>
                                            You can assign sessions to any admin user, even without a role. Users will be redirected to their assigned sessions when they log in.
                                        </small>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Select Session</label>
                                    <select class="form-select" name="session_id" required>
                                        <option value="">Choose session...</option>
                                        <?php foreach ($sessions as $session): ?>
                                            <option value="<?php echo $session['id']; ?>">
                                                <?php echo htmlspecialchars($session['session_title']); ?>
                                                (<?php echo htmlspecialchars($session['event_title']); ?> -
                                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-success" <?php echo empty($moderators) ? 'disabled' : ''; ?>>
                                    <i class="bi bi-check-circle"></i> Assign Session
                                </button>
                            </form>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="bi bi-list-check"></i> Current Session Assignments</h6>
                            <?php
                            // Get current session assignments (both active and expired for management)
                            try {
                                $stmt = $pdo->query("
                                    SELECT sa.id, sa.user_id, sa.session_id, a.username, a.email,
                                           es.session_title, e.title as event_title,
                                           es.start_datetime, es.end_datetime, sa.assigned_at,
                                           CASE
                                               WHEN es.end_datetime IS NOT NULL AND es.end_datetime < NOW() THEN 'expired'
                                               WHEN es.end_datetime IS NULL AND es.start_datetime < NOW() THEN 'expired'
                                               ELSE 'active'
                                           END as session_status
                                    FROM session_assignments sa
                                    JOIN admins a ON sa.user_id = a.id
                                    JOIN event_sessions es ON sa.session_id = es.id
                                    JOIN events e ON es.event_id = e.id
                                    WHERE sa.is_active = 1
                                    ORDER BY
                                        CASE
                                            WHEN es.end_datetime IS NOT NULL AND es.end_datetime < NOW() THEN 1
                                            WHEN es.end_datetime IS NULL AND es.start_datetime < NOW() THEN 1
                                            ELSE 0
                                        END,
                                        es.start_datetime ASC
                                ");
                                $session_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (PDOException $e) {
                                $session_assignments = [];
                            }
                            ?>

                            <?php if (empty($session_assignments)): ?>
                                <div class="text-center text-muted py-3">
                                    <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                                    <p>No session assignments found.</p>
                                </div>
                            <?php else: ?>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach ($session_assignments as $assignment): ?>
                                        <div class="assignment-card <?php echo $assignment['session_status'] === 'expired' ? 'expired-session' : ''; ?>">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center mb-1">
                                                        <strong><?php echo htmlspecialchars($assignment['username']); ?></strong>
                                                        <?php if ($assignment['email']): ?>
                                                            <small class="text-muted ms-2">(<?php echo htmlspecialchars($assignment['email']); ?>)</small>
                                                        <?php endif; ?>
                                                        <?php if ($assignment['session_status'] === 'expired'): ?>
                                                            <span class="badge bg-secondary ms-2">
                                                                <i class="bi bi-clock-history"></i> Expired
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="badge bg-success ms-2">
                                                                <i class="bi bi-check-circle"></i> Active
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <span class="badge <?php echo $assignment['session_status'] === 'expired' ? 'bg-light text-dark' : 'bg-primary'; ?>">
                                                        <?php echo htmlspecialchars($assignment['session_title']); ?>
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-calendar-event"></i> <?php echo htmlspecialchars($assignment['event_title']); ?>
                                                    </small>
                                                    <br>
                                                    <small class="<?php echo $assignment['session_status'] === 'expired' ? 'text-muted' : 'text-info'; ?>">
                                                        <i class="bi bi-clock"></i> <?php echo date('M j, Y g:i A', strtotime($assignment['start_datetime'])); ?>
                                                        <?php if ($assignment['end_datetime']): ?>
                                                            - <?php echo date('g:i A', strtotime($assignment['end_datetime'])); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person-check"></i> Assigned: <?php echo date('M j, Y', strtotime($assignment['assigned_at'])); ?>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                                        <!-- View Session Details -->
                                                        <a href="session_attendance.php?session_id=<?php echo $assignment['session_id']; ?>"
                                                           class="btn btn-outline-info btn-sm" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>

                                                        <!-- Reassign Session (disabled for expired sessions) -->
                                                        <?php if ($assignment['session_status'] === 'expired'): ?>
                                                            <button type="button" class="btn btn-outline-secondary btn-sm" disabled
                                                                    title="Cannot reassign expired sessions">
                                                                <i class="bi bi-arrow-repeat"></i> Reassign
                                                            </button>
                                                        <?php else: ?>
                                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#reassignSessionModal<?php echo $assignment['id']; ?>">
                                                                <i class="bi bi-arrow-repeat"></i> Reassign
                                                            </button>
                                                        <?php endif; ?>

                                                        <!-- Remove Assignment -->
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Remove session assignment for <?php echo htmlspecialchars($assignment['username']); ?>?<?php echo $assignment['session_status'] === 'expired' ? ' (This session has already expired)' : ''; ?>')">
                                                            <input type="hidden" name="action" value="delete_session_assignment">
                                                            <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                                <i class="bi bi-trash"></i> <?php echo $assignment['session_status'] === 'expired' ? 'Archive' : 'Remove'; ?>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Reassign Session Modal -->
                                        <div class="modal fade" id="reassignSessionModal<?php echo $assignment['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">
                                                            <i class="bi bi-arrow-repeat"></i> Reassign Session
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="action" value="assign_session">
                                                            <input type="hidden" name="session_id" value="<?php echo $assignment['session_id']; ?>">

                                                            <div class="alert alert-info">
                                                                <strong>Current Assignment:</strong><br>
                                                                <?php echo htmlspecialchars($assignment['username']); ?> →
                                                                <?php echo htmlspecialchars($assignment['session_title']); ?>
                                                            </div>

                                                            <div class="mb-3">
                                                                <label class="form-label">Reassign to New Moderator</label>
                                                                <select class="form-select" name="user_id" required>
                                                                    <option value="">Choose new moderator...</option>
                                                                    <?php
                                                                    // Get ALL admin users (excluding current) for reassignment
                                                                    $stmt = $pdo->prepare("
                                                                        SELECT DISTINCT a.id, a.username, a.email,
                                                                               COALESCE(ur.role_display_name, 'No Role Assigned') as role_display_name
                                                                        FROM admins a
                                                                        LEFT JOIN user_role_assignments ura ON a.id = ura.user_id AND ura.is_active = 1
                                                                        LEFT JOIN user_roles ur ON ura.role_id = ur.id
                                                                        WHERE a.id != ?
                                                                        ORDER BY a.username
                                                                    ");
                                                                    $stmt->execute([$assignment['user_id']]);
                                                                    $other_moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                                                    ?>
                                                                    <?php foreach ($other_moderators as $moderator): ?>
                                                                        <option value="<?php echo $moderator['id']; ?>">
                                                                            <?php echo htmlspecialchars($moderator['username']); ?>
                                                                            (<?php echo htmlspecialchars($moderator['role_display_name']); ?>)
                                                                            <?php if ($moderator['email']): ?>
                                                                                - <?php echo htmlspecialchars($moderator['email']); ?>
                                                                            <?php endif; ?>
                                                                        </option>
                                                                    <?php endforeach; ?>
                                                                </select>
                                                                <?php if (empty($other_moderators)): ?>
                                                                    <small class="text-muted">
                                                                        <i class="bi bi-exclamation-triangle"></i>
                                                                        No other session moderators available.
                                                                    </small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-warning" <?php echo empty($other_moderators) ? 'disabled' : ''; ?>>
                                                                <i class="bi bi-arrow-repeat"></i> Reassign Session
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
