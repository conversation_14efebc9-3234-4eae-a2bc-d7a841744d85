<?php
/**
 * PWA (Progressive Web App) Entry Point
 * Mobile-optimized interface for church management
 */

// Start session
session_start();

// Include authentication and RBAC
require_once '../includes/auth_check.php';
require_once '../includes/rbac_access_control.php';

// Initialize RBAC
$rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);

// Check if user has permission to access PWA
if (!$rbac->canAccessPage('pwa/index.php')) {
    header('Location: ../access_denied.php?reason=You do not have permission to access the Mobile PWA');
    exit();
}

// Get user info
$user_name = $_SESSION['admin_name'] ?? $_SESSION['username'] ?? 'Admin User';
$user_role = $rbac->getPrimaryRole();

$page_title = 'Mobile PWA - Church Management';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo $page_title; ?></title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#007bff">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Church PWA">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .pwa-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .pwa-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .pwa-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2rem;
        }
        
        .btn-pwa {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            margin: 10px 0;
            width: 100%;
            transition: transform 0.2s;
        }
        
        .btn-pwa:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        
        .feature-item {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            transition: transform 0.2s;
        }
        
        .feature-item:hover {
            transform: translateY(-2px);
        }
        
        .feature-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .status-badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="pwa-container">
        <div class="pwa-card">
            <div class="pwa-icon">
                <i class="bi bi-phone"></i>
            </div>
            
            <h2 class="mb-3">Mobile PWA</h2>
            <div class="status-badge">Coming Soon</div>
            
            <p class="text-muted mb-4">
                Welcome, <strong><?php echo htmlspecialchars($user_name); ?></strong><br>
                <small>Role: <?php echo ucfirst(str_replace('_', ' ', $user_role)); ?></small>
            </p>
            
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                The Progressive Web App is currently under development. For now, you can access all features through the main admin dashboard.
            </div>
            
            <a href="../dashboard.php" class="btn btn-pwa">
                <i class="bi bi-speedometer2"></i>
                Go to Main Dashboard
            </a>
            
            <a href="../events.php" class="btn btn-outline-primary">
                <i class="bi bi-calendar-event"></i>
                Manage Events
            </a>
            
            <div class="feature-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="bi bi-qr-code-scan"></i>
                    </div>
                    <h6>QR Scanner</h6>
                    <small class="text-muted">Quick check-in</small>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h6>Analytics</h6>
                    <small class="text-muted">Real-time data</small>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="bi bi-bell"></i>
                    </div>
                    <h6>Notifications</h6>
                    <small class="text-muted">Stay updated</small>
                </div>
                
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="bi bi-wifi-off"></i>
                    </div>
                    <h6>Offline Mode</h6>
                    <small class="text-muted">Work anywhere</small>
                </div>
            </div>
            
            <div class="mt-4 pt-3 border-top">
                <small class="text-muted">
                    <i class="bi bi-shield-check"></i>
                    Secure access for <?php echo htmlspecialchars($user_role); ?> users
                </small>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Basic PWA functionality
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                console.log('PWA: Service worker support detected');
                // Service worker registration would go here when implemented
            });
        }
        
        // Add to home screen prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            console.log('PWA: Install prompt available');
        });
        
        // Handle install button click
        function installPWA() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        console.log('PWA: User accepted the install prompt');
                    }
                    deferredPrompt = null;
                });
            }
        }
        
        // Check if running as PWA
        if (window.matchMedia('(display-mode: standalone)').matches || 
            window.navigator.standalone === true) {
            console.log('PWA: Running in standalone mode');
            document.body.classList.add('pwa-standalone');
        }
    </script>
</body>
</html>
