/**
 * Universal Event Manager PWA Service Worker
 * Provides offline functionality, caching, and background sync
 */

const CACHE_NAME = 'universal-event-manager-v1.0.0';
const OFFLINE_URL = './offline.html';

// Files to cache for offline functionality (using relative paths)
const CACHE_URLS = [
    './',
    './index.php',
    './offline.html',
    './css/pwa-styles.css',
    './js/pwa-app.js',
    './js/qr-scanner.js',
    '../css/bootstrap.min.css',
    '../js/bootstrap.bundle.min.js',
    './icons/icon-192x192.png',
    './icons/icon-512x512.png'
];

// API endpoints that should be cached (using relative paths)
const API_CACHE_URLS = [
    '../api/get_sessions.php',
    '../api/get_events.php'
];

// Install event - cache essential files
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('Service Worker: Caching essential files');
                return cache.addAll(CACHE_URLS);
            })
            .then(() => {
                console.log('Service Worker: Installation complete');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Installation failed', error);
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('Service Worker: Deleting old cache', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('Service Worker: Activation complete');
                return self.clients.claim();
            })
    );
});

// Fetch event - serve cached content when offline
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Handle navigation requests
    if (request.mode === 'navigate') {
        event.respondWith(
            fetch(request)
                .then(response => {
                    // Cache successful navigation responses
                    if (response.status === 200) {
                        const responseClone = response.clone();
                        caches.open(CACHE_NAME)
                            .then(cache => cache.put(request, responseClone));
                    }
                    return response;
                })
                .catch(() => {
                    // Serve offline page when navigation fails
                    return caches.match(OFFLINE_URL);
                })
        );
        return;
    }
    
    // Handle API requests with cache-first strategy for GET requests
    if (url.pathname.includes('/api/') && request.method === 'GET') {
        event.respondWith(
            caches.match(request)
                .then(cachedResponse => {
                    if (cachedResponse) {
                        // Serve from cache and update in background
                        fetch(request)
                            .then(response => {
                                if (response.status === 200) {
                                    const responseClone = response.clone();
                                    caches.open(CACHE_NAME)
                                        .then(cache => cache.put(request, responseClone));
                                }
                            })
                            .catch(() => {
                                // Network failed, cached version is still valid
                            });
                        return cachedResponse;
                    }
                    
                    // Not in cache, fetch from network
                    return fetch(request)
                        .then(response => {
                            if (response.status === 200) {
                                const responseClone = response.clone();
                                caches.open(CACHE_NAME)
                                    .then(cache => cache.put(request, responseClone));
                            }
                            return response;
                        });
                })
        );
        return;
    }
    
    // Handle POST requests (attendance updates, etc.)
    if (request.method === 'POST') {
        event.respondWith(
            fetch(request)
                .catch(() => {
                    // Store failed POST requests for background sync
                    return storeFailedRequest(request)
                        .then(() => {
                            return new Response(
                                JSON.stringify({
                                    success: false,
                                    offline: true,
                                    message: 'Request stored for sync when online'
                                }),
                                {
                                    status: 202,
                                    headers: { 'Content-Type': 'application/json' }
                                }
                            );
                        });
                })
        );
        return;
    }
    
    // Handle other requests with cache-first strategy
    event.respondWith(
        caches.match(request)
            .then(cachedResponse => {
                return cachedResponse || fetch(request)
                    .then(response => {
                        // Cache successful responses
                        if (response.status === 200) {
                            const responseClone = response.clone();
                            caches.open(CACHE_NAME)
                                .then(cache => cache.put(request, responseClone));
                        }
                        return response;
                    })
                    .catch(() => {
                        // Return a generic offline response for failed requests
                        return new Response(
                            'Offline - Content not available',
                            { status: 503, statusText: 'Service Unavailable' }
                        );
                    });
            })
    );
});

// Background sync for failed requests
self.addEventListener('sync', event => {
    console.log('Service Worker: Background sync triggered', event.tag);
    
    if (event.tag === 'attendance-sync') {
        event.waitUntil(syncFailedRequests());
    }
});

// Push notification handling
self.addEventListener('push', event => {
    console.log('Service Worker: Push notification received');
    
    const options = {
        body: 'You have new event updates',
        icon: '/campaign/church/admin/pwa/icons/icon-192x192.png',
        badge: '/campaign/church/admin/pwa/icons/badge-72x72.png',
        vibrate: [200, 100, 200],
        data: {
            url: '/campaign/church/admin/pwa/'
        },
        actions: [
            {
                action: 'open',
                title: 'Open App',
                icon: '/campaign/church/admin/pwa/icons/open-24x24.png'
            },
            {
                action: 'dismiss',
                title: 'Dismiss',
                icon: '/campaign/church/admin/pwa/icons/close-24x24.png'
            }
        ]
    };
    
    if (event.data) {
        const data = event.data.json();
        options.body = data.message || options.body;
        options.data = { ...options.data, ...data };
    }
    
    event.waitUntil(
        self.registration.showNotification('Universal Event Manager', options)
    );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
    console.log('Service Worker: Notification clicked');
    
    event.notification.close();
    
    if (event.action === 'dismiss') {
        return;
    }
    
    const urlToOpen = event.notification.data?.url || './';

    event.waitUntil(
        clients.matchAll({ type: 'window', includeUncontrolled: true })
            .then(clientList => {
                // Check if app is already open
                for (const client of clientList) {
                    if (client.url.includes('/admin/') && 'focus' in client) {
                        return client.focus();
                    }
                }
                
                // Open new window if app is not open
                if (clients.openWindow) {
                    return clients.openWindow(urlToOpen);
                }
            })
    );
});

// Message handling from main app
self.addEventListener('message', event => {
    console.log('Service Worker: Message received', event.data);
    
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_UPDATE') {
        // Update cache with new data
        updateCache(event.data.url, event.data.data);
    }
});

// Helper functions
async function storeFailedRequest(request) {
    try {
        const requestData = {
            url: request.url,
            method: request.method,
            headers: Object.fromEntries(request.headers.entries()),
            body: await request.text(),
            timestamp: Date.now()
        };
        
        const db = await openDB();
        const transaction = db.transaction(['failed_requests'], 'readwrite');
        const store = transaction.objectStore('failed_requests');
        await store.add(requestData);
        
        // Register background sync
        await self.registration.sync.register('attendance-sync');
        
        console.log('Service Worker: Failed request stored for sync');
    } catch (error) {
        console.error('Service Worker: Failed to store request', error);
    }
}

async function syncFailedRequests() {
    try {
        const db = await openDB();
        const transaction = db.transaction(['failed_requests'], 'readwrite');
        const store = transaction.objectStore('failed_requests');
        const requests = await store.getAll();
        
        for (const requestData of requests) {
            try {
                const response = await fetch(requestData.url, {
                    method: requestData.method,
                    headers: requestData.headers,
                    body: requestData.body
                });
                
                if (response.ok) {
                    // Request succeeded, remove from store
                    await store.delete(requestData.id);
                    console.log('Service Worker: Synced failed request', requestData.url);
                }
            } catch (error) {
                console.error('Service Worker: Failed to sync request', error);
            }
        }
    } catch (error) {
        console.error('Service Worker: Background sync failed', error);
    }
}

async function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('EventManagerDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            if (!db.objectStoreNames.contains('failed_requests')) {
                const store = db.createObjectStore('failed_requests', { 
                    keyPath: 'id', 
                    autoIncrement: true 
                });
                store.createIndex('timestamp', 'timestamp');
            }
        };
    });
}

async function updateCache(url, data) {
    try {
        const cache = await caches.open(CACHE_NAME);
        const response = new Response(JSON.stringify(data), {
            headers: { 'Content-Type': 'application/json' }
        });
        await cache.put(url, response);
        console.log('Service Worker: Cache updated for', url);
    } catch (error) {
        console.error('Service Worker: Failed to update cache', error);
    }
}

console.log('Service Worker: Loaded and ready');
