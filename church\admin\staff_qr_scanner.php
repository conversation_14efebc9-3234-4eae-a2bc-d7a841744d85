<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

require_once '../config.php';

$message = '';
$error = '';
$scan_result = null;

// Handle QR code scan result
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['qr_token'])) {
    $qr_token = trim($_POST['qr_token']);
    
    if (!empty($qr_token)) {
        try {
            // Check if it's a session-specific QR code
            $stmt = $pdo->prepare("
                SELECT 
                    sqr.*,
                    s.session_title,
                    s.start_datetime,
                    s.end_datetime,
                    s.location,
                    e.title as event_title
                FROM session_attendance_qr_codes sqr
                JOIN event_sessions s ON sqr.session_id = s.id
                JOIN events e ON s.event_id = e.id
                WHERE sqr.qr_token = ? AND sqr.expires_at > NOW()
            ");
            $stmt->execute([$qr_token]);
            $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($qr_data) {
                $scan_result = [
                    'type' => 'session_specific',
                    'data' => $qr_data,
                    'valid' => true
                ];
                
                // Check if already used
                if ($qr_data['is_used']) {
                    $scan_result['already_used'] = true;
                    $message = "⚠️ QR code already used for check-in at " . date('M j, Y g:i A', strtotime($qr_data['used_at']));
                } else {
                    $message = "✅ Valid session QR code detected!";
                }
            } else {
                // Check legacy systems
                $stmt = $pdo->prepare("
                    SELECT 
                        'legacy_session' as type,
                        sqr.*,
                        s.session_title,
                        s.start_datetime,
                        e.title as event_title
                    FROM session_qr_codes sqr
                    JOIN event_sessions s ON sqr.session_id = s.id
                    JOIN events e ON s.event_id = e.id
                    WHERE sqr.qr_token = ? AND sqr.expires_at > NOW() AND sqr.is_active = 1
                ");
                $stmt->execute([$qr_token]);
                $legacy_data = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($legacy_data) {
                    $scan_result = [
                        'type' => 'legacy_session',
                        'data' => $legacy_data,
                        'valid' => true
                    ];
                    $message = "✅ Legacy session QR code detected!";
                } else {
                    // Check member QR codes
                    $stmt = $pdo->prepare("
                        SELECT 
                            'member_event' as type,
                            mqr.*,
                            e.title as event_title
                        FROM member_qr_codes mqr
                        JOIN events e ON mqr.event_id = e.id
                        WHERE mqr.qr_token = ?
                    ");
                    $stmt->execute([$qr_token]);
                    $member_data = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($member_data) {
                        $scan_result = [
                            'type' => 'member_event',
                            'data' => $member_data,
                            'valid' => true
                        ];
                        $message = "✅ Member event QR code detected!";
                    } else {
                        $error = "❌ Invalid or expired QR code";
                        $scan_result = ['valid' => false];
                    }
                }
            }
            
        } catch (Exception $e) {
            $error = "Database error: " . $e->getMessage();
        }
    } else {
        $error = "Please provide a QR code token";
    }
}

// Handle check-in action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'checkin') {
    $qr_token = $_POST['qr_token'];
    
    try {
        $pdo->beginTransaction();
        
        // Get QR code data
        $stmt = $pdo->prepare("
            SELECT sqr.*, s.id as session_id
            FROM session_attendance_qr_codes sqr
            JOIN event_sessions s ON sqr.session_id = s.id
            WHERE sqr.qr_token = ? AND sqr.expires_at > NOW()
        ");
        $stmt->execute([$qr_token]);
        $qr_data = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$qr_data) {
            throw new Exception("Invalid QR code");
        }
        
        if ($qr_data['is_used']) {
            throw new Exception("QR code already used");
        }
        
        // Mark QR code as used
        $stmt = $pdo->prepare("
            UPDATE session_attendance_qr_codes 
            SET is_used = 1, used_at = NOW() 
            WHERE qr_token = ?
        ");
        $stmt->execute([$qr_token]);
        
        // Update session attendance
        if ($qr_data['member_id']) {
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance 
                (session_id, member_id, attendance_status, registration_date, attendance_date)
                VALUES (?, ?, 'attended', NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                attendance_status = 'attended', attendance_date = NOW()
            ");
            $stmt->execute([$qr_data['session_id'], $qr_data['member_id']]);
        } else {
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance 
                (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                VALUES (?, ?, ?, 'attended', NOW(), NOW())
                ON DUPLICATE KEY UPDATE
                attendance_status = 'attended', attendance_date = NOW()
            ");
            $stmt->execute([$qr_data['session_id'], $qr_data['guest_name'], $qr_data['guest_email']]);
        }
        
        $pdo->commit();
        $message = "✅ {$qr_data['attendee_name']} checked in successfully!";
        $scan_result = null; // Clear scan result after successful check-in
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $error = "Check-in failed: " . $e->getMessage();
    }
}

// Set page variables for header (only title for browser tab)
$page_title = 'Staff QR Scanner';
// Don't set page_header and page_description to avoid duplicate titles
// $page_header = 'Staff QR Scanner';
// $page_description = 'Scan QR codes for event check-ins and member verification';

// Include the admin header
require_once 'includes/header.php';
?>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
<style>
        /* Remove custom body styles to use admin theme */
        .scanner-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0;
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .scanner-container {
                padding: 0;
            }

            .scanner-card {
                border-radius: 0;
                box-shadow: none;
                border: 1px solid #dee2e6;
            }

            .scanner-header {
                padding: 20px 15px;
            }

            .scanner-body {
                padding: 20px 15px;
            }

            .camera-container {
                padding: 15px;
                margin: 15px 0;
            }

            .manual-input {
                padding: 15px;
                margin: 15px 0;
            }

            .scan-result {
                padding: 15px;
                margin: 15px 0;
            }
        }
        .scanner-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .scanner-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .scanner-body {
            padding: 30px;
        }
        .camera-container {
            position: relative;
            background: #f8f9fa;
            border: 3px dashed #007bff;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        #video {
            width: 100%;
            max-width: 400px;
            border-radius: 10px;
        }
        .scan-result {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .manual-input {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
        }
        .btn-scanner {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .qr-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
        }
        @media (max-width: 576px) {
            .scanner-container {
                padding: 10px;
            }
            .scanner-body {
                padding: 20px;
            }
        }
    </style>

<!-- Scanner Content -->
<div class="container-fluid py-4 mb-5">
    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="scanner-container">
    <div class="scanner-card">
        <div class="scanner-header">
            <h1><i class="bi bi-qr-code-scan"></i> Staff QR Scanner</h1>
            <p class="mb-0">Scan attendee QR codes for instant check-in</p>
        </div>
        
        <div class="scanner-body">
            <!-- Messages -->
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <!-- Camera Scanner -->
            <div class="camera-container">
                <div id="camera-controls">
                    <i class="bi bi-camera text-primary" style="font-size: 4rem;"></i>
                    <h4 class="mt-3">Camera QR Scanner</h4>
                    <p class="text-muted">Position attendee's QR code in front of camera</p>
                    <button class="btn btn-primary btn-scanner" onclick="startCamera()">
                        <i class="bi bi-camera"></i> Start Camera
                    </button>
                </div>
                
                <div id="camera-preview" style="display: none;">
                    <video id="video" autoplay playsinline></video>
                    <canvas id="canvas" style="display: none;"></canvas>
                    <div class="mt-3">
                        <button class="btn btn-danger btn-scanner" onclick="stopCamera()">
                            <i class="bi bi-stop-circle"></i> Stop Camera
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Manual Input -->
            <div class="manual-input">
                <h5><i class="bi bi-keyboard"></i> Manual QR Code Entry</h5>
                <form method="POST">
                    <div class="input-group">
                        <input type="text" class="form-control" name="qr_token" 
                               placeholder="Enter QR code token manually..." 
                               value="<?php echo isset($_POST['qr_token']) ? htmlspecialchars($_POST['qr_token']) : ''; ?>">
                        <button class="btn btn-outline-primary" type="submit">
                            <i class="bi bi-search"></i> Scan
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- Scan Result -->
            <?php if ($scan_result && $scan_result['valid']): ?>
                <div class="scan-result">
                    <h5><i class="bi bi-check-circle text-success"></i> QR Code Scanned Successfully</h5>
                    
                    <?php if ($scan_result['type'] === 'session_specific'): ?>
                        <div class="qr-info">
                            <h6><strong><?php echo htmlspecialchars($scan_result['data']['attendee_name']); ?></strong></h6>
                            <p class="mb-1">
                                <strong>Session:</strong> <?php echo htmlspecialchars($scan_result['data']['session_title']); ?><br>
                                <strong>Event:</strong> <?php echo htmlspecialchars($scan_result['data']['event_title']); ?><br>
                                <strong>Email:</strong> <?php echo htmlspecialchars($scan_result['data']['attendee_email']); ?><br>
                                <strong>Type:</strong> <?php echo ucfirst($scan_result['data']['attendee_type']); ?>
                            </p>
                        </div>
                        
                        <?php if (!isset($scan_result['already_used'])): ?>
                            <form method="POST" class="mt-3">
                                <input type="hidden" name="action" value="checkin">
                                <input type="hidden" name="qr_token" value="<?php echo htmlspecialchars($scan_result['data']['qr_token']); ?>">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="bi bi-check-circle"></i> Check In Now
                                </button>
                            </form>
                        <?php endif; ?>
                        
                    <?php elseif ($scan_result['type'] === 'legacy_session'): ?>
                        <div class="qr-info">
                            <p><strong>Legacy Session QR Code</strong></p>
                            <p>Session: <?php echo htmlspecialchars($scan_result['data']['session_title']); ?></p>
                            <a href="mobile_checkin.php?token=<?php echo urlencode($scan_result['data']['qr_token']); ?>"
                               class="btn btn-warning">
                                <i class="bi bi-arrow-right"></i> Go to Session Check-in
                            </a>
                        </div>
                        
                    <?php elseif ($scan_result['type'] === 'member_event'): ?>
                        <div class="qr-info">
                            <p><strong>Member Event QR Code</strong></p>
                            <p>Event: <?php echo htmlspecialchars($scan_result['data']['event_title']); ?></p>
                            <a href="member_checkin.php?token=<?php echo urlencode($scan_result['data']['qr_token']); ?>" 
                               class="btn btn-info">
                                <i class="bi bi-arrow-right"></i> Go to Event Check-in
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <!-- Quick Actions -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <a href="multi_session_dashboard.php" class="btn btn-outline-primary w-100">
                        <i class="bi bi-grid"></i> Session Dashboard
                    </a>
                </div>
                <div class="col-md-6">
                    <a href="mobile_checkin.php" class="btn btn-outline-secondary w-100">
                        <i class="bi bi-phone"></i> Mobile Check-in
                    </a>
                </div>
            </div>
                </div>
            </div>
        </div>
            </div>
        </div>
    </div>

<script>
let video, canvas, context, scanning = false;

function startCamera() {
    video = document.getElementById('video');
    canvas = document.getElementById('canvas');
    context = canvas.getContext('2d');
    
    // Show camera preview
    document.getElementById('camera-controls').style.display = 'none';
    document.getElementById('camera-preview').style.display = 'block';
    
    // Request camera access
    navigator.mediaDevices.getUserMedia({ 
        video: { 
            facingMode: 'environment' // Use back camera if available
        } 
    })
    .then(function(stream) {
        video.srcObject = stream;
        video.play();
        scanning = true;
        scanQRCode();
    })
    .catch(function(err) {
        console.error('Error accessing camera:', err);
        alert('Unable to access camera. Please check permissions or use manual entry.');
        stopCamera();
    });
}

function stopCamera() {
    scanning = false;
    
    if (video && video.srcObject) {
        video.srcObject.getTracks().forEach(track => track.stop());
    }
    
    document.getElementById('camera-controls').style.display = 'block';
    document.getElementById('camera-preview').style.display = 'none';
}

function scanQRCode() {
    if (!scanning) return;
    
    if (video.readyState === video.HAVE_ENOUGH_DATA) {
        canvas.height = video.videoHeight;
        canvas.width = video.videoWidth;
        context.drawImage(video, 0, 0, canvas.width, canvas.height);
        
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const code = jsQR(imageData.data, imageData.width, imageData.height);
        
        if (code) {
            console.log('QR Code detected:', code.data);
            
            // Extract token from QR code data
            let token = '';
            
            if (code.data.includes('token=')) {
                // Extract from URL
                const url = new URL(code.data);
                token = url.searchParams.get('token');
            } else {
                // Assume the data is the token itself
                token = code.data;
            }
            
            if (token) {
                // Stop scanning and submit
                stopCamera();
                
                // Submit the form with the token
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `<input type="hidden" name="qr_token" value="${token}">`;
                document.body.appendChild(form);
                form.submit();
            } else {
                alert('Invalid QR code format. Please try again.');
            }
        }
    }
    
    if (scanning) {
        requestAnimationFrame(scanQRCode);
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Press 'S' to start scanner
    if (e.key === 's' || e.key === 'S') {
        if (!scanning) {
            startCamera();
        }
    }
    
    // Press 'Escape' to stop scanner
    if (e.key === 'Escape') {
        if (scanning) {
            stopCamera();
        }
    }
});

// Handle page visibility changes
document.addEventListener('visibilitychange', function() {
    if (document.hidden && scanning) {
        stopCamera();
    }
});
</script>

<?php
// Include the admin footer
require_once 'includes/footer.php';
?>
