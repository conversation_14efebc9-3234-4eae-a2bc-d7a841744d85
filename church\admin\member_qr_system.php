<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';
require_once '../vendor/autoload.php';

use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Create member QR codes table
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS member_qr_codes (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            event_id INT(11) NOT NULL,
            member_id INT(11) DEFAULT NULL,
            guest_email VARCHAR(255) DEFAULT NULL,
            qr_token VARCHAR(255) NOT NULL UNIQUE,
            attendee_name VARCHAR(255) NOT NULL,
            attendee_email VARCHAR(255) NOT NULL,
            attendee_type ENUM('member', 'guest') DEFAULT 'member',
            is_used TINYINT(1) DEFAULT 0,
            used_at TIMESTAMP NULL,
            email_sent TINYINT(1) DEFAULT 0,
            email_sent_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_event_id (event_id),
            INDEX idx_qr_token (qr_token),
            INDEX idx_member_id (member_id),
            INDEX idx_guest_email (guest_email),
            FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating member_qr_codes table: " . $e->getMessage());
}

// Handle QR code generation and email sending
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'generate_member_qr_codes') {
            $pdo->beginTransaction();
            
            // Get all event attendees (members + guests)
            $stmt = $pdo->prepare("
                SELECT
                    r.id as rsvp_id,
                    r.user_id as member_id,
                    m.full_name as name,
                    m.email,
                    'member' as type
                FROM event_rsvps r
                JOIN members m ON r.user_id = m.id
                WHERE r.event_id = ? AND r.status = 'attending'

                UNION ALL

                SELECT
                    g.id as rsvp_id,
                    NULL as member_id,
                    g.guest_name as name,
                    g.guest_email as email,
                    'guest' as type
                FROM event_rsvps_guests g
                WHERE g.event_id = ? AND g.status = 'attending'
            ");
            $stmt->execute([$event_id, $event_id]);
            $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $generated_count = 0;
            $email_count = 0;
            
            foreach ($attendees as $attendee) {
                // Check if QR code already exists
                $check_stmt = $pdo->prepare("
                    SELECT id FROM member_qr_codes 
                    WHERE event_id = ? AND 
                    ((member_id = ? AND member_id IS NOT NULL) OR 
                     (guest_email = ? AND guest_email IS NOT NULL))
                ");
                $check_stmt->execute([
                    $event_id, 
                    $attendee['member_id'], 
                    $attendee['type'] === 'guest' ? $attendee['email'] : null
                ]);
                
                if ($check_stmt->fetch()) {
                    continue; // QR code already exists
                }
                
                // Generate unique QR token
                $qr_token = 'QR_' . $event_id . '_' . bin2hex(random_bytes(16));
                
                // Insert QR code record
                $stmt = $pdo->prepare("
                    INSERT INTO member_qr_codes 
                    (event_id, member_id, guest_email, qr_token, attendee_name, attendee_email, attendee_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $event_id,
                    $attendee['member_id'],
                    $attendee['type'] === 'guest' ? $attendee['email'] : null,
                    $qr_token,
                    $attendee['name'],
                    $attendee['email'],
                    $attendee['type']
                ]);
                
                $generated_count++;
                
                // Send email with QR code
                if (sendQRCodeEmail($attendee, $qr_token, $event)) {
                    $email_count++;
                    
                    // Mark email as sent
                    $stmt = $pdo->prepare("
                        UPDATE member_qr_codes 
                        SET email_sent = 1, email_sent_at = NOW() 
                        WHERE qr_token = ?
                    ");
                    $stmt->execute([$qr_token]);
                }
            }
            
            $pdo->commit();
            $message = "Generated {$generated_count} QR codes and sent {$email_count} emails successfully!";
            
        } elseif ($_POST['action'] === 'resend_qr_emails') {
            // Resend emails for QR codes that haven't been sent
            $stmt = $pdo->prepare("
                SELECT * FROM member_qr_codes 
                WHERE event_id = ? AND email_sent = 0
            ");
            $stmt->execute([$event_id]);
            $pending_qr_codes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $email_count = 0;
            foreach ($pending_qr_codes as $qr_data) {
                $attendee = [
                    'name' => $qr_data['attendee_name'],
                    'email' => $qr_data['attendee_email'],
                    'type' => $qr_data['attendee_type']
                ];
                
                if (sendQRCodeEmail($attendee, $qr_data['qr_token'], $event)) {
                    $email_count++;
                    
                    // Mark email as sent
                    $stmt = $pdo->prepare("
                        UPDATE member_qr_codes 
                        SET email_sent = 1, email_sent_at = NOW() 
                        WHERE id = ?
                    ");
                    $stmt->execute([$qr_data['id']]);
                }
            }
            
            $message = "Resent {$email_count} QR code emails successfully!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Function to send QR code email with embedded QR image
function sendQRCodeEmail($attendee, $qr_token, $event) {
    global $pdo;

    try {
        // Generate QR code URL
        $full_qr_url = generate_qr_checkin_url($qr_token, 'member');

        // Generate QR code image
        $qrCode = new QrCode($full_qr_url);
        $qrCode->setSize(300);
        $qrCode->setMargin(10);

        $writer = new PngWriter();
        $result = $writer->write($qrCode);

        // Convert to base64 for email embedding
        $qrCodeBase64 = base64_encode($result->getString());
        $qrCodeDataUri = 'data:image/png;base64,' . $qrCodeBase64;
        
        // Email content
        $subject = "🎟️ Your Event Check-in Code - " . $event['title'];
        $message = "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>Event Check-in Code</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                .header h1 { margin: 0; font-size: 24px; }
                .content { padding: 30px 20px; background: white; }
                .checkin-section { text-align: center; margin: 30px 0; padding: 30px; background: #e8f5e8; border-radius: 15px; border: 3px solid #28a745; }
                .checkin-button { display: inline-block; padding: 20px 40px; background: #28a745; color: white; text-decoration: none; border-radius: 10px; font-size: 20px; font-weight: bold; margin: 20px 0; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
                .checkin-button:hover { background: #218838; }
                .qr-code-box { background: white; border: 3px solid #007bff; padding: 30px; margin: 20px 0; border-radius: 10px; }
                .qr-token { font-size: 18px; font-weight: bold; color: #007bff; letter-spacing: 2px; word-break: break-all; }
                .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
                .instructions h3 { margin-top: 0; color: #1976d2; }
                .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
                .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
                .emergency-info { background: #ffe8e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>🎟️ Your Event Check-in Code</h1>
                    <p>Freedom Assembly Church</p>
                </div>

                <div class='content'>
                    <h2>Hello " . htmlspecialchars($attendee['name']) . "!</h2>
                    <p>You're all set for <strong>" . htmlspecialchars($event['title']) . "</strong>! Here's everything you need for quick check-in.</p>

                    <div class='event-details'>
                        <h3>📅 Event Details</h3>
                        <p><strong>Event:</strong> " . htmlspecialchars($event['title']) . "</p>
                        <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($event['event_date'])) . "</p>
                        <p><strong>Location:</strong> " . htmlspecialchars($event['location']) . "</p>
                    </div>

                    <div class='checkin-section'>
                        <h2>🚀 INSTANT CHECK-IN</h2>
                        <p style='font-size: 18px; margin: 20px 0;'><strong>Click the button below OR scan the QR code:</strong></p>

                        <a href='{$full_qr_url}' class='checkin-button' style='color: white; text-decoration: none;'>
                            ✅ CHECK IN NOW
                        </a>

                        <div class='qr-code-box'>
                            <h3>📱 Your Personal QR Code</h3>
                            <img src='{$qrCodeDataUri}' alt='QR Code for Check-in' style='max-width: 250px; height: auto; border: 2px solid #ddd; border-radius: 8px;' />
                            <div class='qr-token'>" . htmlspecialchars($qr_token) . "</div>
                            <p style='margin: 15px 0 5px 0; font-size: 14px; color: #666;'>Scan this QR code or show it to staff</p>
                        </div>
                    </div>

                    <div class='instructions'>
                        <h3>📱 Three Ways to Check In</h3>
                        <ol style='font-size: 16px; line-height: 1.6;'>
                            <li><strong>EASIEST:</strong> Click the green \"CHECK IN NOW\" button above</li>
                            <li><strong>SCAN:</strong> Show the QR code above to staff for scanning</li>
                            <li><strong>MANUAL:</strong> Visit the check-in station and provide your name</li>
                        </ol>
                    </div>

                    <div class='highlight'>
                        <h3>⚡ Super Fast Check-in Process</h3>
                        <p><strong>Two instant options:</strong> Click the button for immediate check-in, or show your QR code to staff for scanning. Both work perfectly!</p>
                    </div>

                    <div class='emergency-info'>
                        <h3>🆘 If You Have Any Issues</h3>
                        <p><strong>Don't worry!</strong> If the button doesn't work or you can't access the link:</p>
                        <ul>
                            <li>Show this email to any staff member</li>
                            <li>Tell them your check-in code: <strong>" . htmlspecialchars($qr_token) . "</strong></li>
                            <li>Or simply provide your name at the check-in desk</li>
                        </ul>
                    </div>

                    <p style='font-size: 16px; margin: 30px 0;'>We're excited to see you at the event! The new check-in system will get you inside quickly so you don't miss anything.</p>

                    <p><strong>Blessings,</strong><br>
                    Freedom Assembly Church Event Team</p>
                </div>

                <div class='footer'>
                    <p>This is an automated message from Freedom Assembly Church</p>
                    <p>Event Management System | Quick Check-in</p>
                    <p><strong>Direct Check-in Link:</strong><br>
                    <a href='{$full_qr_url}' style='color: #007bff; word-break: break-all;'>{$full_qr_url}</a></p>
                </div>
            </div>
        </body>
        </html>
        ";
        
        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= "From: <EMAIL>" . "\r\n";
        
        // Send email
        return mail($attendee['email'], $subject, $message, $headers);
        
    } catch (Exception $e) {
        error_log("Error sending QR code email: " . $e->getMessage());
        return false;
    }
}

// Get QR code statistics
$stmt = $pdo->prepare("
    SELECT 
        COUNT(*) as total_qr_codes,
        COUNT(CASE WHEN email_sent = 1 THEN 1 END) as emails_sent,
        COUNT(CASE WHEN is_used = 1 THEN 1 END) as qr_codes_used,
        COUNT(CASE WHEN attendee_type = 'member' THEN 1 END) as member_qr_codes,
        COUNT(CASE WHEN attendee_type = 'guest' THEN 1 END) as guest_qr_codes
    FROM member_qr_codes 
    WHERE event_id = ?
");
$stmt->execute([$event_id]);
$qr_stats = $stmt->fetch(PDO::FETCH_ASSOC);

// Get recent QR code usage
$stmt = $pdo->prepare("
    SELECT * FROM member_qr_codes 
    WHERE event_id = ? 
    ORDER BY created_at DESC 
    LIMIT 10
");
$stmt->execute([$event_id]);
$recent_qr_codes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get total attendees count
$stmt = $pdo->prepare("
    SELECT 
        (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'attending') +
        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending') as total_attendees
");
$stmt->execute([$event_id, $event_id]);
$total_attendees = $stmt->fetchColumn();

// Page title and header info
$page_title = 'Member QR Code System';
$page_header = 'Member QR Code System';
$page_description = 'Generate and manage personal QR codes for automatic event check-in';

// Include header
include 'includes/header.php';
?>

<style>
.qr-stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
}
.qr-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    transition: all 0.3s ease;
}
.qr-item.used {
    border-color: #28a745;
    background-color: #f8fff9;
}
.qr-item.pending {
    border-color: #ffc107;
    background-color: #fffbf0;
}
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}
.status-used { background-color: #28a745; }
.status-pending { background-color: #ffc107; }
.status-email-sent { background-color: #007bff; }
.status-email-pending { background-color: #dc3545; }
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="bi bi-qr-code-scan"></i> Member QR Code System</h2>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <a href="member_checkin.php?event_id=<?php echo $event_id; ?>" class="btn btn-success me-2">
                    <i class="bi bi-camera"></i> QR Scanner
                </a>
                <a href="staff_qr_scanner.php" class="btn btn-info me-2">
                    <i class="bi bi-camera-fill"></i> Staff Scanner
                </a>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- QR Code Statistics -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card qr-stats-card">
            <div class="card-body">
                <h5 class="text-white mb-3"><i class="bi bi-graph-up"></i> QR Code Statistics</h5>
                <div class="row text-center">
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $total_attendees; ?></h3>
                        <small class="text-white-50">Total Attendees</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $qr_stats['total_qr_codes'] ?? 0; ?></h3>
                        <small class="text-white-50">QR Codes Generated</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $qr_stats['emails_sent'] ?? 0; ?></h3>
                        <small class="text-white-50">Emails Sent</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $qr_stats['qr_codes_used'] ?? 0; ?></h3>
                        <small class="text-white-50">QR Codes Used</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $qr_stats['member_qr_codes'] ?? 0; ?></h3>
                        <small class="text-white-50">Members</small>
                    </div>
                    <div class="col-md-2">
                        <h3 class="text-white mb-0"><?php echo $qr_stats['guest_qr_codes'] ?? 0; ?></h3>
                        <small class="text-white-50">Guests</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- QR Code Management -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-gear"></i> QR Code Management</h5>
            </div>
            <div class="card-body">
                <?php if ($qr_stats['total_qr_codes'] == 0): ?>
                    <!-- Generate QR Codes -->
                    <div class="text-center py-4">
                        <i class="bi bi-qr-code text-muted" style="font-size: 4rem;"></i>
                        <h4 class="mt-3">No QR Codes Generated Yet</h4>
                        <p class="text-muted">Generate personal QR codes for all event attendees and automatically send them via email.</p>
                        
                        <form method="POST" onsubmit="return confirm('Generate QR codes for all <?php echo $total_attendees; ?> attendees and send emails?')">
                            <input type="hidden" name="action" value="generate_member_qr_codes">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-qr-code"></i> Generate QR Codes & Send Emails
                            </button>
                        </form>
                    </div>
                <?php else: ?>
                    <!-- QR Code Actions -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h6>QR Code Actions</h6>
                            <div class="btn-group" role="group">
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="generate_member_qr_codes">
                                    <button type="submit" class="btn btn-outline-primary" 
                                            onclick="return confirm('Generate QR codes for any new attendees?')">
                                        <i class="bi bi-plus-circle"></i> Generate New QR Codes
                                    </button>
                                </form>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="resend_qr_emails">
                                    <button type="submit" class="btn btn-outline-warning"
                                            onclick="return confirm('Resend emails for QR codes that haven\'t been sent?')">
                                        <i class="bi bi-envelope"></i> Resend Pending Emails
                                    </button>
                                </form>
                            </div>
                        </div>
                        <div class="col-md-6 text-end">
                            <h6>Check-in Progress</h6>
                            <div class="progress" style="height: 25px;">
                                <?php 
                                $usage_percentage = $qr_stats['total_qr_codes'] > 0 ? 
                                    round(($qr_stats['qr_codes_used'] / $qr_stats['total_qr_codes']) * 100, 1) : 0;
                                ?>
                                <div class="progress-bar bg-success" style="width: <?php echo $usage_percentage; ?>%">
                                    <?php echo $usage_percentage; ?>% Checked In
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent QR Code Activity -->
                    <h6><i class="bi bi-clock-history"></i> Recent QR Code Activity</h6>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($recent_qr_codes as $qr): ?>
                            <div class="qr-item <?php echo $qr['is_used'] ? 'used' : 'pending'; ?>">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?php echo htmlspecialchars($qr['attendee_name']); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($qr['attendee_email']); ?>
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <div class="mb-1">
                                            <span class="status-indicator status-<?php echo $qr['is_used'] ? 'used' : 'pending'; ?>"></span>
                                            <span class="badge bg-<?php echo $qr['is_used'] ? 'success' : 'warning'; ?>">
                                                <?php echo $qr['is_used'] ? 'Used' : 'Pending'; ?>
                                            </span>
                                        </div>
                                        <div>
                                            <span class="status-indicator status-email-<?php echo $qr['email_sent'] ? 'sent' : 'pending'; ?>"></span>
                                            <span class="badge bg-<?php echo $qr['email_sent'] ? 'primary' : 'danger'; ?>">
                                                Email <?php echo $qr['email_sent'] ? 'Sent' : 'Pending'; ?>
                                            </span>
                                        </div>
                                        <?php if ($qr['is_used'] && $qr['used_at']): ?>
                                            <br>
                                            <small class="text-muted">
                                                Used: <?php echo date('M j, g:i A', strtotime($qr['used_at'])); ?>
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Instructions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> How It Works</h5>
            </div>
            <div class="card-body">
                <h6>📧 Step 1: Generate & Send QR Codes</h6>
                <p class="small text-muted">System generates unique QR codes for each attendee and automatically emails them.</p>
                
                <h6>📱 Step 2: Members Receive QR Codes</h6>
                <p class="small text-muted">Each member gets their personal QR code via email with check-in instructions.</p>
                
                <h6>🎯 Step 3: Event Day Check-in</h6>
                <p class="small text-muted">Members show QR code at entrance. Staff scan it for instant check-in.</p>
                
                <h6>✅ Step 4: Automatic Attendance</h6>
                <p class="small text-muted">System automatically marks member as attended when QR is scanned.</p>
                
                <div class="alert alert-info mt-3">
                    <small>
                        <strong>Benefits:</strong><br>
                        • No manual selection needed<br>
                        • Instant check-in process<br>
                        • Real-time attendance tracking<br>
                        • Reduces queues at entrance
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
