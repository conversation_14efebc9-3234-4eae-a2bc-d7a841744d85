<?php
/**
 * AJAX Session Handler
 * 
 * Unified session management for AJAX requests to prevent session conflicts
 * and timeout issues that require logout/login cycles.
 */

// Prevent any output before we start
ob_start();

// Set error handling for AJAX requests
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Register shutdown function to catch fatal errors and return proper JSON
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        // Clean any output
        while (ob_get_level()) {
            ob_end_clean();
        }

        header('Content-Type: application/json');

        // Provide detailed error information for debugging
        $error_message = 'Fatal error: ' . ($error['message'] ?? 'Unknown error');
        $debug_info = [
            'file' => basename($error['file']),
            'line' => $error['line'],
            'type' => $error['type'],
            'full_file_path' => $error['file']
        ];

        // Include the actual error message for debugging
        if (isset($error['message'])) {
            $debug_info['error_details'] = $error['message'];
        }

        echo json_encode([
            'success' => false,
            'message' => $error_message,
            'error' => 'Fatal error in ' . basename($error['file']) . ' on line ' . $error['line'] . ': ' . ($error['message'] ?? 'Unknown error'),
            'debug' => $debug_info
        ]);
        exit;
    }
});

/**
 * Initialize AJAX session with proper timeout handling
 */
function initAjaxSession() {
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        // Configure session for AJAX requests
        ini_set('session.cookie_lifetime', 0);
        ini_set('session.gc_maxlifetime', 7200); // 2 hours
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 0);
        ini_set('session.use_strict_mode', 0);
        
        session_start();
    }
    
    // Initialize session timestamps if not set
    if (!isset($_SESSION['CREATED'])) {
        $_SESSION['CREATED'] = time();
    }
    
    // Update last activity - this is crucial for preventing timeouts
    $_SESSION['LAST_ACTIVITY'] = time();
    
    return true;
}

/**
 * Check if admin session is valid for AJAX requests
 */
function validateAjaxAdminSession() {
    // Initialize session first
    initAjaxSession();

    // Log session validation attempt
    error_log("[AJAX_SESSION] Validating session for admin_id: " . ($_SESSION['admin_id'] ?? 'not set'));

    // Check if admin is logged in
    if (!isset($_SESSION['admin_id']) || empty($_SESSION['admin_id'])) {
        error_log("[AJAX_SESSION] Session validation failed: admin not logged in");
        return [
            'valid' => false,
            'error' => 'not_logged_in',
            'message' => 'Admin session not found. Please refresh the page and login again.'
        ];
    }
    
    // Check session timeout (2 hours = 7200 seconds)
    $timeout_seconds = 7200;
    if (isset($_SESSION['LAST_ACTIVITY'])) {
        $inactive_time = time() - $_SESSION['LAST_ACTIVITY'];
        if ($inactive_time >= $timeout_seconds) {
            // Session timed out for AJAX: do NOT destroy session, just return error
            // Let the frontend handle re-login gracefully
            error_log("[AJAX_SESSION] Session timeout detected: inactive for {$inactive_time} seconds (limit: {$timeout_seconds})");
            return [
                'valid' => false,
                'error' => 'session_timeout',
                'message' => 'Session has expired due to inactivity. Please refresh the page and login again.',
                'inactive_time' => $inactive_time,
                'timeout_seconds' => $timeout_seconds
            ];
        }
    }

    // Always update activity timestamp after successful validation
    $_SESSION['LAST_ACTIVITY'] = time();

    error_log("[AJAX_SESSION] Session validation successful for admin: " . $_SESSION['admin_id']);

    return [
        'valid' => true,
        'admin_id' => $_SESSION['admin_id'],
        'admin_name' => $_SESSION['admin_name'] ?? 'Unknown',
        'remaining_time' => $timeout_seconds,
        'last_activity' => $_SESSION['LAST_ACTIVITY']
    ];
}

/**
 * Send JSON response and exit
 */
function sendAjaxResponse($data) {
    // Clean any output that might have been generated
    while (ob_get_level()) {
        ob_end_clean();
    }
    
    // Set proper headers
    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    
    // Send response
    echo json_encode($data);
    exit;
}

/**
 * Handle AJAX session validation and return appropriate response
 */
function handleAjaxSessionValidation() {
    $validation = validateAjaxAdminSession();

    if (!$validation['valid']) {
        // Add additional debug info for session issues
        $response = [
            'success' => false,
            'session_valid' => false,
            'error' => $validation['error'],
            'message' => $validation['message']
        ];

        // Include timeout info if available
        if (isset($validation['inactive_time'])) {
            $response['debug'] = [
                'inactive_time' => $validation['inactive_time'],
                'timeout_seconds' => $validation['timeout_seconds']
            ];
        }

        sendAjaxResponse($response);
    }

    return $validation;
}

/**
 * Extend session for active users (called by heartbeat)
 */
function extendAjaxSession() {
    initAjaxSession();

    if (isset($_SESSION['admin_id'])) {
        $_SESSION['LAST_ACTIVITY'] = time();
        error_log("[AJAX_SESSION] Session extended for admin: " . $_SESSION['admin_id']);
        return true;
    }

    return false;
}

/**
 * Include config safely for AJAX requests
 */
function includeConfigForAjax() {
    // Include config without session manager conflicts
    if (!defined('CONFIG_INCLUDED')) {
        try {
            require_once __DIR__ . '/../../config.php';
        } catch (Exception $e) {
            error_log("AJAX Config include error: " . $e->getMessage());
            sendAjaxResponse([
                'success' => false,
                'message' => 'Configuration error: ' . $e->getMessage()
            ]);
        } catch (Error $e) {
            error_log("AJAX Config fatal error: " . $e->getMessage());
            sendAjaxResponse([
                'success' => false,
                'message' => 'Configuration fatal error: ' . $e->getMessage()
            ]);
        }
    }

    // Ensure PDO connection is available
    global $pdo;

    // If PDO is not set, try to create a direct connection
    if (!isset($pdo) || !($pdo instanceof PDO)) {
        try {
            // Get database settings - ensure they're in global scope
            global $host, $dbname, $username, $password, $environment;

            // If variables aren't set, try to determine environment and set them manually
            if (!isset($host) || !isset($dbname) || !isset($username)) {
                // Determine environment
                if (!isset($environment)) {
                    $environment = 'development'; // Default
                    if (isset($_SERVER['SERVER_NAME'])) {
                        if (strpos($_SERVER['SERVER_NAME'], 'localhost') === false &&
                            strpos($_SERVER['SERVER_NAME'], '127.0.0.1') === false) {
                            $environment = 'production';
                        } elseif (strpos($_SERVER['SERVER_NAME'], 'staging') !== false) {
                            $environment = 'staging';
                        }
                    }
                }

                // Set database configuration based on environment
                if ($environment === 'production') {
                    $host = 'localhost';
                    $dbname = 'u271750246_DDD';
                    $username = 'u271750246_DDD';
                    $password = 'Moremoney2025@!';
                } elseif ($environment === 'staging') {
                    $host = 'localhost';
                    $dbname = 'u271750246_DDD';
                    $username = 'u271750246_DDD';
                    $password = 'Moremoney2025@!';
                } else {
                    // Local XAMPP development settings
                    $host = 'localhost';
                    $dbname = 'campaign_n';
                    $username = 'root';
                    $password = '';
                }

                error_log("AJAX DB: Set database config for environment: $environment");
            }

            if (!isset($host) || !isset($dbname) || !isset($username)) {
                sendAjaxResponse([
                    'success' => false,
                    'message' => 'Database configuration variables still not set after manual configuration'
                ]);
            }

            // Log database connection attempt
            error_log("AJAX DB: Attempting connection - host=$host, dbname=$dbname, user=$username");

            // Create direct PDO connection
            $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];

            $pdo = new PDO($dsn, $username, $password, $options);
            error_log("AJAX DB: PDO connection created successfully");

        } catch (PDOException $e) {
            sendAjaxResponse([
                'success' => false,
                'message' => 'Database connection failed: ' . $e->getMessage()
            ]);
        }
    }

    // Test the connection
    try {
        $pdo->query("SELECT 1");
    } catch (Exception $e) {
        sendAjaxResponse([
            'success' => false,
            'message' => 'Database connection test failed: ' . $e->getMessage()
        ]);
    }

    return $pdo;
}

// Auto-initialize session when this file is included
initAjaxSession();
