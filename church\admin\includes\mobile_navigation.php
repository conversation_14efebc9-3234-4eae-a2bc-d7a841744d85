<?php
/**
 * Mobile Navigation Component
 * Provides responsive mobile/tablet navigation completely separate from desktop sidebar
 */

// Ensure we have access to RBAC functions
if (!function_exists('canAccessPage')) {
    if (file_exists(dirname(__FILE__) . '/rbac_access_control.php')) {
        require_once dirname(__FILE__) . '/rbac_access_control.php';
    }
}

// Fallback function if RBAC is not available
if (!function_exists('canAccessPage')) {
    function canAccessPage($page) {
        // Fallback: allow access to basic pages if RBAC is not available
        return true;
    }
}

// Helper function to build admin URL consistently (if not already defined)
if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        if (defined('ADMIN_URL')) {
            return ADMIN_URL . '/' . $page;
        }
        // Better fallback - construct URL dynamically
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? $_SERVER['SERVER_NAME'] ?? 'localhost';
        $basePath = dirname($_SERVER['SCRIPT_NAME'] ?? '');
        if (strpos($basePath, '/admin') !== false) {
            $basePath = dirname($basePath);
        }
        return $protocol . '://' . $host . $basePath . '/admin/' . $page;
    }
}

// Fallback terminology functions
if (!function_exists('get_member_term')) {
    function get_member_term($plural = false) {
        return $plural ? 'Members' : 'Member';
    }
}

if (!function_exists('get_event_term')) {
    function get_event_term($plural = false) {
        return $plural ? 'Events' : 'Event';
    }
}

if (!function_exists('get_admin_title')) {
    function get_admin_title() {
        return 'Admin Panel';
    }
}

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Mobile Navigation - Only visible on mobile/tablet -->
<div class="mobile-navigation d-lg-none">
    <!-- Mobile Header Bar -->
    <div class="mobile-nav-header">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-auto">
                    <button class="btn btn-link mobile-menu-toggle" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobileNavDrawer" aria-controls="mobileNavDrawer">
                        <i class="bi bi-list fs-4 text-white"></i>
                    </button>
                </div>
                <div class="col">
                    <h6 class="mobile-nav-title mb-0 text-white"><?php echo function_exists('get_admin_title') ? get_admin_title() : 'Admin Panel'; ?></h6>
                </div>
                <div class="col-auto">
                    <!-- Quick QR Scanner Access -->
                    <a href="<?php echo function_exists('admin_url_for') ? admin_url_for('staff_qr_scanner.php') : 'staff_qr_scanner.php'; ?>" class="btn btn-link text-white" title="QR Scanner">
                        <i class="bi bi-qr-code-scan fs-5"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation Drawer -->
    <div class="offcanvas offcanvas-start mobile-nav-drawer" tabindex="-1" id="mobileNavDrawer" aria-labelledby="mobileNavDrawerLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="mobileNavDrawerLabel">
                <i class="bi bi-grid-3x3-gap-fill me-2"></i>
                <?php echo function_exists('get_admin_title') ? get_admin_title() : 'Admin Panel'; ?>
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <!-- Quick Actions Section -->
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-lightning-charge-fill me-2"></i>Quick Actions
                </h6>
                <div class="mobile-nav-items">
                    <a href="<?php echo admin_url_for('dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                    <a href="<?php echo admin_url_for('staff_qr_scanner.php'); ?>" class="mobile-nav-item">
                        <i class="bi bi-qr-code-scan"></i>
                        <span>QR Scanner</span>
                    </a>
                    <a href="<?php echo admin_url_for('mobile_checkin.php'); ?>" class="mobile-nav-item">
                        <i class="bi bi-check-circle"></i>
                        <span>Mobile Check-in</span>
                    </a>
                    <?php if (canAccessPage('pwa/index.php')): ?>
                    <a href="<?php echo admin_url_for('pwa/'); ?>" class="mobile-nav-item" target="_blank">
                        <i class="bi bi-phone"></i>
                        <span>Mobile PWA</span>
                        <span class="badge bg-success ms-auto">NEW</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Member Management Section -->
            <?php if (canAccessPage('members.php') || canAccessPage('add_member.php') || canAccessPage('family_management.php') || canAccessPage('member_skills.php') || canAccessPage('requests.php') || canAccessPage('volunteer_opportunities.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-people-fill me-2"></i><?php echo get_member_term(true); ?>
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('members.php')): ?>
                    <a href="<?php echo admin_url_for('members.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'members.php' ? 'active' : ''; ?>">
                        <i class="bi bi-people"></i>
                        <span>View <?php echo get_member_term(true); ?></span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('add_member.php')): ?>
                    <a href="<?php echo admin_url_for('add_member.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'add_member.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-plus"></i>
                        <span>Add <?php echo get_member_term(); ?></span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('family_management.php')): ?>
                    <a href="<?php echo admin_url_for('family_management.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'family_management.php' ? 'active' : ''; ?>">
                        <i class="bi bi-house-heart"></i>
                        <span>Family Management</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('member_skills.php')): ?>
                    <a href="<?php echo admin_url_for('member_skills.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'member_skills.php' ? 'active' : ''; ?>">
                        <i class="bi bi-tools"></i>
                        <span>Member Skills</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('requests.php')): ?>
                    <a href="<?php echo admin_url_for('requests.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'requests.php' ? 'active' : ''; ?>">
                        <i class="bi bi-chat-heart"></i>
                        <span>Requests</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('volunteer_opportunities.php')): ?>
                    <a href="<?php echo admin_url_for('volunteer_opportunities.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'volunteer_opportunities.php' ? 'active' : ''; ?>">
                        <i class="bi bi-hand-thumbs-up"></i>
                        <span>Volunteer Opportunities</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Event Management Section -->
            <?php if (canAccessPage('events.php') || canAccessPage('event_sessions.php') || canAccessPage('event_attendance.php') || canAccessPage('event_categories.php') || canAccessPage('event_reports.php') || canAccessPage('event_coordinator_dashboard.php') || canAccessPage('session_moderator_dashboard.php') || canAccessPage('realtime_dashboard.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-calendar-event-fill me-2"></i><?php echo get_event_term(true); ?>
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('events.php')): ?>
                    <a href="<?php echo admin_url_for('events.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'events.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar-event"></i>
                        <span>View <?php echo get_event_term(true); ?></span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('event_sessions.php')): ?>
                    <a href="<?php echo admin_url_for('event_sessions.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_sessions.php' ? 'active' : ''; ?>">
                        <i class="bi bi-collection"></i>
                        <span>Event Sessions</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('event_attendance.php')): ?>
                    <a href="<?php echo admin_url_for('event_attendance.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_attendance.php' ? 'active' : ''; ?>">
                        <i class="bi bi-check2-square"></i>
                        <span>Attendance</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('event_categories.php')): ?>
                    <a href="<?php echo admin_url_for('event_categories.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_categories.php' ? 'active' : ''; ?>">
                        <i class="bi bi-tags"></i>
                        <span>Categories</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('event_reports.php')): ?>
                    <a href="<?php echo admin_url_for('event_reports.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_reports.php' ? 'active' : ''; ?>">
                        <i class="bi bi-file-earmark-bar-graph"></i>
                        <span>Reports</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('event_coordinator_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('event_coordinator_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'event_coordinator_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-workspace"></i>
                        <span>Coordinator Dashboard</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('session_moderator_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('session_moderator_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'session_moderator_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-check"></i>
                        <span>Session Moderator</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('realtime_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('realtime_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'realtime_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-broadcast"></i>
                        <span>Live Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Communications Section -->
            <?php if (canAccessPage('email_templates.php') || canAccessPage('bulk_email.php') || canAccessPage('email_scheduler.php') || canAccessPage('contacts.php') || canAccessPage('contact_groups.php') || canAccessPage('birthday.php') || canAccessPage('bulk_sms.php') || canAccessPage('single_sms.php') || canAccessPage('sms_templates.php') || canAccessPage('whatsapp_messages.php') || canAccessPage('whatsapp_templates.php') || canAccessPage('notifications.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-envelope-fill me-2"></i>Communications
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('email_templates.php')): ?>
                    <a href="<?php echo admin_url_for('email_templates.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'email_templates.php' ? 'active' : ''; ?>">
                        <i class="bi bi-file-earmark-text"></i>
                        <span>Email Templates</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('bulk_email.php')): ?>
                    <a href="<?php echo admin_url_for('bulk_email.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'bulk_email.php' ? 'active' : ''; ?>">
                        <i class="bi bi-send"></i>
                        <span>Send Emails</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('email_scheduler.php')): ?>
                    <a href="<?php echo admin_url_for('email_scheduler.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'email_scheduler.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar-check"></i>
                        <span>Email Scheduler</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('contacts.php')): ?>
                    <a href="<?php echo admin_url_for('contacts.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'contacts.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-lines-fill"></i>
                        <span>Contacts</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('contact_groups.php')): ?>
                    <a href="<?php echo admin_url_for('contact_groups.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'contact_groups.php' ? 'active' : ''; ?>">
                        <i class="bi bi-people-fill"></i>
                        <span>Contact Groups</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('birthday.php')): ?>
                    <a href="<?php echo admin_url_for('birthday.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'birthday.php' ? 'active' : ''; ?>">
                        <i class="bi bi-gift"></i>
                        <span>Birthday Messages</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('bulk_sms.php')): ?>
                    <a href="<?php echo admin_url_for('bulk_sms.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'bulk_sms.php' ? 'active' : ''; ?>">
                        <i class="bi bi-chat-text"></i>
                        <span>Send SMS</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('single_sms.php')): ?>
                    <a href="<?php echo admin_url_for('single_sms.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'single_sms.php' ? 'active' : ''; ?>">
                        <i class="bi bi-chat-square-text"></i>
                        <span>Single SMS</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('sms_templates.php')): ?>
                    <a href="<?php echo admin_url_for('sms_templates.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'sms_templates.php' ? 'active' : ''; ?>">
                        <i class="bi bi-file-text"></i>
                        <span>SMS Templates</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('whatsapp_messages.php')): ?>
                    <a href="<?php echo admin_url_for('whatsapp_messages.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'whatsapp_messages.php' ? 'active' : ''; ?>">
                        <i class="bi bi-whatsapp"></i>
                        <span>WhatsApp Messages</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('whatsapp_templates.php')): ?>
                    <a href="<?php echo admin_url_for('whatsapp_templates.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'whatsapp_templates.php' ? 'active' : ''; ?>">
                        <i class="bi bi-whatsapp"></i>
                        <span>WhatsApp Templates</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('notifications.php')): ?>
                    <a href="<?php echo admin_url_for('notifications.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'notifications.php' ? 'active' : ''; ?>">
                        <i class="bi bi-bell"></i>
                        <span>Notifications</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Donations & Finance Section -->
            <?php if (canAccessPage('donations.php') || canAccessPage('gift_management.php') || canAccessPage('enhanced_donate.php') || canAccessPage('payment_integration.php') || canAccessPage('payment_tables.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-currency-dollar me-2"></i>Donations & Finance
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('donations.php')): ?>
                    <a href="<?php echo admin_url_for('donations.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'donations.php' ? 'active' : ''; ?>">
                        <i class="bi bi-heart"></i>
                        <span>Donations</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('gift_management.php')): ?>
                    <a href="<?php echo admin_url_for('gift_management.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'gift_management.php' ? 'active' : ''; ?>">
                        <i class="bi bi-gift"></i>
                        <span>Gift Management</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('enhanced_donate.php')): ?>
                    <a href="<?php echo admin_url_for('enhanced_donate.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'enhanced_donate.php' ? 'active' : ''; ?>">
                        <i class="bi bi-credit-card"></i>
                        <span>Enhanced Donate</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('payment_integration.php')): ?>
                    <a href="<?php echo admin_url_for('payment_integration.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'payment_integration.php' ? 'active' : ''; ?>">
                        <i class="bi bi-wallet2"></i>
                        <span>Payment Integration</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('payment_tables.php')): ?>
                    <a href="<?php echo admin_url_for('payment_tables.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'payment_tables.php' ? 'active' : ''; ?>">
                        <i class="bi bi-table"></i>
                        <span>Payment Tables</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Analytics Section -->
            <?php if (canAccessPage('universal_ai_dashboard.php') || canAccessPage('universal_analytics_dashboard.php') || canAccessPage('advanced_analytics.php') || canAccessPage('email_analytics.php') || canAccessPage('sms_analytics.php') || canAccessPage('recipient_analytics.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-graph-up me-2"></i>Analytics
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('universal_ai_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('universal_ai_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_ai_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-robot"></i>
                        <span>AI Predictions</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('universal_analytics_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('universal_analytics_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_analytics_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-bar-chart"></i>
                        <span>Analytics Dashboard</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('advanced_analytics.php')): ?>
                    <a href="<?php echo admin_url_for('advanced_analytics.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'advanced_analytics.php' ? 'active' : ''; ?>">
                        <i class="bi bi-graph-up-arrow"></i>
                        <span>Advanced Analytics</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('email_analytics.php')): ?>
                    <a href="<?php echo admin_url_for('email_analytics.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'email_analytics.php' ? 'active' : ''; ?>">
                        <i class="bi bi-envelope-check"></i>
                        <span>Email Analytics</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('sms_analytics.php')): ?>
                    <a href="<?php echo admin_url_for('sms_analytics.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'sms_analytics.php' ? 'active' : ''; ?>">
                        <i class="bi bi-chat-square-dots"></i>
                        <span>SMS Analytics</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('recipient_analytics.php')): ?>
                    <a href="<?php echo admin_url_for('recipient_analytics.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'recipient_analytics.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-check"></i>
                        <span>Recipient Analytics</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Settings Section -->
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-gear-fill me-2"></i>Settings
                </h6>
                <div class="mobile-nav-items">
                    <a href="<?php echo admin_url_for('profile.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'profile.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-circle"></i>
                        <span>My Profile</span>
                    </a>
                    <?php if (canAccessPage('universal_organization_setup.php')): ?>
                    <a href="<?php echo admin_url_for('universal_organization_setup.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'universal_organization_setup.php' ? 'active' : ''; ?>">
                        <i class="bi bi-building"></i>
                        <span>Organization Setup</span>
                    </a>
                    <?php endif; ?>

                    <?php if (canAccessPage('settings.php')): ?>
                    <a href="<?php echo admin_url_for('settings.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'settings.php' ? 'active' : ''; ?>">
                        <i class="bi bi-gear"></i>
                        <span>Settings</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('appearance_settings.php')): ?>
                    <a href="<?php echo admin_url_for('appearance_settings.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'appearance_settings.php' ? 'active' : ''; ?>">
                        <i class="bi bi-palette"></i>
                        <span>Appearance</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('branding_settings.php')): ?>
                    <a href="<?php echo admin_url_for('branding_settings.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'branding_settings.php' ? 'active' : ''; ?>">
                        <i class="bi bi-brush"></i>
                        <span>Branding</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('logo_management_consolidated.php')): ?>
                    <a href="<?php echo admin_url_for('logo_management_consolidated.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'logo_management_consolidated.php' ? 'active' : ''; ?>">
                        <i class="bi bi-image"></i>
                        <span>Logo Management</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('custom_fields.php')): ?>
                    <a href="<?php echo admin_url_for('custom_fields.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'custom_fields.php' ? 'active' : ''; ?>">
                        <i class="bi bi-input-cursor-text"></i>
                        <span>Custom Fields</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- System Administration Section -->
            <?php if (canAccessPage('setup_rbac_system.php') || canAccessPage('create_admin_users.php') || canAccessPage('manage_user_permissions.php') || canAccessPage('security_audit.php') || canAccessPage('security_settings.php') || canAccessPage('backup_management.php') || canAccessPage('system_test_dashboard.php') || canAccessPage('super_admin_dashboard.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-shield-check me-2"></i>System Administration
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('setup_rbac_system.php')): ?>
                    <a href="<?php echo admin_url_for('setup_rbac_system.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'setup_rbac_system.php' ? 'active' : ''; ?>">
                        <i class="bi bi-shield-lock"></i>
                        <span>RBAC System</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('create_admin_users.php')): ?>
                    <a href="<?php echo admin_url_for('create_admin_users.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'create_admin_users.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-plus-fill"></i>
                        <span>Create Admin Users</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('manage_user_permissions.php')): ?>
                    <a href="<?php echo admin_url_for('manage_user_permissions.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'manage_user_permissions.php' ? 'active' : ''; ?>">
                        <i class="bi bi-key"></i>
                        <span>User Permissions</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('security_audit.php')): ?>
                    <a href="<?php echo admin_url_for('security_audit.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'security_audit.php' ? 'active' : ''; ?>">
                        <i class="bi bi-shield-exclamation"></i>
                        <span>Security Audit</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('security_settings.php')): ?>
                    <a href="<?php echo admin_url_for('security_settings.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'security_settings.php' ? 'active' : ''; ?>">
                        <i class="bi bi-shield-fill-check"></i>
                        <span>Security Settings</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('backup_management.php')): ?>
                    <a href="<?php echo admin_url_for('backup_management.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'backup_management.php' ? 'active' : ''; ?>">
                        <i class="bi bi-cloud-arrow-up"></i>
                        <span>Backup Management</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('system_test_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('system_test_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'system_test_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-bug"></i>
                        <span>System Testing</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('super_admin_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('super_admin_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'super_admin_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-person-badge"></i>
                        <span>Super Admin Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Integration Section -->
            <?php if (canAccessPage('calendar_integration.php') || canAccessPage('social_media_integration.php') || canAccessPage('sms_integration.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-link-45deg me-2"></i>Integrations
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('calendar_integration.php')): ?>
                    <a href="<?php echo admin_url_for('calendar_integration.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'calendar_integration.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar3"></i>
                        <span>Calendar Integration</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('social_media_integration.php')): ?>
                    <a href="<?php echo admin_url_for('social_media_integration.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'social_media_integration.php' ? 'active' : ''; ?>">
                        <i class="bi bi-share"></i>
                        <span>Social Media</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('sms_integration.php')): ?>
                    <a href="<?php echo admin_url_for('sms_integration.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'sms_integration.php' ? 'active' : ''; ?>">
                        <i class="bi bi-phone"></i>
                        <span>SMS Integration</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Staff Dashboard Section -->
            <?php if (canAccessPage('staff_dashboard.php') || canAccessPage('assignment_dashboard.php') || canAccessPage('multi_session_dashboard.php') || canAccessPage('cross_session_dashboard.php')): ?>
            <div class="mobile-nav-section">
                <h6 class="mobile-nav-section-title">
                    <i class="bi bi-person-workspace me-2"></i>Staff Dashboard
                </h6>
                <div class="mobile-nav-items">
                    <?php if (canAccessPage('staff_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('staff_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'staff_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-speedometer"></i>
                        <span>Staff Dashboard</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('assignment_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('assignment_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'assignment_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-clipboard-check"></i>
                        <span>Assignment Dashboard</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('multi_session_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('multi_session_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'multi_session_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-collection"></i>
                        <span>Multi Session Dashboard</span>
                    </a>
                    <?php endif; ?>
                    <?php if (canAccessPage('cross_session_dashboard.php')): ?>
                    <a href="<?php echo admin_url_for('cross_session_dashboard.php'); ?>" class="mobile-nav-item <?php echo $current_page === 'cross_session_dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-arrow-left-right"></i>
                        <span>Cross Session Dashboard</span>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Mobile Bottom Navigation (phones only) -->
    <div class="mobile-bottom-nav d-md-none">
        <div class="container-fluid">
            <div class="row text-center">
                <div class="col">
                    <a href="<?php echo admin_url_for('dashboard.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="bi bi-speedometer2"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo admin_url_for('staff_qr_scanner.php'); ?>" class="mobile-bottom-nav-item">
                        <i class="bi bi-qr-code-scan"></i>
                        <span>Scanner</span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo admin_url_for('members.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'members.php' ? 'active' : ''; ?>">
                        <i class="bi bi-people"></i>
                        <span><?php echo get_member_term(true); ?></span>
                    </a>
                </div>
                <div class="col">
                    <a href="<?php echo admin_url_for('events.php'); ?>" class="mobile-bottom-nav-item <?php echo $current_page === 'events.php' ? 'active' : ''; ?>">
                        <i class="bi bi-calendar-event"></i>
                        <span><?php echo get_event_term(true); ?></span>
                    </a>
                </div>
                <div class="col">
                    <button class="mobile-bottom-nav-item" data-bs-toggle="offcanvas" data-bs-target="#mobileNavDrawer">
                        <i class="bi bi-grid-3x3-gap"></i>
                        <span>More</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
