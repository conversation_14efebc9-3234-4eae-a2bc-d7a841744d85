<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Create session_locations table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_locations (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            session_id INT(11) NOT NULL,
            location_name VARCHAR(255) NOT NULL,
            location_description TEXT,
            capacity INT(11) DEFAULT NULL,
            equipment_needed TEXT,
            setup_instructions TEXT,
            assigned_staff JSON,
            device_assignments JSON,
            status ENUM('setup', 'active', 'cleanup', 'completed') DEFAULT 'setup',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_session_id (session_id),
            INDEX idx_status (status),
            FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
    
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS session_devices (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            device_name VARCHAR(255) NOT NULL,
            device_type ENUM('tablet', 'phone', 'laptop', 'scanner') DEFAULT 'tablet',
            device_id VARCHAR(255) UNIQUE,
            assigned_session INT(11) DEFAULT NULL,
            assigned_staff INT(11) DEFAULT NULL,
            last_sync TIMESTAMP NULL,
            battery_level INT(11) DEFAULT NULL,
            status ENUM('available', 'assigned', 'in_use', 'offline') DEFAULT 'available',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_device_id (device_id),
            INDEX idx_assigned_session (assigned_session),
            INDEX idx_status (status)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating session location tables: " . $e->getMessage());
}

// Handle location management actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'assign_location') {
            $session_id = $_POST['session_id'] ?? '';
            $location_name = $_POST['location_name'] ?? '';
            $location_description = $_POST['location_description'] ?? '';
            $capacity = $_POST['capacity'] ?? null;
            $equipment_needed = $_POST['equipment_needed'] ?? '';
            $setup_instructions = $_POST['setup_instructions'] ?? '';
            $assigned_staff = $_POST['assigned_staff'] ?? [];
            
            if (empty($session_id) || empty($location_name)) {
                throw new Exception("Session and location name are required.");
            }

            if (empty($assigned_staff) || !is_array($assigned_staff) || count($assigned_staff) === 0) {
                throw new Exception("At least one staff member must be assigned to the location.");
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO session_locations 
                (session_id, location_name, location_description, capacity, equipment_needed, setup_instructions, assigned_staff)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                location_name = VALUES(location_name),
                location_description = VALUES(location_description),
                capacity = VALUES(capacity),
                equipment_needed = VALUES(equipment_needed),
                setup_instructions = VALUES(setup_instructions),
                assigned_staff = VALUES(assigned_staff),
                updated_at = NOW()
            ");
            // Store the original assigned_staff for database (with prefixes)
            $stmt->execute([
                $session_id,
                $location_name,
                $location_description,
                $capacity,
                $equipment_needed,
                $setup_instructions,
                json_encode($assigned_staff)
            ]);

            // Process assigned staff IDs (convert from prefixed format)
            $processed_staff = [];
            if (!empty($assigned_staff)) {
                foreach ($assigned_staff as $staff_id) {
                    if (strpos($staff_id, 'admin_') === 0) {
                        $actual_id = str_replace('admin_', '', $staff_id);
                        $processed_staff[] = ['id' => $actual_id, 'type' => 'admin'];
                    } elseif (strpos($staff_id, 'member_') === 0) {
                        $actual_id = str_replace('member_', '', $staff_id);
                        $processed_staff[] = ['id' => $actual_id, 'type' => 'member'];
                    }
                }
            }

            // Send notifications to assigned staff
            if (!empty($processed_staff)) {
                error_log("DEBUG: Attempting to send notifications to staff: " . print_r($processed_staff, true));

                // Use simple notification approach
                $notification_success = 0;

                foreach ($processed_staff as $staff_info) {
                    $staff_id = $staff_info['id'];
                    $staff_type = $staff_info['type'];

                    error_log("DEBUG: Processing staff ID: $staff_id, Type: $staff_type");

                    // Get staff details
                    if ($staff_type === 'admin') {
                        $stmt = $pdo->prepare("SELECT id, email, full_name FROM admins WHERE id = ?");
                    } else {
                        $stmt = $pdo->prepare("SELECT id, email, full_name FROM members WHERE id = ?");
                    }

                    $stmt->execute([$staff_id]);
                    $staff = $stmt->fetch(PDO::FETCH_ASSOC);

                    if (!$staff) {
                        error_log("ERROR: Staff not found - ID: $staff_id, Type: $staff_type");
                        continue;
                    }

                    // Get session details for email including location details
                    $stmt = $pdo->prepare("
                        SELECT es.*, e.title as event_title, e.event_date,
                               DATE(es.start_datetime) as start_date,
                               TIME(es.start_datetime) as start_time,
                               sl.location_description, sl.capacity, sl.equipment_needed, sl.setup_instructions
                        FROM event_sessions es
                        JOIN events e ON es.event_id = e.id
                        LEFT JOIN session_locations sl ON es.id = sl.session_id
                        WHERE es.id = ?
                    ");
                    $stmt->execute([$session_id]);
                    $session = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($session) {
                        // Create comprehensive email with all required fields
                        $subject = "Session Location Assignment - $location_name";
                        $message_body = "
                        <html>
                        <head><title>Session Assignment</title></head>
                        <body>
                            <h2>Session Location Assignment</h2>
                            <p>Dear {$staff['full_name']},</p>
                            <p>You have been assigned to a session location:</p>
                            <table style='border-collapse:collapse;width:100%;margin-bottom:20px;'>
                                <tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Session:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['session_title']}</td></tr>
                                <tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Event:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['event_title']}</td></tr>
                                <tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Location:</strong></td><td style='border:1px solid #ddd;padding:8px;'>$location_name</td></tr>
                                <tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Date:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['start_date']}</td></tr>
                                <tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Time:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['start_time']}</td></tr>";

                        // Add description if available
                        if (!empty($location_description)) {
                            $message_body .= "<tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Description:</strong></td><td style='border:1px solid #ddd;padding:8px;'>" . htmlspecialchars($location_description) . "</td></tr>";
                        }

                        // Add capacity if available
                        if (!empty($capacity)) {
                            $message_body .= "<tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Capacity:</strong></td><td style='border:1px solid #ddd;padding:8px;'>" . htmlspecialchars($capacity) . " people</td></tr>";
                        }

                        // Add equipment needed if available
                        if (!empty($equipment_needed)) {
                            $message_body .= "<tr><td style='border:1px solid #ddd;padding:8px;background-color:#f8f9fa;'><strong>Equipment Needed:</strong></td><td style='border:1px solid #ddd;padding:8px;'>" . htmlspecialchars($equipment_needed) . "</td></tr>";
                        }

                        $message_body .= "</table>";

                        // Add setup instructions if available
                        if (!empty($setup_instructions)) {
                            $message_body .= "
                            <div style='background-color:#e9ecef;padding:15px;border-radius:5px;margin-bottom:20px;'>
                                <h4 style='margin-top:0;color:#495057;'>Setup Instructions:</h4>
                                <p style='margin-bottom:0;white-space:pre-line;'>" . htmlspecialchars($setup_instructions) . "</p>
                            </div>";
                        }

                        $message_body .= "
                            <p>Please ensure you are available for this assignment and review all the details above.</p>
                            <p>Best regards,<br>Freedom Assembly Church</p>
                        </body>
                        </html>
                        ";

                        error_log("DEBUG: Sending email to: {$staff['email']}");
                        $email_result = sendEmail($staff['email'], $staff['full_name'], $subject, $message_body, true);
                        error_log("DEBUG: Email result: " . ($email_result ? 'SUCCESS' : 'FAILED'));

                        if ($email_result) {
                            $notification_success++;
                        }
                    }
                }

                if ($notification_success > 0) {
                    $message = "Location assigned successfully! Notifications sent to $notification_success staff member(s).";
                } else {
                    $message = "Location assigned successfully! However, there was an issue sending notifications. Check error logs.";
                }
            } else {
                $message = "Location assigned successfully! No staff assigned for notifications.";
            }
            
        } elseif ($_POST['action'] === 'reset_session_assignments') {
            $session_id = $_POST['session_id'] ?? '';

            if (empty($session_id)) {
                throw new Exception("Session ID is required.");
            }

            // Reset all assignments for this session
            $stmt = $pdo->prepare("UPDATE session_locations SET assigned_staff = NULL, status = 'setup' WHERE session_id = ?");
            $stmt->execute([$session_id]);

            $stmt = $pdo->prepare("UPDATE session_devices SET assigned_session = NULL, assigned_staff = NULL, status = 'available' WHERE assigned_session = ?");
            $stmt->execute([$session_id]);

            $message = "All assignments for this session have been reset successfully!";

        } elseif ($_POST['action'] === 'delete_session_locations') {
            $session_id = $_POST['session_id'] ?? '';

            if (empty($session_id)) {
                throw new Exception("Session ID is required.");
            }

            // Delete all locations for this session
            $stmt = $pdo->prepare("DELETE FROM session_locations WHERE session_id = ?");
            $stmt->execute([$session_id]);

            $message = "All locations for this session have been deleted successfully!";

        } elseif ($_POST['action'] === 'assign_device') {
            $device_id = $_POST['device_id'] ?? '';
            $session_id = $_POST['session_id'] ?? '';
            $staff_id = $_POST['staff_id'] ?? '';
            
            if (empty($device_id) || empty($session_id)) {
                throw new Exception("Device and session are required.");
            }
            
            $stmt = $pdo->prepare("
                UPDATE session_devices 
                SET assigned_session = ?, assigned_staff = ?, status = 'assigned', updated_at = NOW()
                WHERE device_id = ?
            ");
            $stmt->execute([$session_id, $staff_id ?: null, $device_id]);

            // Send notification to assigned staff if specified
            if (!empty($staff_id)) {
                sendDeviceAssignmentNotification($pdo, $session_id, $device_id, $staff_id);
            }

            $message = "Device assigned successfully!" . (!empty($staff_id) ? " Notification sent to assigned staff." : "");
            
        } elseif ($_POST['action'] === 'update_location_status') {
            $location_id = $_POST['location_id'] ?? '';
            $status = $_POST['status'] ?? '';
            
            if (empty($location_id) || empty($status)) {
                throw new Exception("Location and status are required.");
            }
            
            $stmt = $pdo->prepare("
                UPDATE session_locations 
                SET status = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$status, $location_id]);
            
            $message = "Location status updated successfully!";
            
        } elseif ($_POST['action'] === 'register_device') {
            $device_name = $_POST['device_name'] ?? '';
            $device_type = $_POST['device_type'] ?? 'tablet';
            $device_id = $_POST['device_id'] ?? '';
            
            if (empty($device_name) || empty($device_id)) {
                throw new Exception("Device name and ID are required.");
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO session_devices (device_name, device_type, device_id)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                device_name = VALUES(device_name),
                device_type = VALUES(device_type),
                updated_at = NOW()
            ");
            $stmt->execute([$device_name, $device_type, $device_id]);
            
            $message = "Device registered successfully!";

        } elseif ($_POST['action'] === 'delete_device') {
            $device_id = $_POST['device_id'] ?? '';

            if (empty($device_id)) {
                throw new Exception("Device ID is required.");
            }

            // Delete the device
            $stmt = $pdo->prepare("DELETE FROM session_devices WHERE device_id = ?");
            $stmt->execute([$device_id]);

            if ($stmt->rowCount() > 0) {
                $message = "Device deleted successfully!";
            } else {
                throw new Exception("Device not found or could not be deleted.");
            }
        }

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get sessions with location assignments
$stmt = $pdo->prepare("
    SELECT 
        s.*,
        sl.id as location_id,
        sl.location_name,
        sl.location_description,
        sl.capacity,
        sl.equipment_needed,
        sl.setup_instructions,
        sl.assigned_staff,
        sl.device_assignments,
        sl.status as location_status,
        COUNT(sa.id) as registered_count,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count
    FROM event_sessions s
    LEFT JOIN session_locations sl ON s.id = sl.session_id
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get available devices
$stmt = $pdo->prepare("
    SELECT 
        sd.*,
        s.session_title,
        m.full_name as staff_name
    FROM session_devices sd
    LEFT JOIN event_sessions s ON sd.assigned_session = s.id
    LEFT JOIN members m ON sd.assigned_staff = m.id
    ORDER BY sd.status, sd.device_name
");
$stmt->execute();
$devices = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get staff members for assignment (include both admins and members)
$staff_members = [];

// Get admins (no is_active column in admins table)
$stmt = $pdo->prepare("SELECT id, full_name, email, 'admin' as type FROM admins ORDER BY full_name");
$stmt->execute();
$admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get members (has is_active column)
$stmt = $pdo->prepare("SELECT id, full_name, email, 'member' as type FROM members WHERE is_active = 1 ORDER BY full_name");
$stmt->execute();
$members = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Combine both with prefixed IDs to avoid conflicts
foreach ($admins as $admin) {
    $staff_members[] = [
        'id' => 'admin_' . $admin['id'],
        'full_name' => $admin['full_name'] . ' (Admin)',
        'email' => $admin['email'],
        'type' => 'admin',
        'actual_id' => $admin['id']
    ];
}

foreach ($members as $member) {
    $staff_members[] = [
        'id' => 'member_' . $member['id'],
        'full_name' => $member['full_name'] . ' (Member)',
        'email' => $member['email'],
        'type' => 'member',
        'actual_id' => $member['id']
    ];
}

// Page title and header info
$page_title = 'Session Location Manager';
$page_header = 'Session Location Manager';
$page_description = 'Manage session locations, staff assignments, and device coordination';

// Include header
include 'includes/header.php';
?>

<style>
.location-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
}
.location-card.assigned {
    border-color: #28a745;
    background-color: #f8fff9;
}
.location-card.active {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.device-item {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    margin: 8px 0;
    transition: all 0.3s ease;
}
.device-item.available {
    border-color: #28a745;
    background-color: #f8fff9;
}
.device-item.assigned {
    border-color: #ffc107;
    background-color: #fffbf0;
}
.device-item.in_use {
    border-color: #007bff;
    background-color: #f8f9ff;
}
.device-item.offline {
    border-color: #dc3545;
    background-color: #fff5f5;
}
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}
.status-setup { background-color: #6c757d; }
.status-active { background-color: #28a745; }
.status-cleanup { background-color: #ffc107; }
.status-completed { background-color: #007bff; }

/* Required field styling */
.form-label .text-danger {
    font-weight: bold;
}

/* Invalid field styling */
.form-control.is-invalid,
.form-select.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Required field help text */
.form-text {
    font-size: 0.875em;
    color: #6c757d;
}

/* Highlight required fields */
.form-label:has(.text-danger) {
    font-weight: 600;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
            </div>
            <div>
                <a href="session_assignment_preferences.php" class="btn btn-outline-secondary me-2" title="Notification Preferences">
                    <i class="bi bi-bell"></i> Notifications
                </a>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#deviceModal">
                    <i class="bi bi-plus-circle"></i> Register Device
                </button>
                <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Device Status Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-devices"></i> Device Status Overview</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php 
                    $device_stats = [
                        'available' => 0,
                        'assigned' => 0,
                        'in_use' => 0,
                        'offline' => 0
                    ];
                    foreach ($devices as $device) {
                        $device_stats[$device['status']]++;
                    }
                    ?>
                    <div class="col-md-3 text-center">
                        <h4 class="text-success"><?php echo $device_stats['available']; ?></h4>
                        <small class="text-muted">Available</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-warning"><?php echo $device_stats['assigned']; ?></h4>
                        <small class="text-muted">Assigned</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-primary"><?php echo $device_stats['in_use']; ?></h4>
                        <small class="text-muted">In Use</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <h4 class="text-danger"><?php echo $device_stats['offline']; ?></h4>
                        <small class="text-muted">Offline</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sessions and Locations -->
<div class="row">
    <div class="col-md-8">
        <h5><i class="bi bi-calendar-event"></i> Sessions & Locations</h5>
        
        <?php foreach ($sessions as $session): ?>
            <div class="card location-card <?php echo $session['location_id'] ? ($session['location_status'] === 'active' ? 'active' : 'assigned') : ''; ?> mb-3">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                            <small class="text-muted">
                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                <?php if ($session['location_name']): ?>
                                    • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location_name']); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        <div class="text-end">
                            <?php if ($session['location_id']): ?>
                                <span class="status-indicator status-<?php echo $session['location_status']; ?>"></span>
                                <span class="badge bg-<?php 
                                    echo $session['location_status'] === 'active' ? 'success' : 
                                        ($session['location_status'] === 'setup' ? 'secondary' : 
                                        ($session['location_status'] === 'cleanup' ? 'warning' : 'primary')); 
                                ?>">
                                    <?php echo ucfirst($session['location_status']); ?>
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary">No Location</span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    <?php if ($session['location_id']): ?>
                        <!-- Location Details -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <strong>Capacity:</strong> <?php echo $session['capacity'] ?: 'Unlimited'; ?><br>
                                <strong>Registered:</strong> <?php echo $session['registered_count']; ?><br>
                                <strong>Attended:</strong> <?php echo $session['attended_count']; ?>
                            </div>
                            <div class="col-md-6">
                                <?php if ($session['assigned_staff']): ?>
                                    <strong>Assigned Staff:</strong><br>
                                    <?php
                                    $staff_ids = json_decode($session['assigned_staff'], true);
                                    if ($staff_ids && is_array($staff_ids)) {
                                        foreach ($staff_ids as $staff_id) {
                                            // Handle both prefixed and non-prefixed IDs
                                            $actual_id = $staff_id;
                                            $staff_type = 'admin'; // default

                                            if (strpos($staff_id, 'admin_') === 0) {
                                                $actual_id = str_replace('admin_', '', $staff_id);
                                                $staff_type = 'admin';
                                            } elseif (strpos($staff_id, 'member_') === 0) {
                                                $actual_id = str_replace('member_', '', $staff_id);
                                                $staff_type = 'member';
                                            }

                                            // Get staff details from database
                                            if ($staff_type === 'admin') {
                                                $stmt = $pdo->prepare("SELECT full_name, email FROM admins WHERE id = ?");
                                            } else {
                                                $stmt = $pdo->prepare("SELECT full_name, email FROM members WHERE id = ?");
                                            }
                                            $stmt->execute([$actual_id]);
                                            $staff_data = $stmt->fetch(PDO::FETCH_ASSOC);

                                            if ($staff_data) {
                                                $badge_class = $staff_type === 'admin' ? 'bg-primary' : 'bg-success';
                                                $type_label = $staff_type === 'admin' ? 'Admin' : 'Member';
                                                echo '<span class="badge ' . $badge_class . ' me-1" title="' . htmlspecialchars($staff_data['email']) . '">';
                                                echo htmlspecialchars($staff_data['full_name']) . ' (' . $type_label . ')';
                                                echo '</span>';
                                            }
                                        }
                                    } else {
                                        echo '<em>No staff assigned</em>';
                                    }
                                    ?>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <?php if ($session['equipment_needed']): ?>
                            <div class="mb-2">
                                <strong>Equipment:</strong> <?php echo htmlspecialchars($session['equipment_needed']); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($session['setup_instructions']): ?>
                            <div class="mb-3">
                                <strong>Setup Instructions:</strong><br>
                                <small class="text-muted"><?php echo nl2br(htmlspecialchars($session['setup_instructions'])); ?></small>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Status Update -->
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="update_location_status">
                            <input type="hidden" name="location_id" value="<?php echo $session['location_id']; ?>">
                            <div class="btn-group" role="group">
                                <button type="submit" name="status" value="setup" class="btn btn-sm btn-outline-secondary">Setup</button>
                                <button type="submit" name="status" value="active" class="btn btn-sm btn-outline-success">Active</button>
                                <button type="submit" name="status" value="cleanup" class="btn btn-sm btn-outline-warning">Cleanup</button>
                                <button type="submit" name="status" value="completed" class="btn btn-sm btn-outline-primary">Completed</button>
                            </div>
                        </form>
                        
                        <a href="mobile_checkin.php?event_id=<?php echo $event_id; ?>&session_id=<?php echo $session['id']; ?>"
                           class="btn btn-sm btn-primary ms-2">
                            <i class="bi bi-phone"></i> Mobile Check-in
                        </a>

                        <!-- Reset and Delete Buttons -->
                        <div class="btn-group ms-2" role="group">
                            <form method="post" style="display: inline;" onsubmit="return confirm('Reset all assignments for this session?')">
                                <input type="hidden" name="action" value="reset_session_assignments">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-warning">
                                    <i class="bi bi-arrow-clockwise"></i> Reset Assignments
                                </button>
                            </form>

                            <form method="post" style="display: inline;" onsubmit="return confirm('Delete all locations for this session?')">
                                <input type="hidden" name="action" value="delete_session_locations">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="bi bi-trash"></i> Delete Locations
                                </button>
                            </form>
                        </div>
                        
                    <?php else: ?>
                        <!-- Assign Location Form -->
                        <button class="btn btn-success" onclick="showLocationForm(<?php echo $session['id']; ?>)">
                            <i class="bi bi-plus-circle"></i> Assign Location
                        </button>
                        
                        <div id="location-form-<?php echo $session['id']; ?>" style="display: none;" class="mt-3">
                            <form method="POST">
                                <input type="hidden" name="action" value="assign_location">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Location Name <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" name="location_name" required>
                                            <div class="form-text">Required: Enter the name of the location</div>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Capacity</label>
                                            <input type="number" class="form-control" name="capacity">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Description</label>
                                            <textarea class="form-control" name="location_description" rows="2"></textarea>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Assigned Staff <span class="text-danger">*</span></label>
                                            <select class="form-select" name="assigned_staff[]" multiple required>
                                                <?php foreach ($staff_members as $staff): ?>
                                                    <option value="<?php echo $staff['id']; ?>">
                                                        <?php echo htmlspecialchars($staff['full_name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">Required: Select at least one staff member to assign to this location</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Equipment Needed</label>
                                    <input type="text" class="form-control" name="equipment_needed" 
                                           placeholder="e.g., Projector, Microphone, Tables">
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Setup Instructions</label>
                                    <textarea class="form-control" name="setup_instructions" rows="3"></textarea>
                                </div>
                                
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-circle"></i> Assign Location
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="hideLocationForm(<?php echo $session['id']; ?>)">
                                    Cancel
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>


                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <!-- Device Management -->
    <div class="col-md-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5><i class="bi bi-devices"></i> Device Management</h5>
            <button class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#deviceModal">
                <i class="bi bi-plus-circle"></i> Register Device
            </button>
        </div>

        <div class="card">
            <div class="card-body" style="max-height: 600px; overflow-y: auto;">
                <?php foreach ($devices as $device): ?>
                    <div class="device-item <?php echo $device['status']; ?>">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong><?php echo htmlspecialchars($device['device_name']); ?></strong>
                                <br>
                                <small class="text-muted">
                                    <i class="bi bi-<?php 
                                        echo $device['device_type'] === 'tablet' ? 'tablet' : 
                                            ($device['device_type'] === 'phone' ? 'phone' : 
                                            ($device['device_type'] === 'laptop' ? 'laptop' : 'upc-scan')); 
                                    ?>"></i>
                                    <?php echo ucfirst($device['device_type']); ?>
                                    • ID: <?php echo htmlspecialchars($device['device_id']); ?>
                                </small>
                                
                                <?php if ($device['session_title']): ?>
                                    <br>
                                    <small class="text-primary">
                                        <i class="bi bi-calendar"></i> <?php echo htmlspecialchars($device['session_title']); ?>
                                    </small>
                                <?php endif; ?>
                                
                                <?php if ($device['staff_name']): ?>
                                    <br>
                                    <small class="text-success">
                                        <i class="bi bi-person"></i> <?php echo htmlspecialchars($device['staff_name']); ?>
                                    </small>
                                <?php endif; ?>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-<?php 
                                    echo $device['status'] === 'available' ? 'success' : 
                                        ($device['status'] === 'assigned' ? 'warning' : 
                                        ($device['status'] === 'in_use' ? 'primary' : 'danger')); 
                                ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $device['status'])); ?>
                                </span>
                                
                                <?php if ($device['battery_level']): ?>
                                    <br>
                                    <small class="text-muted">
                                        <i class="bi bi-battery-<?php 
                                            echo $device['battery_level'] > 75 ? 'full' : 
                                                ($device['battery_level'] > 50 ? 'three-quarters' : 
                                                ($device['battery_level'] > 25 ? 'half' : 'quarter')); 
                                        ?>"></i>
                                        <?php echo $device['battery_level']; ?>%
                                    </small>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mt-2">
                            <?php if ($device['status'] === 'available'): ?>
                                <button class="btn btn-sm btn-primary" onclick="showDeviceAssignment('<?php echo $device['device_id']; ?>')">
                                    <i class="bi bi-plus"></i> Assign
                                </button>
                            <?php endif; ?>

                            <!-- Delete button for all devices -->
                            <form method="post" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this device? This action cannot be undone.')">
                                <input type="hidden" name="action" value="delete_device">
                                <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </form>
                        </div>

                        <?php if ($device['status'] === 'available'): ?>
                            <div id="device-assignment-<?php echo $device['device_id']; ?>" style="display: none;" class="mt-2">
                                <form method="POST">
                                    <input type="hidden" name="action" value="assign_device">
                                    <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">

                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" name="session_id" required>
                                            <option value="">Select Session</option>
                                            <?php foreach ($sessions as $session): ?>
                                                <option value="<?php echo $session['id']; ?>">
                                                    <?php echo htmlspecialchars($session['session_title']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="mb-2">
                                        <select class="form-select form-select-sm" name="staff_id">
                                            <option value="">Select Staff (Optional)</option>
                                            <?php foreach ($staff_members as $staff): ?>
                                                <option value="<?php echo $staff['id']; ?>">
                                                    <?php echo htmlspecialchars($staff['full_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <button type="submit" class="btn btn-sm btn-success">Assign</button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="hideDeviceAssignment('<?php echo $device['device_id']; ?>')">Cancel</button>
                                </form>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<!-- Device Registration Modal -->
<div class="modal fade" id="deviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Register New Device</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="register_device">
                    
                    <div class="mb-3">
                        <label class="form-label">Device Name</label>
                        <input type="text" class="form-control" name="device_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Device Type</label>
                        <select class="form-select" name="device_type" required>
                            <option value="tablet">Tablet</option>
                            <option value="phone">Phone</option>
                            <option value="laptop">Laptop</option>
                            <option value="scanner">Scanner</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Device ID</label>
                        <input type="text" class="form-control" name="device_id" required 
                               placeholder="Unique identifier for this device">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Register Device</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
function showLocationForm(sessionId) {
    document.getElementById('location-form-' + sessionId).style.display = 'block';
}

function hideLocationForm(sessionId) {
    document.getElementById('location-form-' + sessionId).style.display = 'none';
}

function showDeviceAssignment(deviceId) {
    document.getElementById('device-assignment-' + deviceId).style.display = 'block';
}

function hideDeviceAssignment(deviceId) {
    document.getElementById('device-assignment-' + deviceId).style.display = 'none';
}

// Form validation for location assignment
function validateLocationForm(form) {
    const locationName = form.querySelector('input[name="location_name"]');
    const assignedStaff = form.querySelector('select[name="assigned_staff[]"]');

    let isValid = true;
    let errorMessage = '';

    // Validate location name
    if (!locationName.value.trim()) {
        isValid = false;
        errorMessage += 'Location name is required.\n';
        locationName.classList.add('is-invalid');
    } else {
        locationName.classList.remove('is-invalid');
    }

    // Validate assigned staff
    const selectedStaff = Array.from(assignedStaff.selectedOptions);
    if (selectedStaff.length === 0) {
        isValid = false;
        errorMessage += 'At least one staff member must be assigned.\n';
        assignedStaff.classList.add('is-invalid');
    } else {
        assignedStaff.classList.remove('is-invalid');
    }

    if (!isValid) {
        alert('Please fix the following errors:\n\n' + errorMessage);
        return false;
    }

    return true;
}

// Add event listeners to all location assignment forms
document.addEventListener('DOMContentLoaded', function() {
    // Find all location assignment forms and add validation
    const locationForms = document.querySelectorAll('form[method="POST"]');
    locationForms.forEach(function(form) {
        const actionInput = form.querySelector('input[name="action"]');
        if (actionInput && actionInput.value === 'assign_location') {
            form.addEventListener('submit', function(e) {
                if (!validateLocationForm(form)) {
                    e.preventDefault();
                }
            });

            // Add real-time validation feedback
            const locationName = form.querySelector('input[name="location_name"]');
            const assignedStaff = form.querySelector('select[name="assigned_staff[]"]');

            if (locationName) {
                locationName.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.classList.add('is-invalid');
                    } else {
                        this.classList.remove('is-invalid');
                    }
                });
            }

            if (assignedStaff) {
                assignedStaff.addEventListener('change', function() {
                    const selectedStaff = Array.from(this.selectedOptions);
                    if (selectedStaff.length === 0) {
                        this.classList.add('is-invalid');
                    } else {
                        this.classList.remove('is-invalid');
                    }
                });
            }
        }
    });
});

// Auto-refresh device status every 30 seconds
setInterval(function() {
    // This would make an AJAX call to update device status
    console.log('Refreshing device status...');
}, 30000);
</script>

<?php
/**
 * Send notifications to staff assigned to session locations
 */
function sendSessionLocationAssignmentNotifications($pdo, $session_id, $location_name, $assigned_staff) {
    try {
        error_log("DEBUG: sendSessionLocationAssignmentNotifications called with session_id: $session_id, location: $location_name, staff: " . print_r($assigned_staff, true));

        // Get session details
        $stmt = $pdo->prepare("
            SELECT es.*, e.title as event_title, e.event_date, e.location as event_location,
                   DATE(es.start_datetime) as start_date,
                   TIME(es.start_datetime) as start_time
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            WHERE es.id = ?
        ");
        $stmt->execute([$session_id]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        error_log("DEBUG: Session query result: " . print_r($session, true));

        if (!$session) {
            error_log("Session not found for notification: $session_id");
            return false;
        }

        $successCount = 0;

        // Simple notification approach
        foreach ($assigned_staff as $staff_info) {
            $staff_id = is_array($staff_info) ? $staff_info['id'] : $staff_info;
            $staff_type = is_array($staff_info) ? $staff_info['type'] : 'admin';

            error_log("DEBUG: Processing staff ID: $staff_id, Type: $staff_type");

            // Get staff details
            if ($staff_type === 'admin') {
                $stmt = $pdo->prepare("SELECT id, email, full_name FROM admins WHERE id = ?");
            } else {
                $stmt = $pdo->prepare("SELECT id, email, full_name FROM members WHERE id = ?");
            }

            $stmt->execute([$staff_id]);
            $staff = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$staff) {
                error_log("ERROR: Staff not found - ID: $staff_id, Type: $staff_type");
                continue;
            }

            error_log("DEBUG: Staff found: " . print_r($staff, true));

            // Create simple email
            $subject = "Session Location Assignment - $location_name";
            $message = "
            <html>
            <head><title>Session Assignment</title></head>
            <body>
                <h2>Session Location Assignment</h2>
                <p>Dear {$staff['full_name']},</p>
                <p>You have been assigned to a session location:</p>
                <table style='border-collapse:collapse;width:100%;'>
                    <tr><td style='border:1px solid #ddd;padding:8px;'><strong>Session:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['title']}</td></tr>
                    <tr><td style='border:1px solid #ddd;padding:8px;'><strong>Event:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['event_title']}</td></tr>
                    <tr><td style='border:1px solid #ddd;padding:8px;'><strong>Location:</strong></td><td style='border:1px solid #ddd;padding:8px;'>$location_name</td></tr>
                    <tr><td style='border:1px solid #ddd;padding:8px;'><strong>Date:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['start_date']}</td></tr>
                    <tr><td style='border:1px solid #ddd;padding:8px;'><strong>Time:</strong></td><td style='border:1px solid #ddd;padding:8px;'>{$session['start_time']}</td></tr>
                </table>
                <p>Please ensure you are available for this assignment.</p>
                <p>Best regards,<br>Freedom Assembly Church</p>
            </body>
            </html>
            ";

            error_log("DEBUG: Sending email to: {$staff['email']}");
            $email_result = sendEmail($staff['email'], $staff['full_name'], $subject, $message, true);
            error_log("DEBUG: Email result: " . ($email_result ? 'SUCCESS' : 'FAILED'));

            if ($email_result) {
                $successCount++;
            }
        }

        error_log("DEBUG: Notification complete - $successCount emails sent out of " . count($assigned_staff));
        return $successCount > 0;

        foreach ($assigned_staff as $staff_info) {
            // Handle both old format (just ID) and new format (array with id and type)
            if (is_array($staff_info)) {
                $staff_id = $staff_info['id'];
                $staff_type = $staff_info['type'];
            } else {
                // Old format - just the ID, assume admin for backward compatibility
                $staff_id = $staff_info;
                $staff_type = 'admin';
            }

            error_log("DEBUG: Processing staff_id: $staff_id, type: $staff_type");

            // Get staff member details based on type
            $staff = null;

            if ($staff_type === 'admin') {
                // Check admins table (no phone column in admins)
                $stmt = $pdo->prepare("SELECT id, email, full_name FROM admins WHERE id = ?");
                $stmt->execute([$staff_id]);
                $admin = $stmt->fetch(PDO::FETCH_ASSOC);

                error_log("DEBUG: Admin query result for ID $staff_id: " . print_r($admin, true));

                if ($admin) {
                    $staff = [
                        'id' => $admin['id'],
                        'email' => $admin['email'],
                        'name' => $admin['full_name'],
                        'phone' => '', // Admins table doesn't have phone column
                        'type' => 'admin'
                    ];
                }
            } else {
                // Check members table (phone_number column)
                $stmt = $pdo->prepare("SELECT id, email, full_name, phone_number FROM members WHERE id = ?");
                $stmt->execute([$staff_id]);
                $member = $stmt->fetch(PDO::FETCH_ASSOC);

                error_log("DEBUG: Member query result for ID $staff_id: " . print_r($member, true));

                if ($member) {
                    $staff = [
                        'id' => $member['id'],
                        'email' => $member['email'],
                        'name' => $member['full_name'],
                        'phone' => $member['phone_number'] ?? '',
                        'type' => 'member'
                    ];
                }
            }

            if (!$staff) {
                error_log("Staff member not found for notification: $staff_id");
                continue;
            }

            error_log("DEBUG: Staff data found: " . print_r($staff, true));

            // Check notification preferences
            $preferences = getAdminNotificationPreferences($pdo, $staff['id'], 'session_location_assignment');
            error_log("DEBUG: Notification preferences for staff {$staff['id']}: " . print_r($preferences, true));

            // Send email notification if enabled
            $emailSent = false;
            if ($preferences['email_enabled']) {
                error_log("DEBUG: Attempting to send email to {$staff['email']}");
                $emailSent = sendSessionLocationEmailNotification($staff, $session, $location_name);
                error_log("DEBUG: Email send result: " . ($emailSent ? 'SUCCESS' : 'FAILED'));
            } else {
                error_log("DEBUG: Email notifications disabled for staff {$staff['id']}");
            }

            // Send SMS notification if enabled and phone number is available
            $smsSent = false;
            if ($preferences['sms_enabled'] && !empty($staff['phone'])) {
                error_log("DEBUG: Attempting to send SMS to {$staff['phone']}");
                $smsSent = sendSessionLocationSMSNotification($staff, $session, $location_name);
                error_log("DEBUG: SMS send result: " . ($smsSent ? 'SUCCESS' : 'FAILED'));
            } else {
                error_log("DEBUG: SMS notifications disabled or no phone number for staff {$staff['id']}");
            }

            // Create in-app notification for admins if enabled
            if ($staff['type'] === 'admin' && $preferences['web_enabled']) {
                error_log("DEBUG: Creating in-app notification for admin {$staff['id']}");
                $notification_created = createSimpleAdminNotification(
                    $pdo,
                    $staff['id'],
                    'Session Location Assignment',
                    "You have been assigned to '{$location_name}' for session '{$session['session_title']}'"
                );
                error_log("DEBUG: In-app notification created: " . ($notification_created ? 'SUCCESS' : 'FAILED'));
            }

            if ($emailSent || $smsSent) {
                $successCount++;
            }
        }

        return $successCount > 0;

    } catch (Exception $e) {
        error_log("Error sending session location assignment notifications: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email notification for session location assignment
 */
function sendSessionLocationEmailNotification($staff, $session, $location_name) {
    try {
        $subject = "Session Location Assignment - {$session['event_title']}";

        $message = "
        <h2>Session Location Assignment</h2>
        <p>Dear {$staff['name']},</p>

        <p>You have been assigned to manage a location for an upcoming session:</p>

        <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
            <h3>Assignment Details:</h3>
            <p><strong>Event:</strong> {$session['event_title']}</p>
            <p><strong>Session:</strong> {$session['session_title']}</p>
            <p><strong>Location:</strong> {$location_name}</p>
            <p><strong>Date:</strong> " . date('F j, Y', strtotime($session['start_datetime'])) . "</p>
            <p><strong>Time:</strong> " . date('g:i A', strtotime($session['start_datetime'])) . "</p>
            <p><strong>Duration:</strong> {$session['duration']} minutes</p>
        </div>

        <p>Please ensure you are available at the assigned time and location. If you have any questions or concerns, please contact the event organizers.</p>

        <p>Thank you for your service!</p>

        <p>Best regards,<br>" . get_organization_name() . "</p>
        ";

        error_log("DEBUG: Calling sendEmail function for {$staff['email']}");
        $result = sendEmail($staff['email'], $staff['name'], $subject, $message, true);
        error_log("DEBUG: sendEmail returned: " . ($result ? 'true' : 'false'));

        return $result;

    } catch (Exception $e) {
        error_log("Error sending session location email notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS notification for session location assignment
 */
function sendSessionLocationSMSNotification($staff, $session, $location_name) {
    try {
        // Check if SMS notifications are enabled
        $smsEnabled = get_site_setting('sms_notifications_enabled', '0');
        if ($smsEnabled !== '1') {
            return false;
        }

        $message = "Session Assignment: You've been assigned to '{$location_name}' for '{$session['session_title']}' on " .
                   date('M j, Y g:i A', strtotime($session['start_datetime'])) .
                   ". - " . get_organization_name();

        // Here you would integrate with your SMS service (Twilio, etc.)
        // For now, we'll log it and return true to indicate the attempt was made
        error_log("SMS notification would be sent to {$staff['phone']}: $message");

        return true;

    } catch (Exception $e) {
        error_log("Error sending session location SMS notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send notification for device assignment
 */
function sendDeviceAssignmentNotification($pdo, $session_id, $device_id, $staff_id) {
    try {
        // Get session and device details
        $stmt = $pdo->prepare("
            SELECT es.*, e.title as event_title, e.event_date,
                   DATE(es.start_datetime) as start_date,
                   TIME(es.start_datetime) as start_time,
                   sd.device_name, sd.device_type
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            JOIN session_devices sd ON sd.device_id = ?
            WHERE es.id = ?
        ");
        $stmt->execute([$device_id, $session_id]);
        $data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$data) {
            error_log("Session or device not found for notification: session_id=$session_id, device_id=$device_id");
            return false;
        }

        // Get staff member details
        $staff = null;

        // Check admins table first
        $stmt = $pdo->prepare("SELECT id, email, full_name, phone FROM admins WHERE id = ?");
        $stmt->execute([$staff_id]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($admin) {
            $staff = [
                'id' => $admin['id'],
                'email' => $admin['email'],
                'name' => $admin['full_name'],
                'phone' => $admin['phone'],
                'type' => 'admin'
            ];
        } else {
            // Check members table
            $stmt = $pdo->prepare("SELECT id, email, full_name, phone FROM members WHERE id = ?");
            $stmt->execute([$staff_id]);
            $member = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($member) {
                $staff = [
                    'id' => $member['id'],
                    'email' => $member['email'],
                    'name' => $member['full_name'],
                    'phone' => $member['phone'],
                    'type' => 'member'
                ];
            }
        }

        if (!$staff) {
            error_log("Staff member not found for device assignment notification: $staff_id");
            return false;
        }

        // Check notification preferences
        $preferences = getAdminNotificationPreferences($pdo, $staff['id'], 'device_assignment');

        // Send email notification if enabled
        $emailSent = false;
        if ($preferences['email_enabled']) {
            $subject = "Device Assignment - {$data['event_title']}";

            $message = "
            <h2>Device Assignment</h2>
            <p>Dear {$staff['name']},</p>

            <p>You have been assigned a device for an upcoming session:</p>

            <div style='background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
                <h3>Assignment Details:</h3>
                <p><strong>Event:</strong> {$data['event_title']}</p>
                <p><strong>Session:</strong> {$data['session_title']}</p>
                <p><strong>Device:</strong> {$data['device_name']} ({$data['device_type']})</p>
                <p><strong>Date:</strong> " . date('F j, Y', strtotime($data['start_date'])) . "</p>
                <p><strong>Time:</strong> " . date('g:i A', strtotime($data['start_time'])) . "</p>
            </div>

            <p>Please ensure you collect and test the assigned device before the session begins.</p>

            <p>Thank you for your service!</p>

            <p>Best regards,<br>" . get_organization_name() . "</p>
            ";

            $emailSent = sendEmail($staff['email'], $staff['name'], $subject, $message, true);
        }

        // Send SMS if enabled and phone number is available
        if ($preferences['sms_enabled'] && !empty($staff['phone'])) {
            $smsMessage = "Device Assignment: You've been assigned '{$data['device_name']}' for '{$data['session_title']}' on " .
                         date('M j, Y g:i A', strtotime($data['start_date'] . ' ' . $data['start_time'])) .
                         ". - " . get_organization_name();

            // Log SMS (integrate with actual SMS service)
            error_log("SMS notification would be sent to {$staff['phone']}: $smsMessage");
        }

        // Create in-app notification for admins if enabled
        if ($staff['type'] === 'admin' && $preferences['web_enabled']) {
            createAdminNotification(
                $pdo,
                $staff['id'],
                'Device Assignment',
                "You have been assigned device '{$data['device_name']}' for session '{$data['session_title']}'",
                'assignment',
                null,
                'system',
                "session_location_manager.php?event_id={$data['event_id']}",
                'normal'
            );
        }

        return $emailSent;

    } catch (Exception $e) {
        error_log("Error sending device assignment notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Get admin notification preferences for a specific notification type
 */
function getAdminNotificationPreferences($pdo, $admin_id, $notification_type) {
    try {
        $stmt = $pdo->prepare("
            SELECT email_enabled, web_enabled, sms_enabled
            FROM admin_notification_preferences
            WHERE admin_id = ? AND notification_type = ?
        ");
        $stmt->execute([$admin_id, $notification_type]);
        $preferences = $stmt->fetch(PDO::FETCH_ASSOC);

        // Return default preferences if none are set
        if (!$preferences) {
            return [
                'email_enabled' => 1,
                'web_enabled' => 1,
                'sms_enabled' => 0
            ];
        }

        return $preferences;

    } catch (Exception $e) {
        error_log("Error getting admin notification preferences: " . $e->getMessage());
        // Return default preferences on error
        return [
            'email_enabled' => 1,
            'web_enabled' => 1,
            'sms_enabled' => 0
        ];
    }
}

/**
 * Simple admin notification creation function
 */
function createSimpleAdminNotification($pdo, $recipient_id, $title, $message) {
    try {
        // Create admin_notifications table if it doesn't exist
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS admin_notifications (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                recipient_id INT(11) NOT NULL,
                sender_id INT(11) DEFAULT NULL,
                sender_type ENUM('admin', 'system', 'member') DEFAULT 'system',
                notification_type VARCHAR(50) NOT NULL DEFAULT 'assignment',
                title VARCHAR(255) NOT NULL,
                message TEXT NOT NULL,
                action_url VARCHAR(500) DEFAULT NULL,
                priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
                is_read TINYINT(1) DEFAULT 0,
                read_at TIMESTAMP NULL,
                expires_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
        ");

        $stmt = $pdo->prepare("
            INSERT INTO admin_notifications
            (recipient_id, notification_type, title, message, sender_type)
            VALUES (?, ?, ?, ?, ?)
        ");

        return $stmt->execute([$recipient_id, 'assignment', $title, $message, 'system']);

    } catch (Exception $e) {
        error_log("Error creating admin notification: " . $e->getMessage());
        return false;
    }
}
?>

<?php include 'includes/footer.php'; ?>
