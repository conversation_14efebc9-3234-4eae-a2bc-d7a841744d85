<?php
/**
 * Thumbnail Utility Functions
 * 
 * Shared thumbnail generation functions to avoid redefinition errors
 */

if (!function_exists('generateThumbnail')) {
    /**
     * Generate thumbnail for image files
     * 
     * @param string $source_path Path to source image
     * @param string $thumbnail_dir Directory to save thumbnail
     * @param string $filename Filename for thumbnail
     * @return string|null Path to generated thumbnail or null on failure
     */
    function generateThumbnail($source_path, $thumbnail_dir, $filename) {
        try {
            // Check if GD extension is loaded
            if (!extension_loaded('gd')) {
                error_log("Thumbnail error: GD extension not loaded");
                return null;
            }

            $thumbnail_path = $thumbnail_dir . 'thumb_' . $filename;

            // Check if source file exists
            if (!file_exists($source_path)) {
                error_log("Thumbnail error: Source file does not exist: $source_path");
                return null;
            }

            // Get image info with error suppression
            $image_info = @getimagesize($source_path);
            if (!$image_info) {
                error_log("Thumbnail error: Could not get image size for: $source_path");
                return null;
            }

            $width = $image_info[0];
            $height = $image_info[1];
            $type = $image_info[2];

            // Validate image dimensions
            if ($width <= 0 || $height <= 0) {
                error_log("Thumbnail error: Invalid image dimensions: {$width}x{$height}");
                return null;
            }

            // Calculate thumbnail dimensions (max 300x200)
            $thumb_width = 300;
            $thumb_height = 200;
            
            $ratio = min($thumb_width / $width, $thumb_height / $height);
            $new_width = (int)round($width * $ratio);
            $new_height = (int)round($height * $ratio);
            
            // Ensure minimum dimensions
            if ($new_width <= 0 || $new_height <= 0) {
                error_log("Thumbnail error: Invalid calculated dimensions: {$new_width}x{$new_height}");
                return null;
            }

            // Create source image with error handling
            $source = null;
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = @imagecreatefromjpeg($source_path);
                    break;
                case IMAGETYPE_PNG:
                    $source = @imagecreatefrompng($source_path);
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($source_path);
                    break;
                default:
                    error_log("Thumbnail error: Unsupported image type: $type");
                    return null;
            }
            
            if (!$source) {
                error_log("Thumbnail error: Failed to create source image from: $source_path");
                return null;
            }
            
            // Create thumbnail canvas
            $thumbnail = @imagecreatetruecolor($new_width, $new_height);
            if (!$thumbnail) {
                imagedestroy($source);
                error_log("Thumbnail error: Failed to create thumbnail canvas");
                return null;
            }
            
            // Preserve transparency for PNG and GIF
            if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }
            
            // Resize image
            $resize_result = @imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            if (!$resize_result) {
                imagedestroy($source);
                imagedestroy($thumbnail);
                error_log("Thumbnail error: Failed to resize image");
                return null;
            }
            
            // Save thumbnail
            $success = false;
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $success = @imagejpeg($thumbnail, $thumbnail_path, 85);
                    break;
                case IMAGETYPE_PNG:
                    $success = @imagepng($thumbnail, $thumbnail_path);
                    break;
                case IMAGETYPE_GIF:
                    $success = @imagegif($thumbnail, $thumbnail_path);
                    break;
            }
            
            // Clean up memory
            imagedestroy($source);
            imagedestroy($thumbnail);

            if (!$success) {
                error_log("Thumbnail error: Failed to save thumbnail to: $thumbnail_path");
                return null;
            }

            error_log("Thumbnail success: Created thumbnail at: $thumbnail_path");
            return $thumbnail_path;

        } catch (Exception $e) {
            error_log("Thumbnail generation exception: " . $e->getMessage());
            return null;
        }
    }
}

if (!function_exists('generateThumbnailCompact')) {
    /**
     * Generate compact thumbnail (200x200 max) for smaller displays
     * 
     * @param string $source_path Path to source image
     * @param string $thumbnails_dir Directory to save thumbnail
     * @param string $filename Filename for thumbnail
     * @return string|null Path to generated thumbnail or null on failure
     */
    function generateThumbnailCompact($source_path, $thumbnails_dir, $filename) {
        try {
            $thumbnail_path = $thumbnails_dir . 'thumb_' . $filename;
            
            // Get image info
            $image_info = @getimagesize($source_path);
            if (!$image_info) {
                return null;
            }
            
            $width = $image_info[0];
            $height = $image_info[1];
            $type = $image_info[2];
            
            // Calculate thumbnail dimensions (max 200x200)
            $max_size = 200;
            if ($width > $height) {
                $new_width = $max_size;
                $new_height = ($height / $width) * $max_size;
            } else {
                $new_height = $max_size;
                $new_width = ($width / $height) * $max_size;
            }
            
            // Create source image
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $source = @imagecreatefromjpeg($source_path);
                    break;
                case IMAGETYPE_PNG:
                    $source = @imagecreatefrompng($source_path);
                    break;
                case IMAGETYPE_GIF:
                    $source = @imagecreatefromgif($source_path);
                    break;
                default:
                    return null;
            }
            
            if (!$source) {
                return null;
            }
            
            // Create thumbnail
            $thumbnail = @imagecreatetruecolor($new_width, $new_height);
            if (!$thumbnail) {
                imagedestroy($source);
                return null;
            }
            
            // Preserve transparency for PNG and GIF
            if ($type == IMAGETYPE_PNG || $type == IMAGETYPE_GIF) {
                imagealphablending($thumbnail, false);
                imagesavealpha($thumbnail, true);
                $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
                imagefill($thumbnail, 0, 0, $transparent);
            }
            
            @imagecopyresampled($thumbnail, $source, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
            
            // Save thumbnail
            $success = false;
            switch ($type) {
                case IMAGETYPE_JPEG:
                    $success = @imagejpeg($thumbnail, $thumbnail_path, 85);
                    break;
                case IMAGETYPE_PNG:
                    $success = @imagepng($thumbnail, $thumbnail_path);
                    break;
                case IMAGETYPE_GIF:
                    $success = @imagegif($thumbnail, $thumbnail_path);
                    break;
            }
            
            imagedestroy($source);
            imagedestroy($thumbnail);
            
            return $success ? $thumbnail_path : null;
            
        } catch (Exception $e) {
            error_log("Thumbnail generation error: " . $e->getMessage());
            return null;
        }
    }
}
?>
