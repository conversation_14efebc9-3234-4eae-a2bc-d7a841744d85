<?php
/**
 * Admin Email Notifications
 * 
 * This file contains functions for sending email notifications related to admin user management
 * and RSVP notifications to admin users. It integrates with the existing email infrastructure.
 */

// Ensure this file is included only once
if (!defined('ADMIN_EMAIL_NOTIFICATIONS_LOADED')) {
    define('ADMIN_EMAIL_NOTIFICATIONS_LOADED', true);

    /**
     * Check if admin notifications are enabled
     *
     * @param string $notificationType Type of notification to check
     * @return bool Whether notifications are enabled
     */
    function isAdminNotificationEnabled($notificationType) {
        global $pdo;

        try {
            $stmt = $pdo->prepare("SELECT setting_value FROM admin_settings WHERE setting_name = ?");
            $stmt->execute([$notificationType]);
            $result = $stmt->fetchColumn();
            return $result === '1' || $result === 1;
        } catch (Exception $e) {
            // If settings table doesn't exist or error occurs, default to enabled
            return true;
        }
    }

    /**
     * Generate proper admin dashboard URL
     *
     * @return string Complete URL to admin dashboard
     */
    function getAdminDashboardUrl() {
        // Try to get site URL from settings
        $siteUrl = get_site_setting('site_url', '');

        // If no site URL is configured, build one from current request
        if (empty($siteUrl)) {
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $scriptPath = dirname($_SERVER['SCRIPT_NAME'] ?? '');

            // Remove any trailing admin path and add church path
            $basePath = str_replace('/admin', '', $scriptPath);
            if (!str_ends_with($basePath, '/church')) {
                $basePath = rtrim($basePath, '/') . '/church';
            }

            $siteUrl = $protocol . '://' . $host . $basePath;
        }

        // Ensure no double slashes and proper admin path
        $siteUrl = rtrim($siteUrl, '/');
        return $siteUrl . '/admin/dashboard.php';
    }

    /**
     * Replace admin email template placeholders
     *
     * @param string $content Template content
     * @param array $data Data for placeholder replacement
     * @return string Processed content
     */
    function replaceAdminTemplatePlaceholders($content, $data) {
        if (empty($content) || !is_array($data)) {
            return $content;
        }

        // Handle conditional placeholders first - more complex pattern to handle HTML inside
        $content = preg_replace_callback('/\{(\w+)\s*\?\s*"([^"]*?)"\s*:\s*"([^"]*?)"\}/', function($matches) use ($data) {
            $key = $matches[1];
            $trueValue = $matches[2];
            $falseValue = $matches[3];

            return !empty($data[$key]) ? $trueValue : $falseValue;
        }, $content);

        // Replace all simple placeholders in the format {key}
        foreach ($data as $key => $value) {
            $placeholder = '{' . $key . '}';
            // Convert null values to empty string
            $value = $value ?? '';
            $content = str_replace($placeholder, $value, $content);
        }

        return $content;
    }

    /**
     * Send email notification when a new admin user is created
     * 
     * @param int $adminUserId The ID of the newly created admin user
     * @param int $createdByAdminId The ID of the admin who created the user
     * @param string|null $temporaryPassword Temporary password if generated
     * @return bool Success status
     */
    function sendAdminUserCreationNotification($adminUserId, $createdByAdminId, $temporaryPassword = null) {
        global $pdo;

        // Check if admin creation notifications are enabled
        if (!isAdminNotificationEnabled('admin_creation_notifications_enabled')) {
            return true; // Return true to indicate no error, just disabled
        }

        try {
            // Get admin user details
            $stmt = $pdo->prepare("
                SELECT id, username, email, full_name, default_role, created_at
                FROM admins 
                WHERE id = ?
            ");
            $stmt->execute([$adminUserId]);
            $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$adminUser) {
                error_log("Admin user not found for notification: $adminUserId");
                return false;
            }
            
            // Get creator details
            $stmt = $pdo->prepare("SELECT full_name FROM admins WHERE id = ?");
            $stmt->execute([$createdByAdminId]);
            $createdBy = $stmt->fetchColumn() ?: 'System Administrator';
            
            // Prepare email data
            $emailData = [
                'admin_name' => $adminUser['full_name'],
                'admin_username' => $adminUser['username'],
                'admin_email' => $adminUser['email'],
                'created_by' => $createdBy,
                'created_date' => date('F j, Y g:i A', strtotime($adminUser['created_at'])),
                'temporary_password' => $temporaryPassword,
                'dashboard_url' => getAdminDashboardUrl(),
                'organization_name' => get_organization_name(),
                'login_instructions' => $temporaryPassword ?
                    'Please log in with your username and the temporary password provided. You will be prompted to change your password on first login.' :
                    'Please contact your administrator for login credentials if you have not received them separately.'
            ];
            
            // Get email template
            $template = getEmailTemplate('admin_user_creation');
            if (!$template) {
                // Fallback to default template
                $template = getDefaultAdminCreationTemplate();
            }
            
            // Process template
            $subject = replaceAdminTemplatePlaceholders($template['subject'], $emailData);
            $content = replaceAdminTemplatePlaceholders($template['content'], $emailData);
            
            // Send email
            $result = sendEmail(
                $adminUser['email'],
                $adminUser['full_name'],
                $subject,
                $content,
                true,
                $emailData
            );
            
            // Log the notification
            if ($result) {
                logAdminNotification('admin_creation', $adminUserId, $createdByAdminId, 
                    "Admin user creation notification sent to {$adminUser['email']}");
            } else {
                error_log("Failed to send admin creation notification to {$adminUser['email']}");
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Error sending admin user creation notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send email notification when roles are assigned to an admin user
     * 
     * @param int $adminUserId The admin user receiving role assignments
     * @param array $roleIds Array of role IDs being assigned
     * @param int $assignedByAdminId The admin who made the assignments
     * @return bool Success status
     */
    function sendAdminRoleAssignmentNotification($adminUserId, $roleIds, $assignedByAdminId) {
        global $pdo;

        // Check if role assignment notifications are enabled
        if (!isAdminNotificationEnabled('role_assignment_notifications_enabled')) {
            return true; // Return true to indicate no error, just disabled
        }

        try {
            // Get admin user details
            $stmt = $pdo->prepare("
                SELECT id, username, email, full_name
                FROM admins 
                WHERE id = ?
            ");
            $stmt->execute([$adminUserId]);
            $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$adminUser) {
                error_log("Admin user not found for role assignment notification: $adminUserId");
                return false;
            }
            
            // Get assigner details
            $stmt = $pdo->prepare("SELECT full_name FROM admins WHERE id = ?");
            $stmt->execute([$assignedByAdminId]);
            $assignedBy = $stmt->fetchColumn() ?: 'System Administrator';
            
            // Get details of newly assigned roles
            $placeholders = str_repeat('?,', count($roleIds) - 1) . '?';
            $stmt = $pdo->prepare("
                SELECT role_name, role_display_name, role_description
                FROM user_roles 
                WHERE id IN ($placeholders)
            ");
            $stmt->execute($roleIds);
            $newRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get complete role details and permissions
            $roleDetails = getAdminRoleDetails($adminUserId);
            
            // Prepare email data
            $emailData = [
                'admin_name' => $adminUser['full_name'],
                'admin_username' => $adminUser['username'],
                'assigned_by' => $assignedBy,
                'assignment_date' => date('F j, Y g:i A'),
                'new_roles_list' => formatAdminRolesList($newRoles),
                'all_roles_list' => formatAdminRolesList($roleDetails['roles']),
                'permissions_list' => formatAdminPermissionsList($roleDetails['permissions']),
                'dashboard_url' => getAdminDashboardUrl(),
                'organization_name' => get_organization_name()
            ];
            
            // Get email template
            $template = getEmailTemplate('admin_role_assignment');
            if (!$template) {
                // Fallback to default template
                $template = getDefaultRoleAssignmentTemplate();
            }
            
            // Process template
            $subject = replaceAdminTemplatePlaceholders($template['subject'], $emailData);
            $content = replaceAdminTemplatePlaceholders($template['content'], $emailData);
            
            // Send email
            $result = sendEmail(
                $adminUser['email'],
                $adminUser['full_name'],
                $subject,
                $content,
                true,
                $emailData
            );
            
            // Log the notification
            if ($result) {
                logAdminNotification('role_assignment', $adminUserId, $assignedByAdminId, 
                    "Role assignment notification sent to {$adminUser['email']} for roles: " . implode(', ', array_column($newRoles, 'role_name')));
            } else {
                error_log("Failed to send role assignment notification to {$adminUser['email']}");
            }
            
            return $result;
            
        } catch (Exception $e) {
            error_log("Error sending admin role assignment notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email notification when an admin user is assigned to an event
     *
     * @param int $adminUserId The admin user being assigned
     * @param int $eventId The event ID
     * @param string $roleType The role type (coordinator, organizer)
     * @param int $assignedByAdminId The admin who made the assignment
     * @return bool Success status
     */
    function sendEventAssignmentNotification($adminUserId, $eventId, $roleType, $assignedByAdminId) {
        global $pdo;

        // Check if event assignment notifications are enabled
        if (!isAdminNotificationEnabled('event_assignment_notifications_enabled')) {
            return true; // Return true to indicate no error, just disabled
        }

        try {
            // Get admin user details
            $stmt = $pdo->prepare("SELECT username, email, full_name FROM admins WHERE id = ?");
            $stmt->execute([$adminUserId]);
            $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$adminUser) {
                error_log("Admin user not found for event assignment notification: $adminUserId");
                return false;
            }

            // Get event details
            $stmt = $pdo->prepare("SELECT title, event_date, location, description FROM events WHERE id = ?");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$event) {
                error_log("Event not found for assignment notification: $eventId");
                return false;
            }

            // Get assigner details
            $stmt = $pdo->prepare("SELECT full_name FROM admins WHERE id = ?");
            $stmt->execute([$assignedByAdminId]);
            $assignedBy = $stmt->fetchColumn() ?: 'System Administrator';

            // Prepare email data
            $emailData = [
                'admin_name' => $adminUser['full_name'],
                'admin_username' => $adminUser['username'],
                'event_title' => $event['title'],
                'event_date' => date('F j, Y g:i A', strtotime($event['event_date'])),
                'event_location' => $event['location'],
                'event_description' => $event['description'],
                'role_type' => ucfirst($roleType),
                'assigned_by' => $assignedBy,
                'assignment_date' => date('F j, Y g:i A'),
                'dashboard_url' => getAdminDashboardUrl(),
                'organization_name' => get_organization_name()
            ];

            // Get email template
            $template = getEmailTemplate('admin_event_assignment');
            if (!$template) {
                // Fallback to default template
                $template = getDefaultEventAssignmentTemplate();
            }

            // Process template
            $subject = replaceAdminTemplatePlaceholders($template['subject'], $emailData);
            $content = replaceAdminTemplatePlaceholders($template['content'], $emailData);

            // Send email
            $result = sendEmail(
                $adminUser['email'],
                $adminUser['full_name'],
                $subject,
                $content,
                true,
                $emailData
            );

            // Log the notification
            if ($result) {
                logAdminNotification('event_assignment', $adminUserId, $assignedByAdminId,
                    "Event assignment notification sent to {$adminUser['email']} for event: {$event['title']} as {$roleType}");
            } else {
                error_log("Failed to send event assignment notification to {$adminUser['email']}");
            }

            return $result;

        } catch (Exception $e) {
            error_log("Error sending event assignment notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send email notification when an admin user is assigned to a session
     *
     * @param int $adminUserId The admin user being assigned
     * @param int $sessionId The session ID
     * @param int $assignedByAdminId The admin who made the assignment
     * @return bool Success status
     */
    function sendSessionAssignmentNotification($adminUserId, $sessionId, $assignedByAdminId) {
        global $pdo;

        error_log("DEBUG: sendSessionAssignmentNotification called with adminUserId: $adminUserId, sessionId: $sessionId, assignedByAdminId: $assignedByAdminId");

        // Check if session assignment notifications are enabled
        $notificationEnabled = isAdminNotificationEnabled('session_assignment_notifications_enabled');
        error_log("DEBUG: Session assignment notifications enabled: " . ($notificationEnabled ? 'YES' : 'NO'));

        if (!$notificationEnabled) {
            error_log("DEBUG: Session assignment notifications are disabled, returning true");
            return true; // Return true to indicate no error, just disabled
        }

        try {
            // Get admin user details
            $stmt = $pdo->prepare("SELECT username, email, full_name FROM admins WHERE id = ?");
            $stmt->execute([$adminUserId]);
            $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$adminUser) {
                error_log("DEBUG: Admin user not found for session assignment notification: $adminUserId");
                return false;
            }

            error_log("DEBUG: Admin user found: " . $adminUser['full_name'] . " (" . $adminUser['email'] . ")");

            // Get session and event details
            $stmt = $pdo->prepare("
                SELECT es.session_title, es.description, es.start_datetime, es.end_datetime, es.location,
                       e.title as event_title, e.event_date
                FROM event_sessions es
                JOIN events e ON es.event_id = e.id
                WHERE es.id = ?
            ");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$session) {
                error_log("Session not found for assignment notification: $sessionId");
                return false;
            }

            // Get assigner details
            $stmt = $pdo->prepare("SELECT full_name FROM admins WHERE id = ?");
            $stmt->execute([$assignedByAdminId]);
            $assignedBy = $stmt->fetchColumn() ?: 'System Administrator';

            // Prepare email data
            $emailData = [
                'admin_name' => $adminUser['full_name'],
                'admin_username' => $adminUser['username'],
                'session_title' => $session['session_title'],
                'session_description' => $session['description'],
                'session_start' => date('F j, Y g:i A', strtotime($session['start_datetime'])),
                'session_end' => date('F j, Y g:i A', strtotime($session['end_datetime'])),
                'session_location' => $session['location'],
                'event_title' => $session['event_title'],
                'assigned_by' => $assignedBy,
                'assignment_date' => date('F j, Y g:i A'),
                'dashboard_url' => getAdminDashboardUrl(),
                'organization_name' => get_organization_name()
            ];

            // Get email template
            $template = getEmailTemplate('admin_session_assignment');
            if (!$template) {
                // Fallback to default template
                $template = getDefaultSessionAssignmentTemplate();
            }

            // Process template
            $subject = replaceAdminTemplatePlaceholders($template['subject'], $emailData);
            $content = replaceAdminTemplatePlaceholders($template['content'], $emailData);

            // Send email
            error_log("DEBUG: Attempting to send email to: " . $adminUser['email'] . " with subject: " . $subject);
            $result = sendEmail(
                $adminUser['email'],
                $adminUser['full_name'],
                $subject,
                $content,
                true,
                $emailData
            );

            error_log("DEBUG: Email send result: " . ($result ? 'SUCCESS' : 'FAILED'));

            // Log the notification
            if ($result) {
                logAdminNotification('session_assignment', $adminUserId, $assignedByAdminId,
                    "Session assignment notification sent to {$adminUser['email']} for session: {$session['session_title']}");
                error_log("DEBUG: Session assignment notification logged successfully");
            } else {
                error_log("Failed to send session assignment notification to {$adminUser['email']}");
            }

            return $result;

        } catch (Exception $e) {
            error_log("Error sending session assignment notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send RSVP notification to relevant admin users
     * 
     * @param array $rsvpData RSVP details
     * @param int $eventId Event ID
     * @param int|null $sessionId Session ID (if applicable)
     * @return bool Success status
     */
    function sendRSVPNotificationToAdmins($rsvpData, $eventId, $sessionId = null) {
        global $pdo;

        // Check if RSVP notifications are enabled
        if (!isAdminNotificationEnabled('rsvp_notifications_enabled')) {
            return true; // Return true to indicate no error, just disabled
        }

        try {
            // Get relevant admin users
            $adminUsers = [];
            
            if ($sessionId) {
                // Get session-specific admins
                $sessionAdmins = getAdminsForSessionNotification($sessionId);
                $adminUsers = array_merge($adminUsers, $sessionAdmins);
            }
            
            // Get event-specific admins
            $eventAdmins = getAdminsForEventNotification($eventId);
            $adminUsers = array_merge($adminUsers, $eventAdmins);
            
            // Remove duplicates
            $adminUsers = array_unique($adminUsers, SORT_REGULAR);
            
            if (empty($adminUsers)) {
                error_log("No admin users found for RSVP notification - Event: $eventId, Session: $sessionId");
                return false;
            }
            
            // Get event/session details
            $eventDetails = getEventDetails($eventId);
            $sessionDetails = $sessionId ? getSessionDetails($sessionId) : null;
            
            // Get current attendance counts
            $attendanceCounts = getAttendanceCounts($eventId, $sessionId);
            
            // Prepare email data
            $emailData = [
                'rsvp_name' => $rsvpData['name'],
                'rsvp_email' => $rsvpData['email'],
                'rsvp_phone' => $rsvpData['phone'] ?? 'Not provided',
                'rsvp_status' => ucfirst($rsvpData['status']),
                'rsvp_date' => date('F j, Y g:i A'),
                'party_size' => $rsvpData['party_size'] ?? 1,
                'special_requirements' => $rsvpData['special_requirements'] ?? 'None',
                'event_title' => $eventDetails['title'],
                'event_date' => date('F j, Y g:i A', strtotime($eventDetails['start_datetime'])),
                'session_title' => $sessionDetails ? $sessionDetails['title'] : null,
                'session_date' => $sessionDetails ? date('F j, Y g:i A', strtotime($sessionDetails['start_datetime'])) : null,
                'current_attending' => $attendanceCounts['attending'],
                'current_waitlist' => $attendanceCounts['waitlist'],
                'max_capacity' => $sessionDetails ? $sessionDetails['max_attendees'] : $eventDetails['max_attendees'],
                'organization_name' => get_organization_name(),
                'admin_dashboard_url' => getAdminDashboardUrl()
            ];
            
            // Get email template
            $template = getEmailTemplate('admin_rsvp_notification');
            if (!$template) {
                // Fallback to default template
                $template = getDefaultRSVPNotificationTemplate();
            }
            
            // Send to each admin
            $successCount = 0;
            foreach ($adminUsers as $admin) {
                // Add admin-specific data
                $adminEmailData = array_merge($emailData, [
                    'admin_name' => $admin['full_name']
                ]);
                
                // Process template
                $subject = replaceAdminTemplatePlaceholders($template['subject'], $adminEmailData);
                $content = replaceAdminTemplatePlaceholders($template['content'], $adminEmailData);
                
                // Send email
                $result = sendEmail(
                    $admin['email'],
                    $admin['full_name'],
                    $subject,
                    $content,
                    true,
                    $adminEmailData
                );
                
                if ($result) {
                    $successCount++;
                    logAdminNotification('rsvp_notification', $admin['id'], null, 
                        "RSVP notification sent for {$rsvpData['name']} - Event: {$eventDetails['title']}");
                } else {
                    error_log("Failed to send RSVP notification to admin {$admin['email']}");
                }
            }
            
            return $successCount > 0;
            
        } catch (Exception $e) {
            error_log("Error sending RSVP notification to admins: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get admin users who should receive notifications for a specific event
     *
     * @param int $eventId Event ID
     * @return array Array of admin user details
     */
    function getAdminsForEventNotification($eventId) {
        global $pdo;

        try {
            // Get admins assigned to this event (coordinators and organizers)
            $stmt = $pdo->prepare("
                SELECT DISTINCT a.id, a.email, a.full_name, a.username, ea.role_type
                FROM admins a
                JOIN event_assignments ea ON a.id = ea.user_id
                WHERE ea.event_id = ? AND ea.is_active = 1
                ORDER BY a.full_name
            ");
            $stmt->execute([$eventId]);
            $assignedAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get super admins (if configured to receive notifications)
            $includeSuperAdmins = get_site_setting('notify_super_admins_rsvp', '1');
            if ($includeSuperAdmins === '1') {
                $stmt = $pdo->prepare("
                    SELECT DISTINCT a.id, a.email, a.full_name, a.username, 'super_admin' as role_type
                    FROM admins a
                    JOIN user_role_assignments ura ON a.id = ura.user_id
                    JOIN user_roles ur ON ura.role_id = ur.id
                    WHERE ur.role_name = 'super_admin' AND ura.is_active = 1
                    ORDER BY a.full_name
                ");
                $stmt->execute();
                $superAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $assignedAdmins = array_merge($assignedAdmins, $superAdmins);
            }

            return $assignedAdmins;

        } catch (Exception $e) {
            error_log("Error getting admins for event notification: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get admin users who should receive notifications for a specific session
     *
     * @param int $sessionId Session ID
     * @return array Array of admin user details
     */
    function getAdminsForSessionNotification($sessionId) {
        global $pdo;

        try {
            // Get admins assigned to this session (session moderators)
            $stmt = $pdo->prepare("
                SELECT DISTINCT a.id, a.email, a.full_name, a.username, 'session_moderator' as role_type
                FROM admins a
                JOIN session_assignments sa ON a.id = sa.user_id
                WHERE sa.session_id = ? AND sa.is_active = 1
                ORDER BY a.full_name
            ");
            $stmt->execute([$sessionId]);
            $sessionAdmins = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Also get event coordinators for the event this session belongs to
            $stmt = $pdo->prepare("
                SELECT DISTINCT a.id, a.email, a.full_name, a.username, ea.role_type
                FROM admins a
                JOIN event_assignments ea ON a.id = ea.user_id
                JOIN event_sessions es ON ea.event_id = es.event_id
                WHERE es.id = ? AND ea.is_active = 1 AND ea.role_type = 'coordinator'
                ORDER BY a.full_name
            ");
            $stmt->execute([$sessionId]);
            $coordinators = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return array_merge($sessionAdmins, $coordinators);

        } catch (Exception $e) {
            error_log("Error getting admins for session notification: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get detailed role and permission information for an admin user
     *
     * @param int $adminUserId Admin user ID
     * @return array Array with 'roles' and 'permissions' keys
     */
    function getAdminRoleDetails($adminUserId) {
        global $pdo;

        try {
            // Get roles
            $stmt = $pdo->prepare("
                SELECT ur.role_name, ur.role_display_name, ur.role_description, ur.hierarchy_level
                FROM user_roles ur
                JOIN user_role_assignments ura ON ur.id = ura.role_id
                WHERE ura.user_id = ? AND ura.is_active = 1
                ORDER BY ur.hierarchy_level
            ");
            $stmt->execute([$adminUserId]);
            $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get permissions
            $stmt = $pdo->prepare("
                SELECT DISTINCT p.permission_name, p.permission_description, p.category
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                JOIN user_role_assignments ura ON rp.role_id = ura.role_id
                WHERE ura.user_id = ? AND ura.is_active = 1
                ORDER BY p.category, p.permission_name
            ");
            $stmt->execute([$adminUserId]);
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return [
                'roles' => $roles,
                'permissions' => $permissions
            ];

        } catch (Exception $e) {
            error_log("Error getting admin role details: " . $e->getMessage());
            return ['roles' => [], 'permissions' => []];
        }
    }

    /**
     * Format admin roles list for email display
     *
     * @param array $roles Array of role details
     * @return string Formatted HTML list
     */
    function formatAdminRolesList($roles) {
        if (empty($roles)) {
            return '<li>No roles assigned</li>';
        }

        $html = '';
        foreach ($roles as $role) {
            $html .= '<li><strong>' . htmlspecialchars($role['role_display_name']) . '</strong>';
            if (!empty($role['role_description'])) {
                $html .= ' - ' . htmlspecialchars($role['role_description']);
            }
            $html .= '</li>';
        }

        return $html;
    }

    /**
     * Format admin permissions list for email display
     *
     * @param array $permissions Array of permission details
     * @return string Formatted HTML list grouped by category
     */
    function formatAdminPermissionsList($permissions) {
        if (empty($permissions)) {
            return '<li>No specific permissions assigned</li>';
        }

        // Group permissions by category
        $grouped = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'] ?: 'General';
            $grouped[$category][] = $permission;
        }

        $html = '';
        foreach ($grouped as $category => $categoryPermissions) {
            $html .= '<li><strong>' . htmlspecialchars(ucfirst($category)) . ':</strong><ul>';
            foreach ($categoryPermissions as $permission) {
                $html .= '<li>' . htmlspecialchars($permission['permission_description'] ?: $permission['permission_name']) . '</li>';
            }
            $html .= '</ul></li>';
        }

        return $html;
    }

    /**
     * Get event details for notifications
     *
     * @param int $eventId Event ID
     * @return array Event details
     */
    function getEventDetails($eventId) {
        global $pdo;

        try {
            $stmt = $pdo->prepare("
                SELECT id, title, description, start_datetime, end_datetime, max_attendees
                FROM events
                WHERE id = ?
            ");
            $stmt->execute([$eventId]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (Exception $e) {
            error_log("Error getting event details: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get session details for notifications
     *
     * @param int $sessionId Session ID
     * @return array Session details
     */
    function getSessionDetails($sessionId) {
        global $pdo;

        try {
            $stmt = $pdo->prepare("
                SELECT id, title, description, start_datetime, end_datetime, max_attendees
                FROM event_sessions
                WHERE id = ?
            ");
            $stmt->execute([$sessionId]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: [];
        } catch (Exception $e) {
            error_log("Error getting session details: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get attendance counts for event/session
     *
     * @param int $eventId Event ID
     * @param int|null $sessionId Session ID (optional)
     * @return array Attendance counts
     */
    function getAttendanceCounts($eventId, $sessionId = null) {
        global $pdo;

        try {
            if ($sessionId) {
                // Session attendance counts
                $stmt = $pdo->prepare("
                    SELECT
                        COUNT(CASE WHEN status = 'attending' THEN 1 END) as attending,
                        COUNT(CASE WHEN status = 'waitlist' THEN 1 END) as waitlist,
                        COUNT(CASE WHEN status = 'not_attending' THEN 1 END) as not_attending
                    FROM session_rsvps_guests
                    WHERE session_id = ?
                ");
                $stmt->execute([$sessionId]);
            } else {
                // Event attendance counts (both members and guests)
                $stmt = $pdo->prepare("
                    SELECT
                        (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'attending') +
                        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending') as attending,
                        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'waitlist') as waitlist,
                        (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ? AND status = 'not_attending') +
                        (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ? AND status = 'not_attending') as not_attending
                ");
                $stmt->execute([$eventId, $eventId, $eventId, $eventId, $eventId]);
            }

            $counts = $stmt->fetch(PDO::FETCH_ASSOC);
            return [
                'attending' => (int)($counts['attending'] ?? 0),
                'waitlist' => (int)($counts['waitlist'] ?? 0),
                'not_attending' => (int)($counts['not_attending'] ?? 0)
            ];

        } catch (Exception $e) {
            error_log("Error getting attendance counts: " . $e->getMessage());
            return ['attending' => 0, 'waitlist' => 0, 'not_attending' => 0];
        }
    }

    /**
     * Get email template by name
     *
     * @param string $templateName Template name
     * @return array|null Template data
     */
    function getEmailTemplate($templateName) {
        global $pdo;

        try {
            $stmt = $pdo->prepare("
                SELECT id, template_name, subject, content
                FROM email_templates
                WHERE template_name = ? AND template_category = 'admin_notifications'
            ");
            $stmt->execute([$templateName]);
            return $stmt->fetch(PDO::FETCH_ASSOC) ?: null;
        } catch (Exception $e) {
            error_log("Error getting email template: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Log admin notification activity
     *
     * @param string $type Notification type
     * @param int $adminId Admin user ID
     * @param int|null $triggeredBy ID of user who triggered the notification
     * @param string $message Log message
     */
    function logAdminNotification($type, $adminId, $triggeredBy, $message) {
        $logFile = __DIR__ . '/../logs/admin_email_notifications.log';
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[$timestamp] [$type] Admin: $adminId, Triggered by: $triggeredBy - $message\n";
        file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Get default admin creation email template
     *
     * @return array Default template
     */
    function getDefaultAdminCreationTemplate() {
        return [
            'subject' => 'Welcome to {organization_name} - Admin Account Created',
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background-color: #007bff; color: white; padding: 20px; text-align: center;">
                        <h1>Welcome to {organization_name}</h1>
                        <p>Your Administrator Account Has Been Created</p>
                    </div>

                    <div style="padding: 20px; background-color: #f8f9fa;">
                        <h2>Hello {admin_name},</h2>

                        <p>An administrator account has been created for you at {organization_name} by {created_by} on {created_date}.</p>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>Account Details:</h3>
                            <ul>
                                <li><strong>Username:</strong> {admin_username}</li>
                                <li><strong>Email:</strong> {admin_email}</li>
                                {temporary_password ? "<li><strong>Temporary Password:</strong> {temporary_password}</li>" : ""}
                            </ul>
                        </div>



                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Admin Dashboard</a>
                        </div>

                        <div style="background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h4>Login Instructions:</h4>
                            <p>{login_instructions}</p>
                        </div>

                        <p>If you have any questions or need assistance, please contact your system administrator.</p>

                        <p>Best regards,<br>The {organization_name} Team</p>
                    </div>

                    <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>'
        ];
    }

    /**
     * Get default role assignment email template
     *
     * @return array Default template
     */
    function getDefaultRoleAssignmentTemplate() {
        return [
            'subject' => 'Role Assignment Update - {organization_name}',
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background-color: #28a745; color: white; padding: 20px; text-align: center;">
                        <h1>Role Assignment Update</h1>
                        <p>{organization_name}</p>
                    </div>

                    <div style="padding: 20px; background-color: #f8f9fa;">
                        <h2>Hello {admin_name},</h2>

                        <p>Your administrator roles have been updated by {assigned_by} on {assignment_date}.</p>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>Newly Assigned Roles:</h3>
                            <ul>{new_roles_list}</ul>
                        </div>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>All Your Current Roles:</h3>
                            <ul>{all_roles_list}</ul>
                        </div>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>Your Updated Permissions:</h3>
                            <ul>{permissions_list}</ul>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Access Admin Dashboard</a>
                        </div>

                        <p>Your new permissions are now active. Please log out and log back in to ensure all changes take effect.</p>

                        <p>If you have any questions about your new roles or permissions, please contact your system administrator.</p>

                        <p>Best regards,<br>The {organization_name} Team</p>
                    </div>

                    <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>'
        ];
    }

    /**
     * Get default RSVP notification email template
     *
     * @return array Default template
     */
    function getDefaultRSVPNotificationTemplate() {
        return [
            'subject' => 'New RSVP: {rsvp_name} - {event_title}',
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                    <div style="background-color: #17a2b8; color: white; padding: 20px; text-align: center;">
                        <h1>New RSVP Notification</h1>
                        <p>{organization_name}</p>
                    </div>

                    <div style="padding: 20px; background-color: #f8f9fa;">
                        <h2>Hello {admin_name},</h2>

                        <p>A new RSVP has been submitted that requires your attention.</p>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>RSVP Details:</h3>
                            <ul>
                                <li><strong>Name:</strong> {rsvp_name}</li>
                                <li><strong>Email:</strong> {rsvp_email}</li>
                                <li><strong>Phone:</strong> {rsvp_phone}</li>
                                <li><strong>Status:</strong> {rsvp_status}</li>
                                <li><strong>Party Size:</strong> {party_size}</li>
                                <li><strong>RSVP Date:</strong> {rsvp_date}</li>
                            </ul>
                            {special_requirements !== "None" ? "<p><strong>Special Requirements:</strong> {special_requirements}</p>" : ""}
                        </div>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>Event Information:</h3>
                            <ul>
                                <li><strong>Event:</strong> {event_title}</li>
                                <li><strong>Date:</strong> {event_date}</li>
                                {session_title ? "<li><strong>Session:</strong> {session_title}</li>" : ""}
                                {session_date ? "<li><strong>Session Date:</strong> {session_date}</li>" : ""}
                            </ul>
                        </div>

                        <div style="background-color: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                            <h3>Current Attendance:</h3>
                            <ul>
                                <li><strong>Attending:</strong> {current_attending}</li>
                                <li><strong>Waitlist:</strong> {current_waitlist}</li>
                                {max_capacity ? "<li><strong>Max Capacity:</strong> {max_capacity}</li>" : ""}
                            </ul>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{admin_dashboard_url}" style="background-color: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">View in Admin Dashboard</a>
                        </div>

                        <p>Please review this RSVP and take any necessary actions through your admin dashboard.</p>

                        <p>Best regards,<br>The {organization_name} Team</p>
                    </div>

                    <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>'
        ];
    }

    /**
     * Get default event assignment email template
     */
    function getDefaultEventAssignmentTemplate() {
        return [
            'subject' => 'Event Assignment - {event_title}',
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa; padding: 20px;">
                    <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">Event Assignment Notification</h1>
                            <p style="color: #7f8c8d; margin: 10px 0 0 0;">You have been assigned to an event</p>
                        </div>

                        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                            <h2 style="color: #2980b9; margin: 0 0 15px 0; font-size: 18px;">Hello {admin_name},</h2>
                            <p style="margin: 0; line-height: 1.6; color: #34495e;">
                                You have been assigned as <strong>{role_type}</strong> for the following event by {assigned_by} on {assignment_date}.
                            </p>
                        </div>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                            <h3 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 16px;">Event Details</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold; width: 30%;">Event:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{event_title}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Date:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{event_date}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Location:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{event_location}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Your Role:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;"><strong>{role_type}</strong></td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404; font-size: 14px;">
                                <strong>Responsibilities:</strong> As the assigned {role_type}, you will be responsible for coordinating and managing this event. Please review the event details and prepare accordingly. Contact the church leadership if you need any additional information or resources.
                            </p>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                                Access Admin Dashboard
                            </a>
                        </div>

                        <div style="border-top: 1px solid #dee2e6; padding-top: 20px; margin-top: 30px;">
                            <p style="margin: 0; color: #6c757d; font-size: 14px; line-height: 1.5;">
                                If you have any questions about this assignment, please contact the administrator who assigned you or reach out to the church leadership.
                            </p>
                        </div>
                    </div>

                    <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>'
        ];
    }

    /**
     * Get default session assignment email template
     */
    function getDefaultSessionAssignmentTemplate() {
        return [
            'subject' => 'Session Assignment - {session_title}',
            'content' => '
                <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa; padding: 20px;">
                    <div style="background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <h1 style="color: #2c3e50; margin: 0; font-size: 24px;">Session Assignment Notification</h1>
                            <p style="color: #7f8c8d; margin: 10px 0 0 0;">You have been assigned to moderate a session</p>
                        </div>

                        <div style="background-color: #e8f4fd; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                            <h2 style="color: #2980b9; margin: 0 0 15px 0; font-size: 18px;">Hello {admin_name},</h2>
                            <p style="margin: 0; line-height: 1.6; color: #34495e;">
                                You have been assigned as <strong>Session Moderator</strong> for the following session by {assigned_by} on {assignment_date}.
                            </p>
                        </div>

                        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 25px;">
                            <h3 style="color: #2c3e50; margin: 0 0 15px 0; font-size: 16px;">Session Details</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold; width: 30%;">Session:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{session_title}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Event:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{event_title}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Start Time:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{session_start}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">End Time:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{session_end}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; color: #7f8c8d; font-weight: bold;">Location:</td>
                                    <td style="padding: 8px 0; color: #2c3e50;">{session_location}</td>
                                </tr>
                            </table>
                        </div>

                        <div style="background-color: #fff3cd; padding: 15px; border-radius: 6px; margin-bottom: 25px; border-left: 4px solid #ffc107;">
                            <p style="margin: 0; color: #856404; font-size: 14px;">
                                <strong>Description:</strong> {session_description}
                            </p>
                        </div>

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{dashboard_url}" style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                                Access Admin Dashboard
                            </a>
                        </div>

                        <div style="border-top: 1px solid #dee2e6; padding-top: 20px; margin-top: 30px;">
                            <p style="margin: 0; color: #6c757d; font-size: 14px; line-height: 1.5;">
                                As a session moderator, you will be responsible for managing this session. If you have any questions, please contact the administrator who assigned you.
                            </p>
                        </div>
                    </div>

                    <div style="background-color: #6c757d; color: white; padding: 10px; text-align: center; font-size: 12px;">
                        <p>This is an automated message. Please do not reply to this email.</p>
                    </div>
                </div>'
        ];
    }

} // End of ADMIN_EMAIL_NOTIFICATIONS_LOADED check

?>
