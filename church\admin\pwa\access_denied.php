<?php
/**
 * PWA Access Denied Page
 * Mobile-optimized access denied page for PWA
 */

// Start session
session_start();

// Include configuration
require_once '../../config.php';

// Get reason from URL parameter
$reason = $_GET['reason'] ?? 'You do not have permission to access the Mobile PWA';

$page_title = 'Access Denied - Mobile PWA';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title><?php echo $page_title; ?></title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#dc3545">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .pwa-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .pwa-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
        }
        
        .access-denied-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            color: white;
            font-size: 2.5rem;
        }
        
        .btn-pwa {
            border-radius: 15px;
            padding: 15px 30px;
            font-weight: 600;
            margin: 10px 0;
            width: 100%;
            transition: transform 0.2s;
        }
        
        .btn-pwa:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary-pwa {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border: none;
            color: white;
        }
        
        .btn-secondary-pwa {
            background: #6c757d;
            border: none;
            color: white;
        }
        
        .alert-pwa {
            border-radius: 15px;
            border: none;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="pwa-container">
        <div class="pwa-card">
            <div class="access-denied-icon">
                <i class="bi bi-shield-exclamation"></i>
            </div>
            
            <h2 class="text-danger mb-3">Access Denied</h2>
            
            <div class="alert alert-danger alert-pwa">
                <i class="bi bi-exclamation-triangle"></i>
                <?php echo htmlspecialchars($reason); ?>
            </div>
            
            <p class="text-muted mb-4">
                You don't have the required permissions to access the Mobile PWA interface.
            </p>
            
            <div class="d-grid gap-2">
                <a href="../dashboard.php" class="btn btn-primary-pwa btn-pwa">
                    <i class="bi bi-speedometer2"></i>
                    Go to Main Dashboard
                </a>
                
                <a href="../login.php" class="btn btn-secondary-pwa btn-pwa">
                    <i class="bi bi-box-arrow-in-right"></i>
                    Login Again
                </a>
                
                <button onclick="history.back()" class="btn btn-outline-secondary btn-pwa">
                    <i class="bi bi-arrow-left"></i>
                    Go Back
                </button>
            </div>
            
            <div class="mt-4 pt-3 border-top">
                <small class="text-muted">
                    <i class="bi bi-info-circle"></i>
                    Contact your administrator if you believe this is an error
                </small>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
