<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Page configuration
$page_title = 'Advanced Bulk Attendance';
$page_header = 'Advanced Bulk Attendance';
$page_description = 'Manage attendance across multiple sessions for ' . htmlspecialchars($event['title']);

$message = '';
$error = '';

// Handle bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'multi_session_bulk') {
            $selected_sessions = $_POST['selected_sessions'] ?? [];
            $selected_attendees = $_POST['selected_attendees'] ?? [];
            $bulk_action = $_POST['bulk_action'] ?? '';
            
            if (empty($selected_sessions)) {
                $error = "Please select at least one session.";
            } elseif (empty($selected_attendees)) {
                $error = "Please select at least one attendee.";
            } else {
                $pdo->beginTransaction();
                
                $affected_count = 0;
                foreach ($selected_sessions as $session_id) {
                    foreach ($selected_attendees as $attendee_id) {
                        // Check if attendance record exists
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance 
                            WHERE session_id = ? AND 
                            (member_id = ? OR CONCAT('guest_', id) = ?)
                        ");
                        $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                        $existing = $stmt->fetch();
                        
                        if ($existing) {
                            // Update existing record
                            $stmt = $pdo->prepare("
                                UPDATE session_attendance 
                                SET attendance_status = ?,
                                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                                WHERE id = ?
                            ");
                            $stmt->execute([$bulk_action, $bulk_action, $existing['id']]);
                            $affected_count++;
                        } else {
                            // Create new record if attendee is registered for event
                            if (is_numeric($attendee_id)) {
                                // Member
                                $stmt = $pdo->prepare("
                                    INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                                    VALUES (?, ?, ?, CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                                ");
                                $stmt->execute([$session_id, $attendee_id, $bulk_action, $bulk_action]);
                                $affected_count++;
                            }
                        }
                    }
                }
                
                $pdo->commit();
                $message = "Successfully updated attendance for {$affected_count} session-attendee combinations.";
            }
        } elseif ($action === 'smart_event_attendance') {
            $min_sessions = (int)($_POST['min_sessions'] ?? 1);
            $attendance_threshold = (float)($_POST['attendance_threshold'] ?? 50.0);
            
            // Get all event attendees and their session attendance
            $stmt = $pdo->prepare("
                SELECT 
                    COALESCE(er.user_id, erg.id) as attendee_id,
                    COALESCE(m.full_name, erg.guest_name) as attendee_name,
                    CASE WHEN er.user_id IS NOT NULL THEN 'member' ELSE 'guest' END as attendee_type,
                    COUNT(DISTINCT sa.session_id) as sessions_attended,
                    COUNT(DISTINCT es.id) as sessions_registered,
                    ROUND((COUNT(DISTINCT sa.session_id) / NULLIF(COUNT(DISTINCT es.id), 0)) * 100, 1) as attendance_percentage
                FROM (
                    SELECT user_id, 'member' as type FROM event_rsvps WHERE event_id = ? AND status = 'attending'
                    UNION ALL
                    SELECT id, 'guest' as type FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending'
                ) attendees
                LEFT JOIN event_rsvps er ON attendees.user_id = er.user_id AND attendees.type = 'member'
                LEFT JOIN event_rsvps_guests erg ON attendees.user_id = erg.id AND attendees.type = 'guest'
                LEFT JOIN members m ON er.user_id = m.id
                LEFT JOIN session_attendance sa ON (
                    (sa.member_id = er.user_id AND attendees.type = 'member') OR
                    (sa.guest_name = erg.guest_name AND attendees.type = 'guest')
                ) AND sa.attendance_status = 'attended'
                LEFT JOIN event_sessions es ON sa.session_id = es.id AND es.event_id = ?
                GROUP BY attendee_id, attendee_name, attendee_type
                HAVING sessions_attended >= ? OR attendance_percentage >= ?
            ");
            $stmt->execute([$event_id, $event_id, $event_id, $min_sessions, $attendance_threshold]);
            $qualified_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Mark these attendees as attended for the main event
            $updated_count = 0;
            foreach ($qualified_attendees as $attendee) {
                if ($attendee['attendee_type'] === 'member') {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps 
                        SET actually_attended = 1 
                        WHERE event_id = ? AND user_id = ?
                    ");
                    $stmt->execute([$event_id, $attendee['attendee_id']]);
                } else {
                    $stmt = $pdo->prepare("
                        UPDATE event_rsvps_guests 
                        SET actually_attended = 1 
                        WHERE event_id = ? AND id = ?
                    ");
                    $stmt->execute([$event_id, $attendee['attendee_id']]);
                }
                $updated_count++;
            }
            
            $message = "Smart attendance applied! Marked {$updated_count} attendees as attended for the main event based on session attendance.";
        }
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $error = "Error: " . $e->getMessage();
    }
}

// Get all sessions for this event
$stmt = $pdo->prepare("
    SELECT es.*, 
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as no_show_count
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all event attendees (members + guests)
$stmt = $pdo->prepare("
    SELECT 
        er.user_id as id,
        m.full_name as name,
        m.email,
        'member' as type,
        er.actually_attended
    FROM event_rsvps er
    JOIN members m ON er.user_id = m.id
    WHERE er.event_id = ? AND er.status = 'attending'
    
    UNION ALL
    
    SELECT 
        erg.id,
        erg.guest_name as name,
        erg.guest_email as email,
        'guest' as type,
        erg.actually_attended
    FROM event_rsvps_guests erg
    WHERE erg.event_id = ? AND erg.status = 'attending'
    
    ORDER BY name
");
$stmt->execute([$event_id, $event_id]);
$attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'includes/header.php';
?>

<!-- Modern CSS Styles -->
<style>
/* Main container styling */
.bulk-attendance-container {
    background: #f8f9fa;
    min-height: calc(100vh - 200px);
}

/* Hero section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.hero-stat {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.hero-stat h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* Smart attendance panel */
.smart-panel {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e3f2fd;
}

.smart-panel-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.smart-panel-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
}

/* Selection panels */
.selection-panel {
    background: white;
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    height: 100%;
}

.panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f0f0f0;
}

.panel-icon {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

.sessions-icon {
    background: linear-gradient(135deg, #2196F3, #1976D2);
}

.attendees-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

/* Selection controls */
.selection-controls {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.control-btn {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.selection-counter {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Session and attendee items */
.session-selector, .attendee-selector {
    max-height: 450px;
    overflow-y: auto;
    padding: 0.5rem;
}

.session-item, .attendee-item {
    background: #f8f9fa;
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.session-item:hover, .attendee-item:hover {
    background: #e3f2fd;
    border-color: #2196F3;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.2);
}

.session-item.selected, .attendee-item.selected {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-color: #2196F3;
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
}

.session-info h6 {
    color: #1976D2;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.session-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #666;
}

.attendee-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.attendee-name {
    font-weight: 600;
    color: #333;
}

.attendee-badges {
    display: flex;
    gap: 0.5rem;
}

.badge-custom {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Search box */
.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    border-radius: 25px;
    padding: 0.75rem 1rem 0.75rem 3rem;
    border: 2px solid #e0e0e0;
    transition: all 0.3s ease;
}

.search-box input:focus {
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

.search-box .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

/* Action panel */
.action-panel {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.action-header {
    text-align: center;
    margin-bottom: 2rem;
}

.action-header h4 {
    color: #333;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.bulk-action-select {
    border-radius: 15px;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.execute-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
}

.execute-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.4);
}

/* Responsive design */
@media (max-width: 768px) {
    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .selection-controls {
        flex-wrap: wrap;
    }

    .session-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    .panel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}
</style>

<div class="container-fluid bulk-attendance-container">
    <!-- Hero Section -->
    <div class="hero-section">
        <div class="d-flex justify-content-between align-items-start mb-3">
            <div>
                <h1 class="mb-2">
                    <i class="bi bi-diagram-3"></i> Advanced Bulk Attendance
                </h1>
                <p class="mb-0 opacity-90">
                    <strong><?php echo htmlspecialchars($event['title']); ?></strong> •
                    <?php echo date('M j, Y', strtotime($event['event_date'])); ?>
                </p>
            </div>
            <div class="d-flex gap-2">
                <a href="attendance_inheritance_engine.php?event_id=<?php echo $event_id; ?>" class="btn btn-light">
                    <i class="bi bi-diagram-3"></i> Inheritance Engine
                </a>
                <a href="multi_session_dashboard.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-light">
                    <i class="bi bi-arrow-left"></i> Back
                </a>
            </div>
        </div>

        <!-- Hero Stats -->
        <div class="hero-stats">
            <div class="hero-stat">
                <h3><?php echo count($sessions); ?></h3>
                <p class="mb-0">Total Sessions</p>
            </div>
            <div class="hero-stat">
                <h3><?php echo count($attendees); ?></h3>
                <p class="mb-0">Event Attendees</p>
            </div>
            <div class="hero-stat">
                <h3><?php echo array_sum(array_column($sessions, 'registered_count')); ?></h3>
                <p class="mb-0">Total Registrations</p>
            </div>
            <div class="hero-stat">
                <h3><?php echo array_sum(array_column($sessions, 'attended_count')); ?></h3>
                <p class="mb-0">Total Attendances</p>
            </div>
        </div>
    </div>

    <!-- Alert Messages -->
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Smart Event Attendance Panel -->
    <div class="smart-panel">
        <div class="smart-panel-header">
            <div class="smart-panel-icon">
                <i class="bi bi-lightbulb"></i>
            </div>
            <div>
                <h4 class="mb-1">Smart Event Attendance</h4>
                <p class="text-muted mb-0">Automatically mark event attendance based on session participation patterns</p>
            </div>
        </div>

        <form method="POST" class="row g-4">
            <input type="hidden" name="action" value="smart_event_attendance">

            <div class="col-md-4">
                <label class="form-label fw-semibold">Minimum Sessions Required</label>
                <input type="number" name="min_sessions" class="form-control form-control-lg" value="1" min="1" max="<?php echo count($sessions); ?>">
                <small class="text-muted">Mark as attended if attended at least this many sessions</small>
            </div>

            <div class="col-md-4">
                <label class="form-label fw-semibold">Attendance Threshold (%)</label>
                <input type="number" name="attendance_threshold" class="form-control form-control-lg" value="50" min="0" max="100" step="0.1">
                <small class="text-muted">Mark as attended if attended this % of registered sessions</small>
            </div>

            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn execute-btn w-100">
                    <i class="bi bi-magic"></i> Apply Smart Attendance
                </button>
            </div>
        </form>
    </div>

    <!-- Multi-Session Bulk Operations -->
    <div class="row g-4">
        <!-- Session Selection Panel -->
        <div class="col-lg-6">
            <div class="selection-panel">
                <div class="panel-header">
                    <div class="d-flex align-items-center">
                        <div class="panel-icon sessions-icon">
                            <i class="bi bi-calendar-event"></i>
                        </div>
                        <div>
                            <h5 class="mb-0">Select Sessions</h5>
                            <small class="text-muted">Choose sessions to apply bulk actions</small>
                        </div>
                    </div>
                    <div class="selection-counter" id="sessionCount">0 selected</div>
                </div>

                <div class="selection-controls">
                    <button type="button" class="btn btn-primary control-btn" onclick="selectAllSessions()">
                        <i class="bi bi-check-all"></i> Select All
                    </button>
                    <button type="button" class="btn btn-outline-secondary control-btn" onclick="clearSessionSelection()">
                        <i class="bi bi-x-circle"></i> Clear All
                    </button>
                </div>

                <div class="session-selector">
                    <?php foreach ($sessions as $session): ?>
                        <div class="session-item" onclick="toggleSession(<?php echo $session['id']; ?>)">
                            <div class="form-check">
                                <input class="form-check-input session-checkbox" type="checkbox"
                                       name="selected_sessions[]" value="<?php echo $session['id']; ?>"
                                       id="session_<?php echo $session['id']; ?>">
                                <label class="form-check-label w-100" for="session_<?php echo $session['id']; ?>">
                                    <div class="session-info">
                                        <h6><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                        <div class="session-meta">
                                            <span><i class="bi bi-clock"></i> <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?></span>
                                            <span><i class="bi bi-person-check"></i> <?php echo $session['registered_count']; ?> registered</span>
                                            <span><i class="bi bi-check-circle"></i> <?php echo $session['attended_count']; ?> attended</span>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Attendee Selection Panel -->
        <div class="col-lg-6">
            <div class="selection-panel">
                <div class="panel-header">
                    <div class="d-flex align-items-center">
                        <div class="panel-icon attendees-icon">
                            <i class="bi bi-people"></i>
                        </div>
                        <div>
                            <h5 class="mb-0">Select Attendees</h5>
                            <small class="text-muted">Choose attendees for bulk actions</small>
                        </div>
                    </div>
                    <div class="selection-counter" id="attendeeCount">0 selected</div>
                </div>

                <div class="selection-controls">
                    <button type="button" class="btn btn-primary control-btn" onclick="selectAllAttendees()">
                        <i class="bi bi-check-all"></i> Select All
                    </button>
                    <button type="button" class="btn btn-outline-secondary control-btn" onclick="clearAttendeeSelection()">
                        <i class="bi bi-x-circle"></i> Clear All
                    </button>
                </div>

                <div class="search-box">
                    <i class="bi bi-search search-icon"></i>
                    <input type="text" id="attendeeSearch" class="form-control" placeholder="Search attendees...">
                </div>

                <div class="attendee-selector">
                    <?php foreach ($attendees as $attendee): ?>
                        <div class="attendee-item" data-name="<?php echo strtolower($attendee['name']); ?>">
                            <div class="form-check">
                                <input class="form-check-input attendee-checkbox" type="checkbox"
                                       name="selected_attendees[]" value="<?php echo $attendee['id']; ?>"
                                       id="attendee_<?php echo $attendee['id']; ?>">
                                <label class="form-check-label w-100" for="attendee_<?php echo $attendee['id']; ?>">
                                    <div class="attendee-info">
                                        <div class="attendee-name"><?php echo htmlspecialchars($attendee['name']); ?></div>
                                        <div class="attendee-badges">
                                            <span class="badge-custom bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                                <?php echo ucfirst($attendee['type']); ?>
                                            </span>
                                            <?php if ($attendee['actually_attended']): ?>
                                                <span class="badge-custom bg-success">Event Attended</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions Panel -->
    <form method="POST" id="bulkForm">
        <input type="hidden" name="action" value="multi_session_bulk">

        <div class="action-panel">
            <div class="action-header">
                <h4><i class="bi bi-lightning"></i> Execute Bulk Actions</h4>
                <p class="text-muted">Apply actions to selected sessions and attendees</p>
            </div>

            <div class="row g-4 align-items-end">
                <div class="col-md-6">
                    <label class="form-label fw-semibold">Choose Action</label>
                    <select name="bulk_action" class="form-select bulk-action-select" required>
                        <option value="">Select an action...</option>
                        <option value="attended">✅ Mark as Attended</option>
                        <option value="no_show">❌ Mark as No-Show</option>
                        <option value="registered">🔄 Reset to Registered</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <button type="submit" class="btn execute-btn w-100" id="bulkSubmit" disabled>
                        <i class="bi bi-check2-all"></i> Apply to <span id="operationCount">0</span> Combinations
                    </button>
                </div>
            </div>

            <div class="text-center mt-3">
                <small class="text-muted">
                    This will update <strong><span id="operationCountText">0</span> session-attendee combinations</strong>
                </small>
            </div>
        </div>
    </form>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let selectedSessions = new Set();
let selectedAttendees = new Set();

function updateCounts() {
    document.getElementById('sessionCount').textContent = selectedSessions.size + ' selected';
    document.getElementById('attendeeCount').textContent = selectedAttendees.size + ' selected';

    const operationCount = selectedSessions.size * selectedAttendees.size;
    document.getElementById('operationCount').textContent = operationCount.toLocaleString();
    document.getElementById('operationCountText').textContent = operationCount.toLocaleString();

    const bulkSubmit = document.getElementById('bulkSubmit');
    bulkSubmit.disabled = selectedSessions.size === 0 || selectedAttendees.size === 0;

    // Update button text
    if (operationCount > 0) {
        bulkSubmit.innerHTML = `<i class="bi bi-check2-all"></i> Apply to ${operationCount.toLocaleString()} Combinations`;
    } else {
        bulkSubmit.innerHTML = `<i class="bi bi-check2-all"></i> Apply to 0 Combinations`;
    }
}

function toggleSession(sessionId) {
    const checkbox = document.getElementById('session_' + sessionId);
    checkbox.checked = !checkbox.checked;

    if (checkbox.checked) {
        selectedSessions.add(sessionId);
        checkbox.closest('.session-item').classList.add('selected');
    } else {
        selectedSessions.delete(sessionId);
        checkbox.closest('.session-item').classList.remove('selected');
    }

    updateCounts();
}

function selectAllSessions() {
    document.querySelectorAll('.session-checkbox').forEach(checkbox => {
        checkbox.checked = true;
        selectedSessions.add(parseInt(checkbox.value));
        checkbox.closest('.session-item').classList.add('selected');
    });
    updateCounts();
}

function clearSessionSelection() {
    document.querySelectorAll('.session-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.session-item').classList.remove('selected');
    });
    selectedSessions.clear();
    updateCounts();
}

function selectAllAttendees() {
    document.querySelectorAll('.attendee-checkbox:not([style*="display: none"])').forEach(checkbox => {
        checkbox.checked = true;
        selectedAttendees.add(checkbox.value);
        checkbox.closest('.attendee-item').classList.add('selected');
    });
    updateCounts();
}

function clearAttendeeSelection() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.attendee-item').classList.remove('selected');
    });
    selectedAttendees.clear();
    updateCounts();
}

// Attendee search functionality
document.getElementById('attendeeSearch').addEventListener('input', function(e) {
    const searchTerm = e.target.value.toLowerCase();
    document.querySelectorAll('.attendee-item').forEach(item => {
        const name = item.dataset.name;
        if (name.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Track checkbox changes
document.addEventListener('change', function(e) {
    if (e.target.classList.contains('session-checkbox')) {
        const sessionId = parseInt(e.target.value);
        if (e.target.checked) {
            selectedSessions.add(sessionId);
            e.target.closest('.session-item').classList.add('selected');
        } else {
            selectedSessions.delete(sessionId);
            e.target.closest('.session-item').classList.remove('selected');
        }
        updateCounts();
    }

    if (e.target.classList.contains('attendee-checkbox')) {
        const attendeeId = e.target.value;
        if (e.target.checked) {
            selectedAttendees.add(attendeeId);
            e.target.closest('.attendee-item').classList.add('selected');
        } else {
            selectedAttendees.delete(attendeeId);
            e.target.closest('.attendee-item').classList.remove('selected');
        }
        updateCounts();
    }
});

// Form validation
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    if (selectedSessions.size === 0) {
        e.preventDefault();
        alert('Please select at least one session.');
        return;
    }

    if (selectedAttendees.size === 0) {
        e.preventDefault();
        alert('Please select at least one attendee.');
        return;
    }

    const action = document.querySelector('select[name="bulk_action"]').value;
    if (!action) {
        e.preventDefault();
        alert('Please select a bulk action.');
        return;
    }

    const operationCount = selectedSessions.size * selectedAttendees.size;
    if (!confirm(`This will update ${operationCount.toLocaleString()} session-attendee combinations. Continue?`)) {
        e.preventDefault();
    }
});

// Initialize
updateCounts();
</script>

</div> <!-- End container-fluid -->

<?php include 'includes/footer.php'; ?>
