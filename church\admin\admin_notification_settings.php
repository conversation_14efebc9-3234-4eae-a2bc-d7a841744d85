<?php
/**
 * Admin Notification Settings
 * 
 * Configuration page for admin email notification system
 */

// Include the configuration file
require_once '../config.php';
require_once '../includes/admin_email_notifications.php';

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

$page_title = "Admin Notification Settings";
$page_header = "Admin Email Notification Settings";
$page_description = "Configure email notifications for admin users";

$message = '';
$error = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_settings'])) {
    try {
        $pdo->beginTransaction();
        
        // Update or insert settings
        $settings = [
            'admin_creation_notifications_enabled' => isset($_POST['admin_creation_notifications_enabled']) ? 1 : 0,
            'rsvp_notifications_enabled' => isset($_POST['rsvp_notifications_enabled']) ? 1 : 0,
            'role_assignment_notifications_enabled' => isset($_POST['role_assignment_notifications_enabled']) ? 1 : 0,
            'notification_roles' => json_encode($_POST['notification_roles'] ?? []),
            'rsvp_notification_roles' => json_encode($_POST['rsvp_notification_roles'] ?? [])
        ];
        
        foreach ($settings as $setting_name => $setting_value) {
            $stmt = $pdo->prepare("
                INSERT INTO admin_settings (setting_name, setting_value, updated_by, updated_at) 
                VALUES (?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                setting_value = VALUES(setting_value),
                updated_by = VALUES(updated_by),
                updated_at = VALUES(updated_at)
            ");
            $stmt->execute([$setting_name, $setting_value, $_SESSION['admin_id']]);
        }
        
        $pdo->commit();
        $message = "Settings updated successfully!";
        
    } catch (Exception $e) {
        $pdo->rollback();
        $error = "Error updating settings: " . $e->getMessage();
    }
}

// Get current settings
$currentSettings = [];
$stmt = $pdo->prepare("SELECT setting_name, setting_value FROM admin_settings WHERE setting_name LIKE '%notification%'");
$stmt->execute();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $currentSettings[$row['setting_name']] = $row['setting_value'];
}

// Get available roles
$stmt = $pdo->prepare("SELECT id, role_name, description FROM user_roles ORDER BY role_name");
$stmt->execute();
$availableRoles = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Parse JSON settings
$notificationRoles = json_decode($currentSettings['notification_roles'] ?? '[]', true) ?: [];
$rsvpNotificationRoles = json_decode($currentSettings['rsvp_notification_roles'] ?? '[]', true) ?: [];

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3>Admin Email Notification Settings</h3>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
                    <?php endif; ?>
                    
                    <?php if ($error): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <h4>Notification Types</h4>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="admin_creation_notifications_enabled" 
                                           id="admin_creation_notifications_enabled" value="1"
                                           <?php echo ($currentSettings['admin_creation_notifications_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="admin_creation_notifications_enabled">
                                        <strong>Admin User Creation Notifications</strong><br>
                                        <small class="text-muted">Send welcome emails when new admin users are created</small>
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="role_assignment_notifications_enabled" 
                                           id="role_assignment_notifications_enabled" value="1"
                                           <?php echo ($currentSettings['role_assignment_notifications_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="role_assignment_notifications_enabled">
                                        <strong>Role Assignment Notifications</strong><br>
                                        <small class="text-muted">Send emails when admin roles are assigned or updated</small>
                                    </label>
                                </div>
                                
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" name="rsvp_notifications_enabled" 
                                           id="rsvp_notifications_enabled" value="1"
                                           <?php echo ($currentSettings['rsvp_notifications_enabled'] ?? 0) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="rsvp_notifications_enabled">
                                        <strong>RSVP Notifications</strong><br>
                                        <small class="text-muted">Send notifications to admins when users RSVP to events/sessions</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h4>Admin Roles for General Notifications</h4>
                                <p class="text-muted">Select which admin roles should receive admin creation and role assignment notifications:</p>
                                
                                <?php foreach ($availableRoles as $role): ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="notification_roles[]" 
                                               value="<?php echo $role['id']; ?>" id="role_<?php echo $role['id']; ?>"
                                               <?php echo in_array($role['id'], $notificationRoles) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="role_<?php echo $role['id']; ?>">
                                            <strong><?php echo htmlspecialchars($role['role_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($role['description']); ?></small>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                                
                                <hr>
                                
                                <h4>Admin Roles for RSVP Notifications</h4>
                                <p class="text-muted">Select which admin roles should receive RSVP notifications (in addition to event-specific assignments):</p>
                                
                                <?php foreach ($availableRoles as $role): ?>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="rsvp_notification_roles[]" 
                                               value="<?php echo $role['id']; ?>" id="rsvp_role_<?php echo $role['id']; ?>"
                                               <?php echo in_array($role['id'], $rsvpNotificationRoles) ? 'checked' : ''; ?>>
                                        <label class="form-check-label" for="rsvp_role_<?php echo $role['id']; ?>">
                                            <strong><?php echo htmlspecialchars($role['role_name']); ?></strong><br>
                                            <small class="text-muted"><?php echo htmlspecialchars($role['description']); ?></small>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <h4>How RSVP Notifications Work</h4>
                                <div class="alert alert-info">
                                    <ul class="mb-0">
                                        <li><strong>Event RSVPs:</strong> Notifications are sent to admins with event-specific assignments (event coordinators, organizers) plus any roles selected above</li>
                                        <li><strong>Session RSVPs:</strong> Notifications are sent to admins with session-specific assignments (session moderators) plus event coordinators and any roles selected above</li>
                                        <li><strong>Role Hierarchy:</strong> Super admins always receive all notifications regardless of settings</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button type="submit" name="update_settings" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Settings
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
