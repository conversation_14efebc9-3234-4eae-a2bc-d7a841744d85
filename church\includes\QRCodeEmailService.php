<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../config.php';

use Endroid\QrCode\QrCode;
use Endroid\QrCode\Writer\PngWriter;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\SMTP;
use <PERSON><PERSON>Mailer\PHPMailer\Exception;

class QRCodeEmailService {
    private $pdo;
    private $config;

    public function __construct($pdo, $config = []) {
        $this->pdo = $pdo;
        $this->config = array_merge([
            'qr_size' => 300,
            'qr_margin' => 10,
            'base_url' => $this->getBaseUrl(),
            'from_email' => '<EMAIL>',
            'from_name' => 'Freedom Assembly Church',
            'session_qr_expires_hours' => 24,
            'enable_logging' => true
        ], $config);
    }

    /**
     * Get the base URL for QR code links - environment aware
     */
    private function getBaseUrl() {
        // Use the helper function if available (most reliable)
        if (function_exists('get_qr_base_url')) {
            return get_qr_base_url();
        }

        // Fallback: Environment-aware URL generation
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // Determine environment
        $environment = 'development';
        if (defined('ENVIRONMENT')) {
            $environment = ENVIRONMENT;
        } elseif (isset($_SERVER['SERVER_NAME'])) {
            if (strpos($_SERVER['SERVER_NAME'], 'localhost') === false &&
                strpos($_SERVER['SERVER_NAME'], '127.0.0.1') === false) {
                $environment = 'production';
            }
        }

        // For production, use the church application root directly
        if ($environment === 'production') {
            return $protocol . '://' . $host;
        }

        // For development, detect the church folder path
        $scriptPath = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptPath);

        // Clean up the path
        $basePath = rtrim($basePath, '/');

        // If we're in a subdirectory like /admin or /user, go up one level
        if (basename($basePath) === 'admin' || basename($basePath) === 'user') {
            $basePath = dirname($basePath);
        }

        // Remove the 'campaign' part if it exists (development only)
        if (strpos($basePath, '/campaign') !== false) {
            $basePath = str_replace('/campaign', '', $basePath);
        }

        // Handle root directory case
        if (empty($basePath) || $basePath === '.' || $basePath === '/') {
            $basePath = '';
        }

        return $protocol . '://' . $host . $basePath;
    }

    /**
     * Log activity if logging is enabled
     */
    private function log($message, $level = 'info') {
        if ($this->config['enable_logging']) {
            error_log("[QRCodeEmailService] [$level] $message");
        }
    }

    /**
     * Load SMTP settings from database
     */
    private function getEmailSettings() {
        $emailSettings = [
            'smtp_host' => '',
            'smtp_auth' => true,
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_secure' => 'ssl',
            'smtp_port' => 465,
            'sender_email' => $this->config['from_email'],
            'sender_name' => $this->config['from_name'],
            'reply_to_email' => $this->config['from_email']
        ];

        try {
            // Load from site_settings table (current admin panel configuration)
            $stmt = $this->pdo->prepare("SELECT setting_name, setting_value FROM site_settings WHERE setting_name IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'smtp_secure', 'smtp_auth', 'from_email', 'sender_email', 'reply_to_email')");
            $stmt->execute();
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $key = $row['setting_name'];
                $value = $row['setting_value'];

                // Map setting names to expected format
                if ($key === 'smtp_encryption' || $key === 'smtp_secure') {
                    $emailSettings['smtp_secure'] = $value;
                } elseif ($key === 'from_email' || $key === 'sender_email') {
                    $emailSettings['sender_email'] = $value;
                } else {
                    $emailSettings[$key] = $value;
                }
            }
        } catch (PDOException $e) {
            $this->log("Could not load site settings: " . $e->getMessage(), 'error');

            // Fallback to old email_settings table
            try {
                $stmt = $this->pdo->query("SELECT setting_key, setting_value FROM email_settings");
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $emailSettings[$row['setting_key']] = $row['setting_value'];
                }
            } catch (PDOException $e2) {
                $this->log("Could not load email_settings fallback: " . $e2->getMessage(), 'error');

                // Final fallback to settings table
                try {
                    $stmt = $this->pdo->prepare("SELECT setting_key, setting_value FROM settings WHERE setting_key IN ('smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_encryption', 'from_email', 'from_name', 'reply_to_email')");
                    $stmt->execute();
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        $key = $row['setting_key'];
                        if ($key === 'smtp_encryption') {
                            $emailSettings['smtp_secure'] = $row['setting_value'];
                        } elseif ($key === 'from_email') {
                            $emailSettings['sender_email'] = $row['setting_value'];
                        } elseif ($key === 'from_name') {
                            $emailSettings['sender_name'] = $row['setting_value'];
                        } else {
                            $emailSettings[$key] = $row['setting_value'];
                        }
                    }
                } catch (PDOException $e3) {
                    $this->log("Could not load settings fallback: " . $e3->getMessage(), 'error');
                }
            }
        }

        return $emailSettings;
    }
    
    /**
     * Check if a member/guest already has a QR code for an event
     */
    public function hasEventQRCode($event_id, $member_id = null, $guest_email = null) {
        if ($member_id) {
            $stmt = $this->pdo->prepare("
                SELECT id FROM member_qr_codes
                WHERE event_id = ? AND member_id = ?
            ");
            $stmt->execute([$event_id, $member_id]);
        } else {
            $stmt = $this->pdo->prepare("
                SELECT id FROM member_qr_codes
                WHERE event_id = ? AND guest_email = ?
            ");
            $stmt->execute([$event_id, $guest_email]);
        }
        return $stmt->fetch() !== false;
    }

    /**
     * Check if a session already has a QR code
     */
    public function hasSessionQRCode($session_id) {
        $stmt = $this->pdo->prepare("
            SELECT id FROM session_qr_codes
            WHERE session_id = ?
        ");
        $stmt->execute([$session_id]);
        return $stmt->fetch() !== false;
    }

    /**
     * Generate QR code and send confirmation email for session registration
     */
    public function generateAndSendSessionQRCode($session_id, $member_id, $guest_email = null, $guest_name = null) {
        try {
            // Get session details
            $stmt = $this->pdo->prepare("
                SELECT es.*, e.title as event_title, e.location as event_location
                FROM event_sessions es
                JOIN events e ON es.event_id = e.id
                WHERE es.id = ?
            ");
            $stmt->execute([$session_id]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$session) {
                throw new Exception("Session not found: $session_id");
            }

            // Get attendee details
            if ($member_id) {
                $stmt = $this->pdo->prepare("SELECT full_name, email FROM members WHERE id = ?");
                $stmt->execute([$member_id]);
                $attendee = $stmt->fetch(PDO::FETCH_ASSOC);
                $attendee_type = 'member';
                $attendee_name = $attendee['full_name'];
                $attendee_email = $attendee['email'];
            } else {
                $attendee_type = 'guest';
                $attendee_name = $guest_name;
                $attendee_email = $guest_email;
            }

            if (!$attendee_name || !$attendee_email) {
                throw new Exception("Attendee details not found");
            }

            // Check if person-specific session QR code already exists
            if ($member_id) {
                $stmt = $this->pdo->prepare("
                    SELECT * FROM session_attendance_qr_codes
                    WHERE session_id = ? AND member_id = ?
                ");
                $stmt->execute([$session_id, $member_id]);
            } else {
                $stmt = $this->pdo->prepare("
                    SELECT * FROM session_attendance_qr_codes
                    WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                ");
                $stmt->execute([$session_id, $guest_name, $guest_email]);
            }
            $existing_qr = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing_qr) {
                // Use existing person-specific session QR code
                $qr_token = $existing_qr['qr_token'];
                $this->log("Using existing session-specific QR code: $qr_token");
            } else {
                // Generate new person-specific session QR token
                $qr_token = 'SQR_' . $session_id . '_' . ($member_id ?: 'G') . '_' . bin2hex(random_bytes(12));
                $expires_at = date('Y-m-d H:i:s', max(strtotime($session['end_datetime']) + 86400, time() + 86400));

                // Insert person-specific session QR code record
                $stmt = $this->pdo->prepare("
                    INSERT INTO session_attendance_qr_codes
                    (session_id, member_id, guest_name, guest_email, qr_token, attendee_name, attendee_email, attendee_type, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $session_id,
                    $member_id,
                    $member_id ? null : $guest_name,
                    $member_id ? null : $guest_email,
                    $qr_token,
                    $attendee_name,
                    $attendee_email,
                    $attendee_type,
                    $expires_at
                ]);

                $this->log("Generated new session-specific QR code: $qr_token for {$attendee_name}");
            }

            // Send QR code email
            $attendee_data = [
                'name' => $attendee_name,
                'email' => $attendee_email,
                'type' => $attendee_type
            ];

            $email_sent = $this->sendSessionQRCodeEmail($attendee_data, $qr_token, $session);

            return $email_sent;

        } catch (Exception $e) {
            error_log("Session QR Code Email Service Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate QR code and send confirmation email for event registration
     */
    public function generateAndSendEventQRCode($event_id, $member_id = null, $guest_email = null, $guest_name = null) {
        try {
            // Get event details
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$event) {
                throw new Exception("Event not found: $event_id");
            }
            
            // Get attendee details
            if ($member_id) {
                $stmt = $this->pdo->prepare("SELECT full_name, email FROM members WHERE id = ?");
                $stmt->execute([$member_id]);
                $attendee = $stmt->fetch(PDO::FETCH_ASSOC);
                $attendee_type = 'member';
                $attendee_name = $attendee['full_name'];
                $attendee_email = $attendee['email'];
            } else {
                $attendee_type = 'guest';
                $attendee_name = $guest_name;
                $attendee_email = $guest_email;
            }
            
            if (!$attendee_name || !$attendee_email) {
                throw new Exception("Attendee details not found");
            }
            
            // Check if QR code already exists
            $stmt = $this->pdo->prepare("
                SELECT * FROM member_qr_codes 
                WHERE event_id = ? AND attendee_email = ?
            ");
            $stmt->execute([$event_id, $attendee_email]);
            $existing_qr = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing_qr) {
                // Use existing QR code
                $qr_token = $existing_qr['qr_token'];
            } else {
                // Generate new QR token
                $qr_token = 'QR_' . $event_id . '_' . bin2hex(random_bytes(16));
                
                // Insert QR code record
                $stmt = $this->pdo->prepare("
                    INSERT INTO member_qr_codes 
                    (event_id, member_id, guest_email, qr_token, attendee_name, attendee_email, attendee_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $event_id,
                    $member_id,
                    $member_id ? null : $attendee_email,
                    $qr_token,
                    $attendee_name,
                    $attendee_email,
                    $attendee_type
                ]);
            }
            
            // Send QR code email
            $attendee_data = [
                'name' => $attendee_name,
                'email' => $attendee_email,
                'type' => $attendee_type
            ];
            
            $email_sent = $this->sendQRCodeEmail($attendee_data, $qr_token, $event);
            
            if ($email_sent) {
                // Mark email as sent
                $stmt = $this->pdo->prepare("
                    UPDATE member_qr_codes 
                    SET email_sent = 1, email_sent_at = NOW() 
                    WHERE qr_token = ?
                ");
                $stmt->execute([$qr_token]);
            }
            
            return $email_sent;
            
        } catch (Exception $e) {
            error_log("QR Code Email Service Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send QR code email with embedded QR image
     */
    private function sendQRCodeEmail($attendee, $qr_token, $event) {
        try {
            // Generate QR code URL - completely dynamic
            if (function_exists('generate_qr_checkin_url')) {
                $full_qr_url = generate_qr_checkin_url($qr_token, 'member');
            } else {
                // Fallback: Use the most dynamic approach possible
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

                // Use environment-aware URL generation
                $baseUrl = $this->getBaseUrl();

                $full_qr_url = $baseUrl . '/admin/member_checkin.php?token=' . $qr_token;
            }

            // Debug log the generated URL
            error_log("QR Code URL generated: " . $full_qr_url);
            
            // Generate QR code image and save it as a file
            $qrCode = new QrCode($full_qr_url);
            $qrCode->setSize($this->config['qr_size']);
            $qrCode->setMargin($this->config['qr_margin']);

            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            // Save QR code as temporary file for email attachment
            $qr_filename = "qr_event_{$event_id}_" . time() . ".png";
            $qr_filepath = __DIR__ . "/../uploads/" . $qr_filename;

            // Ensure uploads directory exists
            $uploads_dir = __DIR__ . "/../uploads/";
            if (!is_dir($uploads_dir)) {
                mkdir($uploads_dir, 0755, true);
            }

            file_put_contents($qr_filepath, $result->getString());

            $this->log("Generated QR code for event $event_id, saved as: $qr_filename");
            
            // Email content
            $subject = "🎟️ Your Event Check-in Code - " . $event['title'];
            $message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>Event Check-in Code</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 24px; }
                    .content { padding: 30px 20px; background: white; }
                    .checkin-section { text-align: center; margin: 30px 0; padding: 30px; background: #e8f5e8; border-radius: 15px; border: 3px solid #28a745; }
                    .checkin-button { display: inline-block; padding: 20px 40px; background: #28a745; color: white; text-decoration: none; border-radius: 10px; font-size: 20px; font-weight: bold; margin: 20px 0; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
                    .qr-code-box { background: white; border: 3px solid #007bff; padding: 30px; margin: 20px 0; border-radius: 10px; }
                    .qr-token { font-size: 18px; font-weight: bold; color: #007bff; letter-spacing: 2px; word-break: break-all; }
                    .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
                    .instructions h3 { margin-top: 0; color: #1976d2; }
                    .event-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
                    .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h1>🎟️ Your Event Check-in Code</h1>
                        <p>Freedom Assembly Church</p>
                    </div>

                    <div class='content'>
                        <h2>Hello " . htmlspecialchars($attendee['name']) . "!</h2>
                        <p>You're all set for <strong>" . htmlspecialchars($event['title']) . "</strong>! Here's your personal QR code for quick check-in.</p>

                        <div class='event-details'>
                            <h3>📅 Event Details</h3>
                            <p><strong>Event:</strong> " . htmlspecialchars($event['title']) . "</p>
                            <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($event['event_date'])) . "</p>
                            <p><strong>Location:</strong> " . htmlspecialchars($event['location']) . "</p>
                        </div>

                        <div class='checkin-section'>
                            <h2>🚀 INSTANT CHECK-IN</h2>
                            <p style='font-size: 18px; margin: 20px 0;'><strong>Click the button below OR scan the QR code:</strong></p>

                            <a href='{$full_qr_url}' class='checkin-button' style='color: white; text-decoration: none;'>
                                ✅ CHECK IN NOW
                            </a>

                            <div class='qr-code-box'>
                                <h3>📱 Your Personal QR Code</h3>
                                <img src='cid:qrcode' alt='QR Code for Check-in' style='max-width: 250px; height: auto; border: 2px solid #ddd; border-radius: 8px;' />
                                <div class='qr-token'>" . htmlspecialchars($qr_token) . "</div>
                                <p style='margin: 15px 0 5px 0; font-size: 14px; color: #666;'>Scan this QR code or show it to staff</p>
                            </div>
                        </div>

                        <div class='instructions'>
                            <h3>📱 Three Ways to Check In</h3>
                            <ol style='font-size: 16px; line-height: 1.6;'>
                                <li><strong>EASIEST:</strong> Click the green \"CHECK IN NOW\" button above</li>
                                <li><strong>SCAN:</strong> Show the QR code above to staff for scanning</li>
                                <li><strong>MANUAL:</strong> Visit the check-in station and provide your name</li>
                            </ol>
                        </div>

                        <div class='highlight'>
                            <h3>⚡ Super Fast Check-in Process</h3>
                            <p><strong>Two instant options:</strong> Click the button for immediate check-in, or show your QR code to staff for scanning. Both work perfectly!</p>
                        </div>

                        <p style='font-size: 16px; margin: 30px 0;'>We're excited to see you at the event! The new check-in system will get you inside quickly so you don't miss anything.</p>

                        <p><strong>Blessings,</strong><br>
                        Freedom Assembly Church Event Team</p>
                    </div>

                    <div class='footer'>
                        <p>This is an automated message from Freedom Assembly Church</p>
                        <p>Event Management System | QR Code Check-in</p>
                        <p><strong>Direct Check-in Link:</strong><br>
                        <a href='{$full_qr_url}' style='color: #007bff; word-break: break-all;'>{$full_qr_url}</a></p>
                    </div>
                </div>
            </body>
            </html>
            ";
            
            // Send email using PHPMailer with SMTP and embedded QR code
            return $this->sendEmailWithSMTP($attendee['email'], $subject, $message, 'Event QR Code', $qr_filepath);
            
        } catch (Exception $e) {
            error_log("Error sending QR code email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send session QR code email with embedded QR image
     */
    private function sendSessionQRCodeEmail($attendee, $qr_token, $session) {
        try {
            // Generate QR code URL for session check-in - completely dynamic
            if (function_exists('generate_qr_checkin_url')) {
                $full_qr_url = generate_qr_checkin_url($qr_token, 'session');
            } else {
                // Fallback: Use the most dynamic approach possible
                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

                // Use environment-aware URL generation
                $baseUrl = $this->getBaseUrl();

                $full_qr_url = $baseUrl . '/admin/mobile_checkin.php?token=' . $qr_token;
            }

            // Debug log the generated URL
            error_log("Session QR Code URL generated: " . $full_qr_url);

            // Generate QR code image and save it as a file
            $qrCode = new QrCode($full_qr_url);
            $qrCode->setSize($this->config['qr_size']);
            $qrCode->setMargin($this->config['qr_margin']);

            $writer = new PngWriter();
            $result = $writer->write($qrCode);

            // Save QR code as temporary file for email attachment
            $qr_filename = "qr_session_{$session['id']}_" . time() . ".png";
            $qr_filepath = __DIR__ . "/../uploads/" . $qr_filename;

            // Ensure uploads directory exists
            $uploads_dir = __DIR__ . "/../uploads/";
            if (!is_dir($uploads_dir)) {
                mkdir($uploads_dir, 0755, true);
            }

            file_put_contents($qr_filepath, $result->getString());

            $this->log("Generated QR code for session {$session['id']}, saved as: $qr_filename");

            // Email content for session
            $subject = "🎟️ Your Session Check-in Code - " . $session['session_title'];
            $message = "
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset='UTF-8'>
                <meta name='viewport' content='width=device-width, initial-scale=1.0'>
                <title>Session Check-in Code</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 0; background-color: #f4f4f4; }
                    .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                    .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px 20px; text-align: center; }
                    .header h1 { margin: 0; font-size: 24px; }
                    .content { padding: 30px 20px; background: white; }
                    .checkin-section { text-align: center; margin: 30px 0; padding: 30px; background: #e8f5e8; border-radius: 15px; border: 3px solid #28a745; }
                    .checkin-button { display: inline-block; padding: 20px 40px; background: #28a745; color: white; text-decoration: none; border-radius: 10px; font-size: 20px; font-weight: bold; margin: 20px 0; box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
                    .qr-code-box { background: white; border: 3px solid #007bff; padding: 30px; margin: 20px 0; border-radius: 10px; }
                    .qr-token { font-size: 18px; font-weight: bold; color: #007bff; letter-spacing: 2px; word-break: break-all; }
                    .instructions { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2196f3; }
                    .instructions h3 { margin-top: 0; color: #1976d2; }
                    .session-details { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }
                    .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px; }
                    .highlight { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }
                </style>
            </head>
            <body>
                <div class='container'>
                    <div class='header'>
                        <h1>🎟️ Your Session Check-in Code</h1>
                        <p>Freedom Assembly Church</p>
                    </div>

                    <div class='content'>
                        <h2>Hello " . htmlspecialchars($attendee['name']) . "!</h2>
                        <p>You're registered for <strong>" . htmlspecialchars($session['session_title']) . "</strong>! Here's your personal QR code for quick check-in.</p>

                        <div class='session-details'>
                            <h3>📅 Session Details</h3>
                            <p><strong>Session:</strong> " . htmlspecialchars($session['session_title']) . "</p>
                            <p><strong>Event:</strong> " . htmlspecialchars($session['event_title']) . "</p>
                            <p><strong>Date:</strong> " . date('F j, Y g:i A', strtotime($session['start_datetime'])) . "</p>
                            <p><strong>Location:</strong> " . htmlspecialchars($session['location'] ?: $session['event_location']) . "</p>
                        </div>

                        <div class='checkin-section'>
                            <h2>🚀 INSTANT CHECK-IN</h2>
                            <p style='font-size: 18px; margin: 20px 0;'><strong>Click the button below OR scan the QR code:</strong></p>

                            <a href='{$full_qr_url}' class='checkin-button' style='color: white; text-decoration: none;'>
                                ✅ CHECK IN NOW
                            </a>

                            <div class='qr-code-box'>
                                <h3>📱 Your Personal QR Code</h3>
                                <img src='cid:qrcode' alt='QR Code for Check-in' style='max-width: 250px; height: auto; border: 2px solid #ddd; border-radius: 8px;' />
                                <div class='qr-token'>" . htmlspecialchars($qr_token) . "</div>
                                <p style='margin: 15px 0 5px 0; font-size: 14px; color: #666;'>Scan this QR code or show it to staff</p>
                            </div>
                        </div>

                        <div class='instructions'>
                            <h3>📱 Three Ways to Check In</h3>
                            <ol style='font-size: 16px; line-height: 1.6;'>
                                <li><strong>EASIEST:</strong> Click the green \"CHECK IN NOW\" button above</li>
                                <li><strong>SCAN:</strong> Show the QR code above to staff for scanning</li>
                                <li><strong>MANUAL:</strong> Visit the check-in station and provide your name</li>
                            </ol>
                        </div>

                        <div class='highlight'>
                            <h3>⚡ Super Fast Check-in Process</h3>
                            <p><strong>Two instant options:</strong> Click the button for immediate check-in, or show your QR code to staff for scanning. Both work perfectly!</p>
                        </div>

                        <p style='font-size: 16px; margin: 30px 0;'>We're excited to see you at the session! The new check-in system will get you inside quickly so you don't miss anything.</p>

                        <p><strong>Blessings,</strong><br>
                        Freedom Assembly Church Event Team</p>
                    </div>

                    <div class='footer'>
                        <p>This is an automated message from Freedom Assembly Church</p>
                        <p>Session Management System | QR Code Check-in</p>
                        <p><strong>Direct Check-in Link:</strong><br>
                        <a href='{$full_qr_url}' style='color: #007bff; word-break: break-all;'>{$full_qr_url}</a></p>
                    </div>
                </div>
            </body>
            </html>
            ";

            // Send email using PHPMailer with SMTP and embedded QR code
            return $this->sendEmailWithSMTP($attendee['email'], $subject, $message, 'Session QR Code', $qr_filepath);

        } catch (Exception $e) {
            error_log("Error sending session QR code email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get QR code statistics for an event
     */
    public function getEventQRStats($event_id) {
        $stmt = $this->pdo->prepare("
            SELECT
                COUNT(*) as total_qr_codes,
                COUNT(CASE WHEN email_sent = 1 THEN 1 END) as emails_sent,
                COUNT(CASE WHEN is_used = 1 THEN 1 END) as qr_codes_used,
                COUNT(CASE WHEN attendee_type = 'member' THEN 1 END) as member_qr_codes,
                COUNT(CASE WHEN attendee_type = 'guest' THEN 1 END) as guest_qr_codes
            FROM member_qr_codes
            WHERE event_id = ?
        ");
        $stmt->execute([$event_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Get QR code statistics for a session
     */
    public function getSessionQRStats($session_id) {
        $stmt = $this->pdo->prepare("
            SELECT
                COUNT(*) as total_qr_codes,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_qr_codes,
                SUM(scan_count) as total_scans,
                MAX(scan_count) as max_scans
            FROM session_qr_codes
            WHERE session_id = ?
        ");
        $stmt->execute([$session_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Resend QR code emails for attendees who haven't received them
     */
    public function resendPendingEventQREmails($event_id) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM member_qr_codes
            WHERE event_id = ? AND email_sent = 0
        ");
        $stmt->execute([$event_id]);
        $pending_qr_codes = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $success_count = 0;
        $total_count = count($pending_qr_codes);

        foreach ($pending_qr_codes as $qr_data) {
            $attendee = [
                'name' => $qr_data['attendee_name'],
                'email' => $qr_data['attendee_email'],
                'type' => $qr_data['attendee_type']
            ];

            // Get event details
            $stmt = $this->pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($event && $this->sendQRCodeEmail($attendee, $qr_data['qr_token'], $event)) {
                // Mark email as sent
                $stmt = $this->pdo->prepare("
                    UPDATE member_qr_codes
                    SET email_sent = 1, email_sent_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$qr_data['id']]);
                $success_count++;
            }
        }

        $this->log("Resent $success_count out of $total_count pending QR emails for event $event_id");

        return [
            'total' => $total_count,
            'sent' => $success_count,
            'failed' => $total_count - $success_count
        ];
    }

    /**
     * Clean up expired session QR codes
     */
    public function cleanupExpiredSessionQRCodes() {
        $stmt = $this->pdo->prepare("
            UPDATE session_qr_codes
            SET is_active = 0
            WHERE expires_at < NOW() AND is_active = 1
        ");
        $stmt->execute();
        $affected = $stmt->rowCount();

        $this->log("Deactivated $affected expired session QR codes");

        return $affected;
    }

    /**
     * Send email using PHPMailer with SMTP configuration from database
     */
    private function sendEmailWithSMTP($to_email, $subject, $html_body, $email_type = 'QR Code', $qr_filepath = null) {
        try {
            // Get SMTP settings from database
            $emailSettings = $this->getEmailSettings();

            // Create PHPMailer instance
            $mail = new PHPMailer(true);

            // Server settings
            $mail->isSMTP();
            $mail->Host = $emailSettings['smtp_host'];
            $mail->SMTPAuth = true;
            $mail->Username = $emailSettings['smtp_username'];
            $mail->Password = $emailSettings['smtp_password'];

            // Handle port and encryption
            $port = intval($emailSettings['smtp_port']);
            if ($port == 465) {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;  // SSL for port 465
                $mail->Port = 465;
            } else {
                $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;  // TLS for port 587
                $mail->Port = 587;
            }

            // Recipients
            $mail->setFrom($emailSettings['sender_email'], $emailSettings['sender_name']);
            $mail->addAddress($to_email);
            $mail->addReplyTo($emailSettings['reply_to_email'], $emailSettings['sender_name']);

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $html_body;
            $mail->CharSet = 'UTF-8';

            // Add QR code as embedded attachment if provided
            if ($qr_filepath && file_exists($qr_filepath)) {
                $mail->addEmbeddedImage($qr_filepath, 'qrcode', basename($qr_filepath), 'base64', 'image/png');
                $this->log("Added embedded QR code image: " . basename($qr_filepath));
            }

            // Send the email
            $result = $mail->send();

            // Clean up temporary QR code file
            if ($qr_filepath && file_exists($qr_filepath)) {
                unlink($qr_filepath);
                $this->log("Cleaned up temporary QR file: " . basename($qr_filepath));
            }

            if ($result) {
                $this->log("$email_type email sent successfully to: $to_email");
            } else {
                $this->log("Failed to send $email_type email to: $to_email", 'error');
            }

            return $result;

        } catch (Exception $e) {
            $this->log("SMTP Error sending $email_type email to $to_email: " . $e->getMessage(), 'error');
            return false;
        }
    }
}
