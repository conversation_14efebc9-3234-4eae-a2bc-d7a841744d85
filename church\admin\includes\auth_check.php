<?php
/**
 * Admin Authentication Check
 * Ensures admin is logged in and has appropriate access
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Include configuration for database connection
require_once dirname(__DIR__) . '/../config.php';

// Check if admin is logged in (using correct session variables set by login.php)
if (!isset($_SESSION['admin_id']) || !isset($_SESSION['admin_role'])) {
    // Redirect to login page
    header('Location: ../login.php');
    exit();
}

// Super admin has access to everything - bypass all other checks
if ($_SESSION['admin_role'] === 'super_admin') {
    // Set admin info for use in pages
    $current_admin_id = $_SESSION['admin_id'];
    $current_admin_role = $_SESSION['admin_role'];
    $current_admin_name = $_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'Super Admin';

    // Optional: Set timezone if available
    if (isset($_SESSION['timezone'])) {
        date_default_timezone_set($_SESSION['timezone']);
    } else {
        date_default_timezone_set('America/New_York'); // Default timezone
    }

    return; // Super admin bypasses all further checks
}

// For non-super admin users, check RBAC permissions
if (file_exists('includes/rbac_access_control.php')) {
    require_once 'includes/rbac_access_control.php';

    try {
        $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id']);

        // Check if user has any admin-level permissions
        $admin_roles = ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'];
        $has_admin_access = false;

        foreach ($admin_roles as $role) {
            if ($rbac->hasRole($role)) {
                $has_admin_access = true;
                break;
            }
        }

        if (!$has_admin_access) {
            header('Location: ../login.php?error=access_denied');
            exit();
        }
    } catch (Exception $e) {
        // If RBAC fails, fall back to basic session check for known admin roles
        $valid_admin_roles = ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'];
        if (!in_array($_SESSION['admin_role'], $valid_admin_roles)) {
            header('Location: ../login.php?error=access_denied');
            exit();
        }
    }
} else {
    // No RBAC system, use basic session check for known admin roles
    $valid_admin_roles = ['super_admin', 'limited_admin', 'event_coordinator', 'organizer', 'session_moderator', 'staff'];
    if (!in_array($_SESSION['admin_role'], $valid_admin_roles)) {
        header('Location: ../login.php?error=access_denied');
        exit();
    }
}

// Set admin info for use in pages
$current_admin_id = $_SESSION['admin_id'];
$current_admin_role = $_SESSION['admin_role'];
$current_admin_name = $_SESSION['admin_name'] ?? $_SESSION['admin_username'] ?? 'Admin User';

// Optional: Set timezone if available
if (isset($_SESSION['timezone'])) {
    date_default_timezone_set($_SESSION['timezone']);
} else {
    date_default_timezone_set('America/New_York'); // Default timezone
}
?>
