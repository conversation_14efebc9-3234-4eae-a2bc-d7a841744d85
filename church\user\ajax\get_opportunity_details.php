<?php
session_start();
require_once '../../config.php';

// Check if user is authenticated
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Check if opportunity ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid opportunity ID']);
    exit;
}

$opportunityId = (int)$_GET['id'];

try {
    // Fetch opportunity details with contact information
    $stmt = $pdo->prepare("
        SELECT vo.*, m.full_name as contact_name, m.email as contact_email,
               COUNT(DISTINCT va.id) as application_count,
               COUNT(DISTINCT CASE WHEN va.status = 'approved' THEN va.id END) as filled_positions
        FROM volunteer_opportunities vo
        LEFT JOIN members m ON vo.contact_person_id = m.id
        LEFT JOIN volunteer_applications va ON vo.id = va.opportunity_id
        WHERE vo.id = ? AND vo.status = 'active'
        GROUP BY vo.id
    ");
    
    $stmt->execute([$opportunityId]);
    $opportunity = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$opportunity) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Opportunity not found']);
        exit;
    }
    
    // Return the opportunity details
    echo json_encode([
        'success' => true,
        'opportunity' => $opportunity
    ]);
    
} catch (Exception $e) {
    error_log("Error fetching opportunity details: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error']);
}
?>
