<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Scanner - Campaign Church</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-size: 1.1rem;
        }
        .mobile-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .scanner-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        
        /* Camera Scanner Styles */
        .camera-preview {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            pointer-events: none;
        }
        
        .scanner-frame {
            width: 200px;
            height: 200px;
            border: 3px solid #28a745;
            border-radius: 12px;
            position: relative;
            background: rgba(40, 167, 69, 0.1);
        }
        
        .scanner-frame::before,
        .scanner-frame::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #28a745;
        }
        
        .scanner-frame::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }
        
        .scanner-frame::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }
        
        #camera-video {
            background: #000;
        }
    </style>
</head>
<body>

<div class="mobile-container">
    <div class="card scanner-card">
        <div class="card-body text-center py-5">
            <i class="bi bi-camera text-primary" style="font-size: 4rem;"></i>
            <h3 class="mt-3">QR Code Scanner</h3>
            <p class="lead text-muted">Scan QR codes to access check-in pages</p>
            
            <div class="mt-4">
                <button class="btn btn-primary btn-lg" onclick="startQRScanner()" id="scan-btn">
                    <i class="bi bi-camera"></i> Start Camera
                </button>
                <button class="btn btn-secondary btn-lg ms-2" onclick="stopQRScanner()" id="stop-btn" style="display: none;">
                    <i class="bi bi-stop-circle"></i> Stop
                </button>
            </div>

            <div class="mt-3" id="manual-input-btn" style="display: none;">
                <button class="btn btn-outline-primary" onclick="showManualInput()">
                    <i class="bi bi-keyboard"></i> Enter Code Manually
                </button>
            </div>

            <!-- Camera Preview -->
            <div id="camera-container" class="mt-4" style="display: none;">
                <div class="camera-preview">
                    <video id="camera-video" autoplay playsinline style="width: 100%; max-width: 400px; border-radius: 8px;"></video>
                    <canvas id="camera-canvas" style="display: none;"></canvas>
                    <div class="scanner-overlay">
                        <div class="scanner-frame"></div>
                        <p class="text-center mt-3 text-white" id="scanner-status">
                            <i class="bi bi-qr-code-scan"></i> Point camera at QR code
                        </p>
                        <div class="text-center mt-2">
                            <small class="text-white-50" id="scanner-hint">
                                Make sure the QR code is clearly visible and well-lit
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <h6><i class="bi bi-keyboard"></i> Manual Entry</h6>
                <p class="text-muted small">Or enter a session token manually:</p>
                <form method="GET" action="mobile_checkin.php" class="mt-2">
                    <div class="input-group">
                        <input type="text" class="form-control" name="token" placeholder="Enter session code" required>
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-right"></i> Go
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="text-center mt-3">
        <a href="mobile_checkin.php" class="btn btn-outline-light">
            <i class="bi bi-arrow-left"></i> Back to Mobile Check-in
        </a>
    </div>
</div>

<script>
// QR Scanner Variables
let stream = null;
let scanning = false;
let video = null;
let canvas = null;
let context = null;

function startQRScanner() {
    const cameraContainer = document.getElementById('camera-container');
    const scanBtn = document.getElementById('scan-btn');
    const stopBtn = document.getElementById('stop-btn');
    const manualBtn = document.getElementById('manual-input-btn');
    
    video = document.getElementById('camera-video');
    canvas = document.getElementById('camera-canvas');
    context = canvas.getContext('2d');
    
    // Show camera interface
    cameraContainer.style.display = 'block';
    scanBtn.style.display = 'none';
    stopBtn.style.display = 'inline-block';
    manualBtn.style.display = 'block';
    
    // Request camera access
    navigator.mediaDevices.getUserMedia({ 
        video: { 
            facingMode: 'environment', // Use back camera if available
            width: { ideal: 1280 },
            height: { ideal: 720 }
        } 
    })
    .then(function(mediaStream) {
        stream = mediaStream;
        video.srcObject = stream;
        video.play();
        
        // Start scanning when video is ready
        video.addEventListener('loadedmetadata', function() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            scanning = true;
            updateScannerStatus('Scanning for QR codes...', 'success');
            scanQRCode();
        });
    })
    .catch(function(error) {
        console.error('Camera access denied:', error);
        updateScannerStatus('Camera access denied', 'error');
        
        // Show user-friendly error message
        setTimeout(() => {
            alert('Camera access is required to scan QR codes. Please:\n\n1. Allow camera access when prompted\n2. Refresh the page and try again\n3. Or use "Enter Code Manually" option');
            stopQRScanner();
        }, 1000);
    });
}

function stopQRScanner() {
    scanning = false;
    
    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }
    
    const cameraContainer = document.getElementById('camera-container');
    const scanBtn = document.getElementById('scan-btn');
    const stopBtn = document.getElementById('stop-btn');
    const manualBtn = document.getElementById('manual-input-btn');
    
    cameraContainer.style.display = 'none';
    scanBtn.style.display = 'inline-block';
    stopBtn.style.display = 'none';
    manualBtn.style.display = 'none';
}

function scanQRCode() {
    if (!scanning || !video || video.readyState !== video.HAVE_ENOUGH_DATA) {
        if (scanning) {
            requestAnimationFrame(scanQRCode);
        }
        return;
    }
    
    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    
    // Detect QR code using jsQR library
    const qrResult = detectQRCode(imageData);
    
    if (qrResult) {
        console.log('QR Code detected:', qrResult);
        
        // Extract token from QR code data
        let token = '';
        
        try {
            if (qrResult.includes('token=')) {
                // Extract from URL
                const url = new URL(qrResult);
                token = url.searchParams.get('token');
            } else if (qrResult.includes('mobile_checkin.php')) {
                // Extract token from path
                const match = qrResult.match(/token=([^&]+)/);
                if (match) token = match[1];
            } else {
                // Assume the data is the token itself
                token = qrResult;
            }
            
            if (token) {
                updateScannerStatus('QR Code detected! Redirecting...', 'success');
                stopQRScanner();
                
                // Navigate to check-in page with token
                setTimeout(() => {
                    window.location.href = 'mobile_checkin.php?token=' + encodeURIComponent(token);
                }, 1000);
                return;
            }
        } catch (e) {
            console.error('Error parsing QR code:', e);
        }
        
        alert('Invalid QR code format. Please try again.');
    }
    
    // Continue scanning
    if (scanning) {
        requestAnimationFrame(scanQRCode);
    }
}

// QR code detection using jsQR library
function detectQRCode(imageData) {
    try {
        const code = jsQR(imageData.data, imageData.width, imageData.height, {
            inversionAttempts: "dontInvert",
        });
        
        if (code) {
            return code.data;
        }
        return null;
    } catch (error) {
        console.error('QR detection error:', error);
        return null;
    }
}

// Update scanner status display
function updateScannerStatus(message, type = 'info') {
    const statusElement = document.getElementById('scanner-status');
    const hintElement = document.getElementById('scanner-hint');
    
    if (statusElement) {
        let icon = 'bi-qr-code-scan';
        if (type === 'success') icon = 'bi-check-circle';
        if (type === 'error') icon = 'bi-exclamation-triangle';
        if (type === 'scanning') icon = 'bi-search';
        
        statusElement.innerHTML = `<i class="bi ${icon}"></i> ${message}`;
    }
    
    if (hintElement && type === 'error') {
        hintElement.textContent = 'Try adjusting lighting or distance to the QR code';
    }
}

// Alternative: Use manual token input when camera detection fails
function showManualInput() {
    const token = prompt('Enter the session token manually:');
    if (token && token.trim()) {
        window.location.href = 'mobile_checkin.php?token=' + encodeURIComponent(token.trim());
    }
}
</script>

</body>
</html>
