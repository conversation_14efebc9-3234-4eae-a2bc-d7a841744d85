<?php
session_start();

// Include the configuration file
require_once '../config.php';

$token = $_GET['token'] ?? '';
$event_id = $_GET['event_id'] ?? '';
$message = '';
$error = '';
$session_data = null;

// If token is provided, validate and get session data
if (!empty($token)) {
    try {
        // First try the new session-specific QR codes
        $stmt = $pdo->prepare("
            SELECT
                s.*,
                e.title as event_title,
                e.id as event_id,
                sqr.expires_at,
                sqr.attendee_name,
                sqr.attendee_email,
                sqr.attendee_type,
                sqr.member_id,
                sqr.guest_name,
                sqr.guest_email,
                sqr.is_used
            FROM session_attendance_qr_codes sqr
            JOIN event_sessions s ON sqr.session_id = s.id
            JOIN events e ON s.event_id = e.id
            WHERE sqr.qr_token = ? AND sqr.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $session_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session_data) {
            // Fallback to old session QR codes for backward compatibility
            $stmt = $pdo->prepare("
                SELECT s.*, e.title as event_title, e.id as event_id, qr.expires_at, qr.is_active
                FROM session_qr_codes qr
                JOIN event_sessions s ON qr.session_id = s.id
                JOIN events e ON s.event_id = e.id
                WHERE qr.qr_token = ? AND qr.is_active = 1 AND qr.expires_at > NOW()
            ");
            $stmt->execute([$token]);
            $session_data = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($session_data) {
                $session_data['qr_type'] = 'legacy';
                // Update scan count for legacy QR codes
                $stmt = $pdo->prepare("UPDATE session_qr_codes SET scan_count = scan_count + 1 WHERE qr_token = ?");
                $stmt->execute([$token]);
            }
        } else {
            $session_data['qr_type'] = 'session_specific';
        }

        if (!$session_data) {
            $error = "Invalid or expired QR code. Please contact event staff.";
        }
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

// Handle check-in submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'qr_checkin' && !empty($token)) {
            // Handle QR code check-in
            if (!$session_data) {
                throw new Exception("Invalid QR code session data.");
            }

            $session_id = $session_data['id'];
            $pdo->beginTransaction();

            if ($session_data['qr_type'] === 'session_specific') {
                // Session-specific QR code check-in
                $member_id = $session_data['member_id'];
                $guest_name = $session_data['guest_name'];
                $guest_email = $session_data['guest_email'];
                $attendee_type = $session_data['attendee_type'];

                if ($session_data['is_used']) {
                    throw new Exception("This QR code has already been used for check-in.");
                }

                // Mark QR code as used
                $stmt = $pdo->prepare("
                    UPDATE session_attendance_qr_codes
                    SET is_used = 1, used_at = NOW()
                    WHERE qr_token = ?
                ");
                $stmt->execute([$token]);

                // Update or create session attendance record
                if ($attendee_type === 'member' && $member_id) {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance
                        (session_id, member_id, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, 'attended', NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        attendance_status = 'attended', attendance_date = NOW()
                    ");
                    $stmt->execute([$session_id, $member_id]);
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance
                        (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, ?, 'attended', NOW(), NOW())
                        ON DUPLICATE KEY UPDATE
                        attendance_status = 'attended', attendance_date = NOW()
                    ");
                    $stmt->execute([$session_id, $guest_name, $guest_email]);
                }

                $message = "✅ {$session_data['attendee_name']} checked in successfully via QR code!";

            } else {
                // Legacy session QR code - show attendee selection
                $message = "QR code scanned successfully. Please select the attendee to check in.";
            }

            $pdo->commit();

        } elseif ($_POST['action'] === 'checkin_attendee') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_type = $_POST['attendee_type'] ?? '';
            $member_id = $_POST['member_id'] ?? '';
            $guest_name = $_POST['guest_name'] ?? '';
            $guest_email = $_POST['guest_email'] ?? '';
            
            if (empty($session_id)) {
                throw new Exception("Session ID is required.");
            }
            
            $pdo->beginTransaction();
            
            if ($attendee_type === 'member' && !empty($member_id)) {
                // Check if member attendance record exists
                $stmt = $pdo->prepare("
                    SELECT id FROM session_attendance 
                    WHERE session_id = ? AND member_id = ?
                ");
                $stmt->execute([$session_id, $member_id]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance 
                        (session_id, member_id, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, 'attended', NOW(), NOW())
                    ");
                    $stmt->execute([$session_id, $member_id]);
                }
                
                $message = "Member checked in successfully!";
                
            } elseif ($attendee_type === 'guest' && !empty($guest_name) && !empty($guest_email)) {
                // Check if guest attendance record exists
                $stmt = $pdo->prepare("
                    SELECT id FROM session_attendance 
                    WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                ");
                $stmt->execute([$session_id, $guest_name, $guest_email]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);
                } else {
                    // Create new record
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance 
                        (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                        VALUES (?, ?, ?, 'attended', NOW(), NOW())
                    ");
                    $stmt->execute([$session_id, $guest_name, $guest_email]);
                }
                
                $message = "Guest checked in successfully!";
                
            } else {
                throw new Exception("Please provide valid attendee information.");
            }
            
            $pdo->commit();
            
        } elseif ($_POST['action'] === 'bulk_checkin') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_ids = $_POST['attendee_ids'] ?? [];
            
            if (empty($session_id) || empty($attendee_ids)) {
                throw new Exception("Session and attendees are required.");
            }
            
            $pdo->beginTransaction();
            $checked_in_count = 0;
            
            foreach ($attendee_ids as $attendee_id) {
                if (strpos($attendee_id, 'member_') === 0) {
                    $member_id = substr($attendee_id, 7);
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$session_id, $member_id]);
                    $checked_in_count += $stmt->rowCount();
                } else {
                    // Handle guest format: guest_name_email
                    $parts = explode('_', $attendee_id, 3);
                    if (count($parts) >= 3) {
                        $guest_name = $parts[1];
                        $guest_email = $parts[2];
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = 'attended', attendance_date = NOW()
                            WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                        ");
                        $stmt->execute([$session_id, $guest_name, $guest_email]);
                        $checked_in_count += $stmt->rowCount();
                    }
                }
            }
            
            $pdo->commit();
            $message = "Successfully checked in {$checked_in_count} attendees!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get session attendees if we have session data
$attendees = [];
if ($session_data) {
    $stmt = $pdo->prepare("
        SELECT 
            sa.*,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.full_name
                ELSE sa.guest_name
            END as attendee_name,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.email
                ELSE sa.guest_email
            END as attendee_email,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN 'member'
                ELSE 'guest'
            END as attendee_type,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END as attendee_id
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE sa.session_id = ?
        ORDER BY sa.attendance_status ASC, attendee_name ASC
    ");
    $stmt->execute([$session_data['id']]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get all members for quick search
$all_members = [];
if ($session_data) {
    $stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE is_active = 1 ORDER BY full_name");
    $stmt->execute();
    $all_members = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Set page variables for header
$page_title = 'Mobile Check-in';
$page_header = 'Mobile Check-in';
$page_description = 'Mobile check-in interface for event sessions';

// Include the admin header
require_once 'includes/header.php';
?>

<script src="https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.js"></script>
    <style>
        /* Override admin body styles for mobile check-in */
        .main-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            min-height: calc(100vh - 120px);
            font-size: 1.1rem;
        }
        .mobile-container {
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
        .checkin-card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            border: none;
        }
        .session-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
        }
        .attendee-item {
            border: 2px solid #dee2e6;
            border-radius: 10px;
            margin: 8px 0;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .attendee-item:hover {
            border-color: #0d6efd;
            background-color: #f8f9ff;
        }
        .attendee-item.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .attendee-item.attended {
            border-color: #6c757d;
            background-color: #f8f9fa;
            opacity: 0.7;
        }
        .status-badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        /* Camera Scanner Styles */
        .camera-preview {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }

        .scanner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            pointer-events: none;
        }

        .scanner-frame {
            width: 200px;
            height: 200px;
            border: 3px solid #28a745;
            border-radius: 12px;
            position: relative;
            background: rgba(40, 167, 69, 0.1);
        }

        .scanner-frame::before,
        .scanner-frame::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #28a745;
        }

        .scanner-frame::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
        }

        .scanner-frame::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
        }

        #camera-video {
            background: #000;
        }

        /* Session QR Code Display Styles */
        .session-qr-card {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .session-qr-card:hover {
            border-color: #28a745;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
        }

        .qr-display {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .qr-code-image {
            border: 2px solid #28a745;
            border-radius: 8px;
            max-width: 180px;
            height: auto;
        }
        .quick-checkin-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            border-radius: 50px;
            padding: 15px 20px;
        }
        .search-box {
            font-size: 1.1rem;
            padding: 12px;
            border-radius: 10px;
        }
        @media (max-width: 576px) {
            .mobile-container {
                padding: 10px;
            }
            .attendee-item {
                padding: 12px;
            }
        }

        /* Print Styles */
        @media print {
            body {
                background: white !important;
                color: black !important;
                font-size: 12pt !important;
                line-height: 1.4 !important;
                margin: 0 !important;
                padding: 0 !important;
            }

            .mobile-container {
                max-width: none !important;
                margin: 0 !important;
                padding: 10pt !important;
            }

            .checkin-card {
                box-shadow: none !important;
                border: 1pt solid #000 !important;
                border-radius: 0 !important;
                margin: 0 !important;
                page-break-inside: avoid;
            }

            .session-header {
                background: #f0f0f0 !important;
                color: black !important;
                border: 1pt solid #000 !important;
                border-radius: 0 !important;
                padding: 10pt !important;
                margin-bottom: 10pt !important;
            }

            .attendee-item {
                border: 1pt solid #ccc !important;
                border-radius: 0 !important;
                margin: 5pt 0 !important;
                padding: 8pt !important;
                background: white !important;
                page-break-inside: avoid;
            }

            .attendee-item.attended {
                background: #f5f5f5 !important;
                opacity: 1 !important;
            }

            .attendee-item.selected {
                background: #e8f5e8 !important;
                border-color: #000 !important;
            }

            .btn {
                display: none !important;
            }

            .alert {
                border: 1pt solid #000 !important;
                border-radius: 0 !important;
                background: white !important;
                color: black !important;
                padding: 8pt !important;
                margin: 5pt 0 !important;
            }

            .status-badge {
                border: 1pt solid #000 !important;
                background: white !important;
                color: black !important;
                padding: 2pt 4pt !important;
            }

            /* Hide interactive elements */
            .camera-container,
            .manual-input,
            button,
            input[type="button"],
            input[type="submit"],
            .btn,
            .dropdown,
            .nav-tabs {
                display: none !important;
            }

            /* Show only essential content */
            .session-info,
            .attendee-list,
            .attendance-summary {
                display: block !important;
            }

            /* Page breaks */
            .page-break {
                page-break-before: always;
            }

            /* Print header */
            .print-header {
                display: block !important;
                text-align: center;
                margin-bottom: 20pt;
                border-bottom: 2pt solid #000;
                padding-bottom: 10pt;
            }

            .print-header h1 {
                font-size: 18pt;
                margin: 0;
            }

            .print-header p {
                font-size: 12pt;
                margin: 5pt 0 0 0;
            }
        }
    </style>

<!-- Print Header (only visible when printing) -->
<div class="print-header" style="display: none;">
    <h1>Mobile Check-in Report</h1>
    <p>Generated on: <?php echo date('F j, Y \a\t g:i A'); ?></p>
</div>

<!-- Print Button (floating action button) -->
<button type="button" class="btn btn-primary position-fixed"
        style="top: 100px; right: 20px; z-index: 1050; border-radius: 50px; padding: 12px 20px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);"
        onclick="window.print()"
        title="Print Check-in Report">
    <i class="bi bi-printer"></i> Print
</button>

<div class="mobile-container">
    <?php if (empty($session_data)): ?>
        <!-- No Session Data - Show Available Session QR Codes -->
        <div class="card checkin-card">
            <div class="card-body text-center py-4">
                <i class="bi bi-qr-code text-primary" style="font-size: 3rem;"></i>
                <h3 class="mt-3">Mobile Check-in</h3>
                <p class="lead text-muted">Scan a QR code below with your camera app</p>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger mt-3">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <?php
                // Get available sessions for today's events
                try {
                    $stmt = $pdo->prepare("
                        SELECT
                            s.id,
                            s.session_title,
                            s.start_datetime,
                            s.location,
                            e.title as event_title,
                            qr.qr_token
                        FROM event_sessions s
                        JOIN events e ON s.event_id = e.id
                        LEFT JOIN session_qr_codes qr ON s.id = qr.session_id AND qr.is_active = 1
                        WHERE s.start_datetime >= NOW() - INTERVAL 2 HOUR
                        AND s.start_datetime <= NOW() + INTERVAL 7 DAY
                        ORDER BY s.start_datetime ASC
                        LIMIT 5
                    ");
                    $stmt->execute();
                    $available_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
                } catch (Exception $e) {
                    $available_sessions = [];
                    error_log("Error fetching sessions: " . $e->getMessage());
                }
                ?>

                <?php if (!empty($available_sessions)): ?>
                    <div class="row mt-4">
                        <?php foreach ($available_sessions as $session): ?>
                            <div class="col-12 mb-4">
                                <div class="session-qr-card">
                                    <h5 class="mb-2">
                                        <i class="bi bi-calendar-event"></i>
                                        <?php echo htmlspecialchars($session['session_title']); ?>
                                    </h5>
                                    <p class="text-muted mb-3">
                                        <?php echo htmlspecialchars($session['event_title']); ?><br>
                                        <small>
                                            <i class="bi bi-clock"></i> <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                            <?php if ($session['location']): ?>
                                                • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                            <?php endif; ?>
                                        </small>
                                    </p>

                                    <?php if ($session['qr_token']): ?>
                                        <div class="qr-display">
                                            <?php
                                            $qr_url = admin_url_for("mobile_checkin.php?token=" . $session['qr_token']);
                                            ?>
                                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=<?php echo urlencode($qr_url); ?>"
                                                 alt="QR Code for <?php echo htmlspecialchars($session['session_title']); ?>"
                                                 class="qr-code-image">

                                            <div class="mt-3">
                                                <small class="text-muted">Scan with your camera app</small><br>
                                                <a href="<?php echo $qr_url; ?>" class="btn btn-sm btn-outline-primary mt-2">
                                                    <i class="bi bi-link"></i> Direct Link
                                                </a>
                                            </div>
                                        </div>
                                    <?php else: ?>
                                        <div class="alert alert-warning">
                                            <small><i class="bi bi-exclamation-triangle"></i> QR code not generated yet</small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info mt-4">
                        <i class="bi bi-info-circle"></i> No sessions available for check-in at this time.
                    </div>
                <?php endif; ?>

                <div class="mt-4">
                    <h6><i class="bi bi-keyboard"></i> Manual Entry</h6>
                    <p class="text-muted small">If you have a session code, enter it below:</p>
                    <form method="GET" class="mt-2">
                        <div class="input-group">
                            <input type="text" class="form-control" name="token" placeholder="Enter session code" required>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-arrow-right"></i> Go
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
    <?php else: ?>
        <!-- Session Check-in Interface -->
        <div class="card checkin-card">
            <!-- Session Header -->
            <div class="session-header">
                <h4 class="mb-1">
                    <i class="bi bi-calendar-event"></i> <?php echo htmlspecialchars($session_data['session_title']); ?>
                </h4>
                <p class="mb-0 text-white-50"><?php echo htmlspecialchars($session_data['event_title']); ?></p>
                <small class="text-white-50">
                    <?php echo date('M j, Y g:i A', strtotime($session_data['start_datetime'])); ?>
                    <?php if ($session_data['location']): ?>
                        • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session_data['location']); ?>
                    <?php endif; ?>
                </small>
            </div>
            
            <div class="card-body">
                <!-- Session-Specific QR Code Check-in -->
                <?php if (isset($session_data['qr_type']) && $session_data['qr_type'] === 'session_specific' && !$message): ?>
                    <div class="alert alert-info border-0 mb-4">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-qr-code-scan fs-3 me-3 text-primary"></i>
                            <div>
                                <h6 class="mb-1">Session-Specific QR Code Detected</h6>
                                <p class="mb-2">
                                    <strong><?php echo htmlspecialchars($session_data['attendee_name']); ?></strong>
                                    <br><small class="text-muted"><?php echo htmlspecialchars($session_data['attendee_email']); ?></small>
                                </p>
                                <?php if ($session_data['is_used']): ?>
                                    <div class="badge bg-warning">Already checked in</div>
                                <?php else: ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="qr_checkin">
                                        <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                                        <button type="submit" class="btn btn-success btn-sm">
                                            <i class="bi bi-check-circle"></i> Check In Now
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Success/Error Messages -->
                <?php if ($message): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <!-- Quick Stats -->
                <div class="row mb-3">
                    <div class="col-4 text-center">
                        <h5 class="text-primary mb-0"><?php echo count($attendees); ?></h5>
                        <small class="text-muted">Registered</small>
                    </div>
                    <div class="col-4 text-center">
                        <h5 class="text-success mb-0"><?php echo count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'attended'; })); ?></h5>
                        <small class="text-muted">Checked In</small>
                    </div>
                    <div class="col-4 text-center">
                        <h5 class="text-warning mb-0"><?php echo count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'registered'; })); ?></h5>
                        <small class="text-muted">Pending</small>
                    </div>
                </div>
                
                <!-- Search Box -->
                <div class="mb-3">
                    <input type="text" class="form-control search-box" id="attendee-search" 
                           placeholder="Search attendees..." onkeyup="filterAttendees()">
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-3">
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100" onclick="selectAllPending()">
                            <i class="bi bi-check-square"></i> Select Pending
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-secondary w-100" onclick="selectNone()">
                            <i class="bi bi-square"></i> Clear Selection
                        </button>
                    </div>
                </div>
                
                <!-- Attendee List -->
                <div class="attendee-list" style="max-height: 400px; overflow-y: auto;">
                    <?php if (empty($attendees)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                            <p>No attendees registered for this session.</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($attendees as $attendee): ?>
                            <div class="attendee-item <?php echo $attendee['attendance_status'] === 'attended' ? 'attended' : ''; ?>"
                                 data-attendee-id="<?php echo $attendee['attendee_id']; ?>"
                                 data-name="<?php echo strtolower($attendee['attendee_name']); ?>"
                                 onclick="toggleAttendeeSelection('<?php echo $attendee['attendee_id']; ?>')">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <input type="checkbox" class="form-check-input me-3 attendee-checkbox"
                                               value="<?php echo $attendee['attendee_id']; ?>"
                                               onclick="event.stopPropagation();"
                                               <?php echo $attendee['attendance_status'] === 'attended' ? 'disabled' : ''; ?>>
                                        <div>
                                            <strong><?php echo htmlspecialchars($attendee['attendee_name']); ?></strong>
                                            <?php if ($attendee['attendee_email']): ?>
                                                <br>
                                                <small class="text-muted">
                                                    <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($attendee['attendee_email']); ?>
                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge status-badge bg-<?php
                                            echo $attendee['attendance_status'] === 'attended' ? 'success' :
                                                ($attendee['attendance_status'] === 'no_show' ? 'danger' : 'secondary');
                                        ?>">
                                            <?php echo ucfirst($attendee['attendance_status']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            <span class="badge bg-<?php echo $attendee['attendee_type'] === 'member' ? 'primary' : 'info'; ?>">
                                                <?php echo ucfirst($attendee['attendee_type']); ?>
                                            </span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Manual Check-in Form -->
                <div class="mt-4">
                    <h6><i class="bi bi-person-plus"></i> Manual Check-in</h6>
                    <form method="POST">
                        <input type="hidden" name="action" value="checkin_attendee">
                        <input type="hidden" name="session_id" value="<?php echo $session_data['id']; ?>">
                        
                        <div class="mb-3">
                            <select class="form-select" name="attendee_type" id="attendee-type" onchange="toggleAttendeeFields()">
                                <option value="">Select Attendee Type</option>
                                <option value="member">Church Member</option>
                                <option value="guest">Guest</option>
                            </select>
                        </div>
                        
                        <div id="member-fields" style="display: none;">
                            <div class="mb-3">
                                <select class="form-select" name="member_id" id="member-select">
                                    <option value="">Select Member</option>
                                    <?php foreach ($all_members as $member): ?>
                                        <option value="<?php echo $member['id']; ?>">
                                            <?php echo htmlspecialchars($member['full_name']); ?> 
                                            (<?php echo htmlspecialchars($member['email']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div id="guest-fields" style="display: none;">
                            <div class="mb-3">
                                <input type="text" class="form-control" name="guest_name" placeholder="Guest Name">
                            </div>
                            <div class="mb-3">
                                <input type="email" class="form-control" name="guest_email" placeholder="Guest Email">
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-success w-100">
                            <i class="bi bi-check-circle"></i> Check In
                        </button>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Quick Check-in Button -->
<?php if ($session_data && !empty($attendees)): ?>
    <button class="btn btn-success quick-checkin-btn" onclick="bulkCheckin()" id="quick-checkin-btn" style="display: none;">
        <i class="bi bi-check-all"></i> Check In Selected (<span id="selected-count">0</span>)
    </button>
<?php endif; ?>

<!-- Staff QR Scanner Button -->
<a href="staff_qr_scanner.php" class="btn btn-info" style="position: fixed; bottom: 20px; left: 20px; z-index: 1000; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border-radius: 50px; padding: 15px 20px;">
    <i class="bi bi-camera-fill"></i> Staff Scanner
</a>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="js/realtime-sync.js"></script>
<script>
// Initialize real-time sync for this session
<?php if ($session_data): ?>
const realtimeSync = window.RealtimeSyncManager.create(<?php echo $session_data['id']; ?>, {
    onUpdate: function(updates) {
        console.log('Received updates:', updates);
        // Updates are automatically processed by the sync system
    },
    onConflict: function(conflictData) {
        showConflictAlert(conflictData);
    },
    onStatsUpdate: function(stats) {
        updateSessionStats(stats);
    },
    onError: function(error) {
        console.error('Sync error:', error);
    }
});

function showConflictAlert(conflictData) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-warning alert-dismissible fade show';
    alert.innerHTML = `
        <i class="bi bi-exclamation-triangle"></i>
        <strong>Scheduling Conflict Detected!</strong><br>
        This member has conflicts with ${conflictData.conflicts.length} other session(s).
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.mobile-container').insertBefore(alert, document.querySelector('.card'));
}

function updateSessionStats(stats) {
    // Update the quick stats display
    const statsElements = document.querySelectorAll('.col-4 h5');
    if (statsElements.length >= 3) {
        statsElements[0].textContent = stats.total_registered || 0;
        statsElements[1].textContent = stats.total_attended || 0;
        statsElements[2].textContent = stats.total_pending || 0;
    }
}
<?php endif; ?>

// Mobile check-in functionality
function toggleAttendeeFields() {
    const attendeeType = document.getElementById('attendee-type').value;
    const memberFields = document.getElementById('member-fields');
    const guestFields = document.getElementById('guest-fields');
    
    if (attendeeType === 'member') {
        memberFields.style.display = 'block';
        guestFields.style.display = 'none';
    } else if (attendeeType === 'guest') {
        memberFields.style.display = 'none';
        guestFields.style.display = 'block';
    } else {
        memberFields.style.display = 'none';
        guestFields.style.display = 'none';
    }
}

function toggleAttendeeSelection(attendeeId) {
    const item = document.querySelector(`[data-attendee-id="${attendeeId}"]`);
    const checkbox = item.querySelector('.attendee-checkbox');
    
    if (!checkbox.disabled) {
        checkbox.checked = !checkbox.checked;
        
        if (checkbox.checked) {
            item.classList.add('selected');
        } else {
            item.classList.remove('selected');
        }
        
        updateSelectionCount();
    }
}

function selectAllPending() {
    document.querySelectorAll('.attendee-item:not(.attended)').forEach(item => {
        const checkbox = item.querySelector('.attendee-checkbox');
        if (!checkbox.disabled) {
            checkbox.checked = true;
            item.classList.add('selected');
        }
    });
    updateSelectionCount();
}

function selectNone() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.closest('.attendee-item').classList.remove('selected');
    });
    updateSelectionCount();
}

function updateSelectionCount() {
    const selectedCount = document.querySelectorAll('.attendee-checkbox:checked').length;
    const quickBtn = document.getElementById('quick-checkin-btn');
    const countSpan = document.getElementById('selected-count');

    if (quickBtn && countSpan) {
        if (selectedCount > 0) {
            quickBtn.style.display = 'block';
            countSpan.textContent = selectedCount;
        } else {
            quickBtn.style.display = 'none';
        }
    }
}

function filterAttendees() {
    const searchTerm = document.getElementById('attendee-search').value.toLowerCase();
    
    document.querySelectorAll('.attendee-item').forEach(item => {
        const name = item.getAttribute('data-name');
        if (name.includes(searchTerm)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
    });
}

function bulkCheckin() {
    const selectedCheckboxes = document.querySelectorAll('.attendee-checkbox:checked');
    const attendeeIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    
    if (attendeeIds.length === 0) {
        alert('Please select attendees to check in.');
        return;
    }
    
    if (!confirm(`Check in ${attendeeIds.length} selected attendees?`)) {
        return;
    }
    
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';
    
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'bulk_checkin';
    form.appendChild(actionInput);
    
    const sessionInput = document.createElement('input');
    sessionInput.type = 'hidden';
    sessionInput.name = 'session_id';
    sessionInput.value = '<?php echo $session_data['id'] ?? ''; ?>';
    form.appendChild(sessionInput);
    
    attendeeIds.forEach(id => {
        const attendeeInput = document.createElement('input');
        attendeeInput.type = 'hidden';
        attendeeInput.name = 'attendee_ids[]';
        attendeeInput.value = id;
        form.appendChild(attendeeInput);
    });
    
    document.body.appendChild(form);
    form.submit();
}

// QR Scanner Variables
let stream = null;
let scanning = false;
let video = null;
let canvas = null;
let context = null;

function startQRScanner() {
    const cameraContainer = document.getElementById('camera-container');
    const scanBtn = document.getElementById('scan-btn');
    const stopBtn = document.getElementById('stop-btn');
    const manualBtn = document.getElementById('manual-input-btn');

    video = document.getElementById('camera-video');
    canvas = document.getElementById('camera-canvas');
    context = canvas.getContext('2d');

    // Show camera interface
    cameraContainer.style.display = 'block';
    scanBtn.style.display = 'none';
    stopBtn.style.display = 'inline-block';
    manualBtn.style.display = 'block';

    // Request camera access
    navigator.mediaDevices.getUserMedia({
        video: {
            facingMode: 'environment', // Use back camera if available
            width: { ideal: 1280 },
            height: { ideal: 720 }
        }
    })
    .then(function(mediaStream) {
        stream = mediaStream;
        video.srcObject = stream;
        video.play();

        // Start scanning when video is ready
        video.addEventListener('loadedmetadata', function() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            scanning = true;
            updateScannerStatus('Scanning for QR codes...', 'success');
            scanQRCode();
        });
    })
    .catch(function(error) {
        console.error('Camera access denied:', error);
        updateScannerStatus('Camera access denied', 'error');

        // Show user-friendly error message
        setTimeout(() => {
            alert('Camera access is required to scan QR codes. Please:\n\n1. Allow camera access when prompted\n2. Refresh the page and try again\n3. Or use "Enter Code Manually" option');
            stopQRScanner();
        }, 1000);
    });
}

function stopQRScanner() {
    scanning = false;

    if (stream) {
        stream.getTracks().forEach(track => track.stop());
        stream = null;
    }

    const cameraContainer = document.getElementById('camera-container');
    const scanBtn = document.getElementById('scan-btn');
    const stopBtn = document.getElementById('stop-btn');
    const manualBtn = document.getElementById('manual-input-btn');

    cameraContainer.style.display = 'none';
    scanBtn.style.display = 'inline-block';
    stopBtn.style.display = 'none';
    manualBtn.style.display = 'none';
}

function scanQRCode() {
    if (!scanning || !video || video.readyState !== video.HAVE_ENOUGH_DATA) {
        if (scanning) {
            requestAnimationFrame(scanQRCode);
        }
        return;
    }

    // Draw video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);

    // Simple QR detection using pattern recognition
    const qrResult = detectQRCode(imageData);

    if (qrResult) {
        console.log('QR Code detected:', qrResult);

        // Extract token from QR code data
        let token = '';

        try {
            if (qrResult.includes('token=')) {
                // Extract from URL
                const url = new URL(qrResult);
                token = url.searchParams.get('token');
            } else if (qrResult.includes('mobile_checkin.php')) {
                // Extract token from path
                const match = qrResult.match(/token=([^&]+)/);
                if (match) token = match[1];
            } else {
                // Assume the data is the token itself
                token = qrResult;
            }

            if (token) {
                updateScannerStatus('QR Code detected! Redirecting...', 'success');
                stopQRScanner();

                // Navigate to check-in page with token
                setTimeout(() => {
                    window.location.href = 'mobile_checkin.php?token=' + encodeURIComponent(token);
                }, 1000);
                return;
            }
        } catch (e) {
            console.error('Error parsing QR code:', e);
        }

        alert('Invalid QR code format. Please try again.');
    }

    // Continue scanning
    if (scanning) {
        requestAnimationFrame(scanQRCode);
    }
}

// QR code detection using jsQR library
function detectQRCode(imageData) {
    try {
        const code = jsQR(imageData.data, imageData.width, imageData.height, {
            inversionAttempts: "dontInvert",
        });

        if (code) {
            return code.data;
        }
        return null;
    } catch (error) {
        console.error('QR detection error:', error);
        return null;
    }
}

// Update scanner status display
function updateScannerStatus(message, type = 'info') {
    const statusElement = document.getElementById('scanner-status');
    const hintElement = document.getElementById('scanner-hint');

    if (statusElement) {
        let icon = 'bi-qr-code-scan';
        if (type === 'success') icon = 'bi-check-circle';
        if (type === 'error') icon = 'bi-exclamation-triangle';
        if (type === 'scanning') icon = 'bi-search';

        statusElement.innerHTML = `<i class="bi ${icon}"></i> ${message}`;
    }

    if (hintElement && type === 'error') {
        hintElement.textContent = 'Try adjusting lighting or distance to the QR code';
    }
}

// Alternative: Use manual token input when camera detection fails
function showManualInput() {
    const token = prompt('Enter the session token manually:');
    if (token && token.trim()) {
        window.location.href = 'mobile_checkin.php?token=' + encodeURIComponent(token.trim());
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateSelectionCount();
    
    // Add touch-friendly interactions
    document.querySelectorAll('.attendee-item').forEach(item => {
        item.style.minHeight = '70px';
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
