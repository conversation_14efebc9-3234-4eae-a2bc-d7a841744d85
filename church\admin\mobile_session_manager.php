<?php
require_once '../config.php';
require_once 'includes/auth_check.php';

$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;
$session_id = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;

if (!$event_id) {
    header('Location: events.php');
    exit();
}

// Get event details
$stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
$stmt->execute([$event_id]);
$event = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header('Location: events.php');
    exit();
}

// Handle AJAX requests for mobile operations
if (isset($_POST['ajax'])) {
    header('Content-Type: application/json');
    
    try {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'quick_checkin') {
            $attendee_id = $_POST['attendee_id'] ?? '';
            $session_id = (int)($_POST['session_id'] ?? 0);
            
            if (!$attendee_id || !$session_id) {
                throw new Exception('Missing required parameters');
            }
            
            // Check if attendance record exists
            $stmt = $pdo->prepare("
                SELECT id FROM session_attendance 
                WHERE session_id = ? AND 
                (member_id = ? OR CONCAT('guest_', id) = ?)
            ");
            $stmt->execute([$session_id, $attendee_id, $attendee_id]);
            $existing = $stmt->fetch();
            
            if ($existing) {
                // Update existing record
                $stmt = $pdo->prepare("
                    UPDATE session_attendance 
                    SET attendance_status = 'attended', attendance_date = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$existing['id']]);
            } else {
                // Create new record
                if (is_numeric($attendee_id)) {
                    $stmt = $pdo->prepare("
                        INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                        VALUES (?, ?, 'attended', NOW())
                    ");
                    $stmt->execute([$session_id, $attendee_id]);
                }
            }
            
            echo json_encode(['success' => true, 'message' => 'Checked in successfully']);
            
        } elseif ($action === 'bulk_checkin') {
            $attendee_ids = $_POST['attendee_ids'] ?? [];
            $session_id = (int)($_POST['session_id'] ?? 0);
            
            if (empty($attendee_ids) || !$session_id) {
                throw new Exception('Missing required parameters');
            }
            
            $pdo->beginTransaction();
            $checked_in_count = 0;
            
            foreach ($attendee_ids as $attendee_id) {
                // Check if attendance record exists
                $stmt = $pdo->prepare("
                    SELECT id FROM session_attendance 
                    WHERE session_id = ? AND 
                    (member_id = ? OR CONCAT('guest_', id) = ?)
                ");
                $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                $existing = $stmt->fetch();
                
                if ($existing) {
                    // Update existing record
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = 'attended', attendance_date = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([$existing['id']]);
                    $checked_in_count++;
                } else {
                    // Create new record
                    if (is_numeric($attendee_id)) {
                        $stmt = $pdo->prepare("
                            INSERT INTO session_attendance (session_id, member_id, attendance_status, attendance_date)
                            VALUES (?, ?, 'attended', NOW())
                        ");
                        $stmt->execute([$session_id, $attendee_id]);
                        $checked_in_count++;
                    }
                }
            }
            
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => "Checked in {$checked_in_count} attendees"]);
            
        } elseif ($action === 'get_session_stats') {
            $session_id = (int)($_POST['session_id'] ?? 0);
            
            $stmt = $pdo->prepare("
                SELECT 
                    COUNT(sa.id) as total_registered,
                    COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
                    COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as total_no_show,
                    ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                           NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate
                FROM session_attendance sa
                WHERE sa.session_id = ?
            ");
            $stmt->execute([$session_id]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode(['success' => true, 'stats' => $stats]);
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit();
}

// Get sessions for this event
$stmt = $pdo->prepare("
    SELECT es.*, 
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / 
                  NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
           CASE 
               WHEN es.start_datetime > NOW() THEN 'upcoming'
               WHEN es.start_datetime <= NOW() AND es.end_datetime >= NOW() THEN 'active'
               ELSE 'completed'
           END as session_status
    FROM event_sessions es
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.event_id = ? AND es.status = 'active'
    GROUP BY es.id
    ORDER BY es.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// If specific session is selected, get attendees
$attendees = [];
if ($session_id) {
    $stmt = $pdo->prepare("
        SELECT 
            COALESCE(sa.member_id, CONCAT('guest_', sa.id)) as id,
            COALESCE(m.full_name, sa.guest_name) as name,
            CASE WHEN sa.member_id IS NOT NULL THEN 'member' ELSE 'guest' END as type,
            sa.attendance_status,
            sa.attendance_date
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE sa.session_id = ?
        ORDER BY name
    ");
    $stmt->execute([$session_id]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Page title and header info
$page_title = 'Mobile Session Manager';
$page_header = 'Mobile Session Manager';
$page_description = 'Mobile-optimized session management for ' . htmlspecialchars($event['title']);

// Include header
include 'includes/header.php';
?>

<!-- Additional mobile-specific styles -->
<style>
        /* Mobile-optimized session manager styles */
        .mobile-session-container {
            padding-bottom: 80px; /* Space for bottom navigation */
        }
        
        .session-card {
            background: white;
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid;
            transition: all 0.2s;
        }
        
        .session-card.active {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }
        
        .session-card.upcoming {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        }
        
        .session-card.completed {
            border-left-color: #6c757d;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }
        
        .attendee-item {
            background: white;
            border-radius: 10px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 1px 5px rgba(0,0,0,0.1);
            transition: all 0.2s;
        }
        
        .attendee-item.attended {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-left: 4px solid #28a745;
        }
        
        .attendee-item.no_show {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-left: 4px solid #dc3545;
        }
        
        .quick-action-btn {
            border-radius: 50px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            margin: 0.25rem;
            min-width: 120px;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            background: white;
            border-top: 1px solid #dee2e6;
            padding: 0.75rem;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
            /* Responsive positioning to avoid sidebar overlap */
            left: 0;
            right: 0;
        }

        .bottom-nav .btn {
            font-size: 0.8rem;
            padding: 0.5rem 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bottom-nav .btn small {
            font-size: 0.7rem;
            line-height: 1;
            display: block;
        }
        
        .stats-bar {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .progress-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            margin: 0 auto;
        }
        
        .search-box {
            position: sticky;
            top: 0;
            background: #f8f9fa;
            padding: 1rem;
            z-index: 100;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        @media (max-width: 576px) {
            .container-fluid {
                padding: 0.5rem;
            }

            .session-card, .attendee-item {
                margin-left: 0.5rem;
                margin-right: 0.5rem;
            }
        }

        /* Handle sidebar overlap on larger screens */
        @media (min-width: 992px) {
            .mobile-session-container {
                margin-left: 0; /* Let the standard layout handle sidebar spacing */
            }

            /* Adjust bottom nav to account for sidebar */
            .bottom-nav {
                left: 250px; /* Standard sidebar width */
            }

            /* If sidebar is collapsed, use full width */
            .sidebar-collapsed .bottom-nav {
                left: 0;
            }
        }

        /* On medium screens, still account for sidebar */
        @media (min-width: 768px) and (max-width: 991px) {
            .bottom-nav {
                left: 0; /* Sidebar typically collapses on medium screens */
            }
        }

        /* Mobile screens - full width bottom nav */
        @media (max-width: 767px) {
            .bottom-nav {
                left: 0;
                right: 0;
            }

            .bottom-nav .btn {
                font-size: 0.75rem;
                padding: 0.4rem 0.2rem;
            }

            .bottom-nav .btn small {
                font-size: 0.65rem;
            }
        }
    </style>

<!-- Mobile Session Manager Content -->

<div class="container-fluid">
    <?php if (!$session_id): ?>
        <!-- Session Selection View -->
        <div class="search-box">
            <input type="text" class="form-control" id="sessionSearch" placeholder="🔍 Search sessions...">
        </div>

        <div id="sessionsList">
            <?php foreach ($sessions as $session): ?>
                <div class="session-card <?php echo $session['session_status']; ?>" onclick="selectSession(<?php echo $session['id']; ?>)">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h6 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                            <small class="text-muted">
                                <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                <?php if ($session['location']): ?>
                                    • <?php echo htmlspecialchars($session['location']); ?>
                                <?php endif; ?>
                            </small>
                            <div class="mt-1">
                                <span class="badge bg-<?php echo $session['session_status'] === 'active' ? 'success' : ($session['session_status'] === 'upcoming' ? 'warning' : 'secondary'); ?>">
                                    <?php echo strtoupper($session['session_status']); ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-4 text-center">
                            <div class="progress-circle" style="background: <?php echo $session['attendance_rate'] >= 80 ? '#28a745' : ($session['attendance_rate'] >= 60 ? '#ffc107' : '#dc3545'); ?>">
                                <?php echo $session['attendance_rate']; ?>%
                            </div>
                            <small class="text-muted">
                                <?php echo $session['attended_count']; ?>/<?php echo $session['registered_count']; ?>
                            </small>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

    <?php else: ?>
        <!-- Session Management View -->
        <?php
        $current_session = array_filter($sessions, function($s) use ($session_id) {
            return $s['id'] == $session_id;
        });
        $current_session = reset($current_session);
        ?>

        <!-- Session Stats Bar -->
        <div class="stats-bar">
            <div class="row text-center">
                <div class="col-3">
                    <h4 class="text-primary" id="registeredCount"><?php echo $current_session['registered_count']; ?></h4>
                    <small class="text-muted">Registered</small>
                </div>
                <div class="col-3">
                    <h4 class="text-success" id="attendedCount"><?php echo $current_session['attended_count']; ?></h4>
                    <small class="text-muted">Attended</small>
                </div>
                <div class="col-3">
                    <h4 class="text-info" id="attendanceRate"><?php echo $current_session['attendance_rate']; ?>%</h4>
                    <small class="text-muted">Rate</small>
                </div>
                <div class="col-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="text-center mb-3">
            <button class="btn btn-success quick-action-btn" onclick="bulkCheckIn()">
                <i class="bi bi-check2-all"></i> Bulk Check-in
            </button>
            <button class="btn btn-warning quick-action-btn" onclick="selectAll()">
                <i class="bi bi-check-square"></i> Select All
            </button>
            <button class="btn btn-secondary quick-action-btn" onclick="clearSelection()">
                <i class="bi bi-square"></i> Clear
            </button>
        </div>

        <!-- Search Box -->
        <div class="search-box">
            <input type="text" class="form-control" id="attendeeSearch" placeholder="🔍 Search attendees...">
        </div>

        <!-- Attendees List -->
        <div id="attendeesList">
            <?php foreach ($attendees as $attendee): ?>
                <div class="attendee-item <?php echo $attendee['attendance_status']; ?>"
                     data-name="<?php echo strtolower($attendee['name']); ?>"
                     data-attendee-id="<?php echo $attendee['id']; ?>">
                    <div class="row align-items-center">
                        <div class="col-1">
                            <input type="checkbox" class="form-check-input attendee-checkbox"
                                   value="<?php echo $attendee['id']; ?>">
                        </div>
                        <div class="col-8">
                            <strong><?php echo htmlspecialchars($attendee['name']); ?></strong>
                            <br>
                            <small class="text-muted">
                                <span class="badge bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                    <?php echo ucfirst($attendee['type']); ?>
                                </span>
                                <?php if ($attendee['attendance_date']): ?>
                                    • Checked in: <?php echo date('g:i A', strtotime($attendee['attendance_date'])); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                        <div class="col-3 text-end">
                            <button class="btn btn-<?php echo $attendee['attendance_status'] === 'attended' ? 'success' : 'outline-success'; ?> btn-sm"
                                    onclick="quickCheckIn('<?php echo $attendee['id']; ?>', this)">
                                <i class="bi bi-<?php echo $attendee['attendance_status'] === 'attended' ? 'check-circle-fill' : 'check-circle'; ?>"></i>
                            </button>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Loading Spinner -->
        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Processing...</p>
        </div>

    <?php endif; ?>
</div>

<!-- Bottom Navigation -->
<div class="bottom-nav">
    <div class="row text-center">
        <?php if ($session_id): ?>
            <div class="col-4">
                <button class="btn btn-outline-secondary w-100" onclick="goBack()">
                    <i class="bi bi-arrow-left"></i><br>
                    <small>Back</small>
                </button>
            </div>
            <div class="col-4">
                <button class="btn btn-primary w-100" onclick="bulkCheckIn()" id="bulkCheckInBtn" disabled>
                    <i class="bi bi-check2-all"></i><br>
                    <small>Check-in (<span id="selectedCount">0</span>)</small>
                </button>
            </div>
            <div class="col-4">
                <button class="btn btn-outline-info w-100" onclick="refreshStats()">
                    <i class="bi bi-arrow-clockwise"></i><br>
                    <small>Refresh</small>
                </button>
            </div>
        <?php else: ?>
            <div class="col-6">
                <button class="btn btn-outline-secondary w-100" onclick="window.location.href='multi_session_dashboard.php?event_id=<?php echo $event_id; ?>'">
                    <i class="bi bi-arrow-left"></i><br>
                    <small>Dashboard</small>
                </button>
            </div>
            <div class="col-6">
                <button class="btn btn-primary w-100" onclick="refreshSessions()">
                    <i class="bi bi-arrow-clockwise"></i><br>
                    <small>Refresh</small>
                </button>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
const eventId = <?php echo $event_id; ?>;
const sessionId = <?php echo $session_id ?: 'null'; ?>;
let selectedAttendees = new Set();

// Session selection
function selectSession(id) {
    window.location.href = `?event_id=${eventId}&session_id=${id}`;
}

// Quick check-in for individual attendee
async function quickCheckIn(attendeeId, button) {
    try {
        showLoading(true);

        const response = await fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `ajax=1&action=quick_checkin&attendee_id=${attendeeId}&session_id=${sessionId}`
        });

        const data = await response.json();

        if (data.success) {
            // Update button appearance
            button.className = 'btn btn-success btn-sm';
            button.innerHTML = '<i class="bi bi-check-circle-fill"></i>';

            // Update attendee item appearance
            const attendeeItem = button.closest('.attendee-item');
            attendeeItem.classList.remove('no_show', 'registered');
            attendeeItem.classList.add('attended');

            // Refresh stats
            refreshStats();

            showToast('✅ Checked in successfully!', 'success');
        } else {
            showToast('❌ Check-in failed: ' + data.error, 'error');
        }
    } catch (error) {
        showToast('❌ Network error occurred', 'error');
    } finally {
        showLoading(false);
    }
}

// Bulk check-in
async function bulkCheckIn() {
    const selected = Array.from(selectedAttendees);

    if (selected.length === 0) {
        showToast('⚠️ Please select attendees first', 'warning');
        return;
    }

    if (!confirm(`Check in ${selected.length} attendees?`)) {
        return;
    }

    try {
        showLoading(true);

        const response = await fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `ajax=1&action=bulk_checkin&session_id=${sessionId}&attendee_ids=${JSON.stringify(selected)}`
        });

        const data = await response.json();

        if (data.success) {
            // Update UI for checked-in attendees
            selected.forEach(attendeeId => {
                const attendeeItem = document.querySelector(`[data-attendee-id="${attendeeId}"]`);
                if (attendeeItem) {
                    attendeeItem.classList.remove('no_show', 'registered');
                    attendeeItem.classList.add('attended');

                    const button = attendeeItem.querySelector('button');
                    button.className = 'btn btn-success btn-sm';
                    button.innerHTML = '<i class="bi bi-check-circle-fill"></i>';

                    const checkbox = attendeeItem.querySelector('.attendee-checkbox');
                    checkbox.checked = false;
                }
            });

            // Clear selection
            clearSelection();

            // Refresh stats
            refreshStats();

            showToast(`✅ ${data.message}`, 'success');
        } else {
            showToast('❌ Bulk check-in failed: ' + data.error, 'error');
        }
    } catch (error) {
        showToast('❌ Network error occurred', 'error');
    } finally {
        showLoading(false);
    }
}

// Selection management
function selectAll() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = true;
        selectedAttendees.add(checkbox.value);
    });
    updateSelectionUI();
}

function clearSelection() {
    document.querySelectorAll('.attendee-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    selectedAttendees.clear();
    updateSelectionUI();
}

function updateSelectionUI() {
    const count = selectedAttendees.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('bulkCheckInBtn').disabled = count === 0;
}

// Refresh stats
async function refreshStats() {
    if (!sessionId) return;

    try {
        const response = await fetch('', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `ajax=1&action=get_session_stats&session_id=${sessionId}`
        });

        const data = await response.json();

        if (data.success) {
            document.getElementById('registeredCount').textContent = data.stats.total_registered;
            document.getElementById('attendedCount').textContent = data.stats.total_attended;
            document.getElementById('attendanceRate').textContent = data.stats.attendance_rate + '%';
        }
    } catch (error) {
        console.error('Failed to refresh stats:', error);
    }
}

// Search functionality
document.addEventListener('DOMContentLoaded', function() {
    // Session search
    const sessionSearch = document.getElementById('sessionSearch');
    if (sessionSearch) {
        sessionSearch.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            document.querySelectorAll('.session-card').forEach(card => {
                const title = card.querySelector('h6').textContent.toLowerCase();
                if (title.includes(searchTerm)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }

    // Attendee search
    const attendeeSearch = document.getElementById('attendeeSearch');
    if (attendeeSearch) {
        attendeeSearch.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            document.querySelectorAll('.attendee-item').forEach(item => {
                const name = item.dataset.name;
                if (name.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Checkbox event listeners
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('attendee-checkbox')) {
            const attendeeId = e.target.value;
            if (e.target.checked) {
                selectedAttendees.add(attendeeId);
            } else {
                selectedAttendees.delete(attendeeId);
            }
            updateSelectionUI();
        }
    });
});

// Utility functions
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        spinner.style.display = show ? 'block' : 'none';
    }
}

function showToast(message, type) {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : (type === 'error' ? 'danger' : 'warning')} position-fixed`;
    toast.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 2000; min-width: 300px; text-align: center;';
    toast.textContent = message;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}

function goBack() {
    window.location.href = `?event_id=${eventId}`;
}

function refreshSessions() {
    window.location.reload();
}

// PWA functionality
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js').then(function(registration) {
            console.log('ServiceWorker registration successful');
        }, function(err) {
            console.log('ServiceWorker registration failed: ', err);
        });
    });
}

// Prevent zoom on double tap
let lastTouchEnd = 0;
document.addEventListener('touchend', function (event) {
    const now = (new Date()).getTime();
    if (now - lastTouchEnd <= 300) {
        event.preventDefault();
    }
    lastTouchEnd = now;
}, false);

// Auto-refresh stats every 30 seconds
if (sessionId) {
    setInterval(refreshStats, 30000);
}

// Adjust bottom navigation for sidebar overlap
function adjustBottomNav() {
    const bottomNav = document.querySelector('.bottom-nav');
    const sidebar = document.querySelector('.sidebar');

    if (bottomNav && sidebar) {
        const sidebarWidth = sidebar.offsetWidth;
        const isVisible = window.getComputedStyle(sidebar).display !== 'none';
        const isMobile = window.innerWidth < 992;

        if (isVisible && !isMobile && sidebarWidth > 0) {
            bottomNav.style.left = sidebarWidth + 'px';
        } else {
            bottomNav.style.left = '0px';
        }
    }
}

// Adjust on load and resize
document.addEventListener('DOMContentLoaded', adjustBottomNav);
window.addEventListener('resize', adjustBottomNav);

// Watch for sidebar toggle changes
const sidebarToggle = document.querySelector('[data-bs-toggle="collapse"][data-bs-target="#sidebar"]');
if (sidebarToggle) {
    sidebarToggle.addEventListener('click', function() {
        setTimeout(adjustBottomNav, 300); // Wait for animation
    });
}
</script>

</div> <!-- End container-fluid -->

<?php include 'includes/footer.php'; ?>
