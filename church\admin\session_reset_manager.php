<?php
/**
 * Session Reset Manager - Reset/Delete Sessions & Locations and Device Management
 */

require_once '../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error = '';

// Handle reset/delete actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        switch ($_POST['action']) {
            case 'reset_all_assignments':
                // Reset all session location assignments
                $pdo->exec("UPDATE session_locations SET assigned_staff = NULL, status = 'setup'");
                $pdo->exec("UPDATE session_devices SET assigned_session = NULL, assigned_staff = NULL, status = 'available'");
                $message = "All assignments have been reset successfully!";
                break;
                
            case 'delete_all_locations':
                // Delete all session locations
                $pdo->exec("DELETE FROM session_locations");
                $message = "All session locations have been deleted successfully!";
                break;
                
            case 'delete_all_devices':
                // Delete all devices
                $pdo->exec("DELETE FROM session_devices");
                $message = "All devices have been deleted successfully!";
                break;
                
            case 'reset_specific_session':
                $session_id = $_POST['session_id'] ?? '';
                if ($session_id) {
                    $stmt = $pdo->prepare("UPDATE session_locations SET assigned_staff = NULL, status = 'setup' WHERE session_id = ?");
                    $stmt->execute([$session_id]);
                    $stmt = $pdo->prepare("UPDATE session_devices SET assigned_session = NULL, assigned_staff = NULL, status = 'available' WHERE assigned_session = ?");
                    $stmt->execute([$session_id]);
                    $message = "Session assignments have been reset successfully!";
                } else {
                    $error = "Please select a session to reset.";
                }
                break;
                
            case 'delete_specific_location':
                $location_id = $_POST['location_id'] ?? '';
                if ($location_id) {
                    $stmt = $pdo->prepare("DELETE FROM session_locations WHERE id = ?");
                    $stmt->execute([$location_id]);
                    $message = "Location has been deleted successfully!";
                } else {
                    $error = "Please select a location to delete.";
                }
                break;
                
            case 'reassign_location':
                $location_id = $_POST['location_id'] ?? '';
                $new_staff = $_POST['new_staff'] ?? [];
                if ($location_id) {
                    $stmt = $pdo->prepare("UPDATE session_locations SET assigned_staff = ? WHERE id = ?");
                    $stmt->execute([json_encode($new_staff), $location_id]);
                    
                    // Send notifications if staff assigned
                    if (!empty($new_staff)) {
                        // Get location details
                        $stmt = $pdo->prepare("SELECT sl.*, es.title as session_title FROM session_locations sl JOIN event_sessions es ON sl.session_id = es.id WHERE sl.id = ?");
                        $stmt->execute([$location_id]);
                        $location = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($location) {
                            // Process staff IDs and send notifications
                            $processed_staff = [];
                            foreach ($new_staff as $staff_id) {
                                if (strpos($staff_id, 'admin_') === 0) {
                                    $actual_id = str_replace('admin_', '', $staff_id);
                                    $processed_staff[] = ['id' => $actual_id, 'type' => 'admin'];
                                } elseif (strpos($staff_id, 'member_') === 0) {
                                    $actual_id = str_replace('member_', '', $staff_id);
                                    $processed_staff[] = ['id' => $actual_id, 'type' => 'member'];
                                }
                            }
                            
                            include_once 'session_location_manager.php';
                            if (function_exists('sendSessionLocationAssignmentNotifications')) {
                                $notification_result = sendSessionLocationAssignmentNotifications($pdo, $location['session_id'], $location['location_name'], $processed_staff);
                                if ($notification_result) {
                                    $message = "Location reassigned successfully! Notifications sent to assigned staff.";
                                } else {
                                    $message = "Location reassigned successfully! However, there was an issue sending notifications.";
                                }
                            } else {
                                $message = "Location reassigned successfully!";
                            }
                        }
                    } else {
                        $message = "Location staff assignment cleared successfully!";
                    }
                } else {
                    $error = "Please select a location to reassign.";
                }
                break;
                
            case 'test_email_system':
                // Test email system
                $test_result = sendEmail('<EMAIL>', 'Godwin Bointa', 'Email System Test - ' . date('H:i:s'), 'This is a test email from the Session Reset Manager. Time: ' . date('Y-m-d H:i:s'), true);
                if ($test_result) {
                    $message = "Test email sent successfully! Check <EMAIL> inbox.";
                } else {
                    $error = "Test email failed. Check email configuration.";
                }
                break;
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get current data for display
$sessions = [];
$locations = [];
$devices = [];

try {
    // Get sessions with location counts
    $stmt = $pdo->prepare("
        SELECT es.*, e.title as event_title, 
               COUNT(sl.id) as location_count,
               COUNT(CASE WHEN sl.assigned_staff IS NOT NULL AND sl.assigned_staff != '[]' THEN 1 END) as assigned_count
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        LEFT JOIN session_locations sl ON es.id = sl.session_id
        GROUP BY es.id
        ORDER BY e.event_date, es.start_time
    ");
    $stmt->execute();
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all locations
    $stmt = $pdo->prepare("
        SELECT sl.*, es.title as session_title, e.title as event_title
        FROM session_locations sl
        JOIN event_sessions es ON sl.session_id = es.id
        JOIN events e ON es.event_id = e.id
        ORDER BY e.event_date, es.start_time, sl.location_name
    ");
    $stmt->execute();
    $locations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get all devices
    $stmt = $pdo->prepare("
        SELECT sd.*, es.title as session_title, e.title as event_title
        FROM session_devices sd
        LEFT JOIN event_sessions es ON sd.assigned_session = es.id
        LEFT JOIN events e ON es.event_id = e.id
        ORDER BY sd.device_name
    ");
    $stmt->execute();
    $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get staff for reassignment
    $staff_members = [];
    
    // Get admins
    $stmt = $pdo->prepare("SELECT id, full_name, email FROM admins ORDER BY full_name");
    $stmt->execute();
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get members
    $stmt = $pdo->prepare("SELECT id, full_name, email FROM members WHERE is_active = 1 ORDER BY full_name");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Combine with prefixed IDs
    foreach ($admins as $admin) {
        $staff_members[] = [
            'id' => 'admin_' . $admin['id'],
            'name' => $admin['full_name'] . ' (Admin)',
            'email' => $admin['email']
        ];
    }
    
    foreach ($members as $member) {
        $staff_members[] = [
            'id' => 'member_' . $member['id'],
            'name' => $member['full_name'] . ' (Member)',
            'email' => $member['email']
        ];
    }
    
} catch (Exception $e) {
    $error = "Error loading data: " . $e->getMessage();
}

$page_title = 'Session Reset Manager';
$page_header = 'Session Reset Manager';
$page_description = 'Reset, delete, and reassign session locations and devices';

include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>
    
    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">🔧 Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <form method="post" onsubmit="return confirm('Are you sure you want to reset ALL assignments?')">
                                <input type="hidden" name="action" value="reset_all_assignments">
                                <button type="submit" class="btn btn-warning w-100">
                                    🔄 Reset All Assignments
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL locations?')">
                                <input type="hidden" name="action" value="delete_all_locations">
                                <button type="submit" class="btn btn-danger w-100">
                                    🗑️ Delete All Locations
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL devices?')">
                                <input type="hidden" name="action" value="delete_all_devices">
                                <button type="submit" class="btn btn-danger w-100">
                                    📱 Delete All Devices
                                </button>
                            </form>
                        </div>
                        <div class="col-md-3">
                            <form method="post">
                                <input type="hidden" name="action" value="test_email_system">
                                <button type="submit" class="btn btn-info w-100">
                                    📧 Test Email System
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sessions Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📅 Sessions Overview</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Session</th>
                                    <th>Event</th>
                                    <th>Locations</th>
                                    <th>Assigned</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($sessions as $session): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($session['title']); ?></td>
                                    <td><?php echo htmlspecialchars($session['event_title']); ?></td>
                                    <td><?php echo $session['location_count']; ?></td>
                                    <td><?php echo $session['assigned_count']; ?></td>
                                    <td>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Reset all assignments for this session?')">
                                            <input type="hidden" name="action" value="reset_specific_session">
                                            <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">Reset</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Locations Management -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📍 Locations Management</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Location</th>
                                    <th>Session</th>
                                    <th>Event</th>
                                    <th>Assigned Staff</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($locations as $location): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($location['location_name']); ?></td>
                                    <td><?php echo htmlspecialchars($location['session_title']); ?></td>
                                    <td><?php echo htmlspecialchars($location['event_title']); ?></td>
                                    <td>
                                        <?php
                                        $assigned = json_decode($location['assigned_staff'], true);
                                        if ($assigned && is_array($assigned)) {
                                            echo count($assigned) . ' staff assigned';
                                        } else {
                                            echo 'No staff assigned';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $location['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($location['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#reassignModal<?php echo $location['id']; ?>">
                                            Reassign
                                        </button>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Delete this location?')">
                                            <input type="hidden" name="action" value="delete_specific_location">
                                            <input type="hidden" name="location_id" value="<?php echo $location['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-danger">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Reassign Modals -->
    <?php foreach ($locations as $location): ?>
    <div class="modal fade" id="reassignModal<?php echo $location['id']; ?>" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <form method="post">
                    <div class="modal-header">
                        <h5 class="modal-title">Reassign Location: <?php echo htmlspecialchars($location['location_name']); ?></h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reassign_location">
                        <input type="hidden" name="location_id" value="<?php echo $location['id']; ?>">

                        <div class="mb-3">
                            <label class="form-label">Assign Staff</label>
                            <select class="form-select" name="new_staff[]" multiple>
                                <?php foreach ($staff_members as $staff): ?>
                                    <option value="<?php echo $staff['id']; ?>">
                                        <?php echo htmlspecialchars($staff['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">Hold Ctrl/Cmd to select multiple staff members</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Reassign & Notify</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Devices Management -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">📱 Devices Management</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Device</th>
                                    <th>Type</th>
                                    <th>Assigned Session</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($devices as $device): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($device['device_name']); ?></td>
                                    <td><?php echo ucfirst($device['device_type']); ?></td>
                                    <td>
                                        <?php
                                        if ($device['session_title']) {
                                            echo htmlspecialchars($device['session_title']);
                                        } else {
                                            echo 'Not assigned';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?php echo $device['status'] === 'assigned' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($device['status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <form method="post" style="display:inline;" onsubmit="return confirm('Reset this device assignment?')">
                                            <input type="hidden" name="action" value="reset_device">
                                            <input type="hidden" name="device_id" value="<?php echo $device['device_id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-warning">Reset</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo count($sessions); ?></h5>
                    <p class="card-text">Total Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo count($locations); ?></h5>
                    <p class="card-text">Total Locations</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo count($devices); ?></h5>
                    <p class="card-text">Total Devices</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title"><?php echo count($staff_members); ?></h5>
                    <p class="card-text">Available Staff</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
