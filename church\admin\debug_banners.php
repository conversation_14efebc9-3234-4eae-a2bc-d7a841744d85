<?php
require_once '../config.php';

echo "<h2>Event Banner Debug Analysis</h2>";
echo "<style>
.debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
.success { color: green; font-weight: bold; }
.error { color: red; font-weight: bold; }
.warning { color: orange; font-weight: bold; }
.info { color: blue; }
</style>";

// Get all events with their banner information
$sql = "
    SELECT e.id, e.title, e.start_date, e.start_time,
           ef.id as file_id, ef.file_name, ef.file_path, ef.file_type, ef.is_header_banner
    FROM events e 
    LEFT JOIN event_files ef ON e.id = ef.event_id AND ef.is_header_banner = 1
    WHERE e.title LIKE '%Sunday%' OR e.title LIKE '%Emmanuel%'
    ORDER BY e.id, ef.is_header_banner DESC
";

$stmt = $conn->prepare($sql);
$stmt->execute();
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<div class='debug-section'>";
echo "<h3>1. Database Query Results</h3>";
foreach ($events as $event) {
    echo "<div style='margin: 10px 0; padding: 10px; background: #f9f9f9;'>";
    echo "<strong>Event:</strong> " . htmlspecialchars($event['title']) . " (ID: {$event['id']})<br>";
    
    if ($event['file_id']) {
        echo "<span class='success'>✓ Has banner file</span><br>";
        echo "<strong>File ID:</strong> {$event['file_id']}<br>";
        echo "<strong>File Name:</strong> " . htmlspecialchars($event['file_name']) . "<br>";
        echo "<strong>File Path (DB):</strong> " . htmlspecialchars($event['file_path']) . "<br>";
        echo "<strong>File Type:</strong> " . htmlspecialchars($event['file_type']) . "<br>";
        echo "<strong>Is Header Banner:</strong> " . ($event['is_header_banner'] ? 'Yes' : 'No') . "<br>";
    } else {
        echo "<span class='error'>✗ No banner file found</span><br>";
    }
    echo "</div>";
}
echo "</div>";

// Test the admin_file_path function
echo "<div class='debug-section'>";
echo "<h3>2. Path Conversion Testing</h3>";
foreach ($events as $event) {
    if ($event['file_path']) {
        echo "<div style='margin: 10px 0; padding: 10px; background: #f0f8ff;'>";
        echo "<strong>Event:</strong> " . htmlspecialchars($event['title']) . "<br>";
        echo "<strong>Original DB Path:</strong> " . htmlspecialchars($event['file_path']) . "<br>";
        
        $converted_path = admin_file_path($event['file_path']);
        echo "<strong>Converted Path:</strong> " . htmlspecialchars($converted_path) . "<br>";
        
        // Check if file exists
        $server_path = __DIR__ . '/' . $converted_path;
        echo "<strong>Server Path:</strong> " . htmlspecialchars($server_path) . "<br>";
        
        if (file_exists($server_path)) {
            echo "<span class='success'>✓ File exists on server</span><br>";
        } else {
            echo "<span class='error'>✗ File NOT found on server</span><br>";
        }
        echo "</div>";
    }
}
echo "</div>";

// Test thumbnail path generation
echo "<div class='debug-section'>";
echo "<h3>3. Thumbnail Path Testing</h3>";
foreach ($events as $event) {
    if ($event['file_path'] && strpos($event['file_type'], 'image/') === 0) {
        echo "<div style='margin: 10px 0; padding: 10px; background: #fff8f0;'>";
        echo "<strong>Event:</strong> " . htmlspecialchars($event['title']) . "<br>";
        
        $banner_path = admin_file_path($event['file_path']);
        $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($banner_path)) . '/thumb_' . basename($banner_path);
        
        echo "<strong>Banner Path:</strong> " . htmlspecialchars($banner_path) . "<br>";
        echo "<strong>Thumbnail Path:</strong> " . htmlspecialchars($thumbnail_path) . "<br>";
        
        $server_thumbnail_path = __DIR__ . '/' . $thumbnail_path;
        $server_banner_path = __DIR__ . '/' . $banner_path;
        
        echo "<strong>Server Thumbnail Path:</strong> " . htmlspecialchars($server_thumbnail_path) . "<br>";
        echo "<strong>Server Banner Path:</strong> " . htmlspecialchars($server_banner_path) . "<br>";
        
        if (file_exists($server_thumbnail_path)) {
            echo "<span class='success'>✓ Thumbnail exists</span><br>";
        } else {
            echo "<span class='warning'>⚠ Thumbnail not found</span><br>";
        }
        
        if (file_exists($server_banner_path)) {
            echo "<span class='success'>✓ Banner exists</span><br>";
        } else {
            echo "<span class='error'>✗ Banner not found</span><br>";
        }
        
        // Determine what would be displayed
        $display_path = (file_exists($server_thumbnail_path)) ? $thumbnail_path :
                       (file_exists($server_banner_path) ? $banner_path : $banner_path);
        echo "<strong>Display Path:</strong> " . htmlspecialchars($display_path) . "<br>";
        echo "</div>";
    }
}
echo "</div>";

// Check upload directories
echo "<div class='debug-section'>";
echo "<h3>4. Directory Structure Check</h3>";
$upload_dirs = [
    '../uploads/events/promotional',
    '../uploads/events/thumbnails',
    '../uploads/events'
];

foreach ($upload_dirs as $dir) {
    $server_dir = __DIR__ . '/' . $dir;
    echo "<strong>Directory:</strong> " . htmlspecialchars($dir) . "<br>";
    echo "<strong>Server Path:</strong> " . htmlspecialchars($server_dir) . "<br>";
    
    if (is_dir($server_dir)) {
        echo "<span class='success'>✓ Directory exists</span><br>";
        $files = scandir($server_dir);
        $file_count = count($files) - 2; // Exclude . and ..
        echo "<strong>Files in directory:</strong> $file_count<br>";
        
        if ($file_count > 0) {
            echo "<strong>Sample files:</strong><br>";
            $count = 0;
            foreach ($files as $file) {
                if ($file !== '.' && $file !== '..' && $count < 5) {
                    echo "• " . htmlspecialchars($file) . "<br>";
                    $count++;
                }
            }
        }
    } else {
        echo "<span class='error'>✗ Directory not found</span><br>";
    }
    echo "<br>";
}
echo "</div>";

// Test the exact logic from events.php
echo "<div class='debug-section'>";
echo "<h3>5. Events.php Logic Simulation</h3>";
foreach ($events as $event) {
    if ($event['file_path'] && strpos($event['file_type'], 'image/') === 0) {
        echo "<div style='margin: 10px 0; padding: 10px; background: #f5f5f5; border-left: 4px solid #007bff;'>";
        echo "<strong>Event:</strong> " . htmlspecialchars($event['title']) . "<br>";
        
        // Simulate the exact logic from events.php lines 770-782
        $dbBannerPath = $event['file_path'];
        $banner_path = admin_file_path($dbBannerPath);
        $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($banner_path)) . '/thumb_' . basename($banner_path);
        
        $serverThumbnailPath = __DIR__ . '/' . $thumbnail_path;
        $serverBannerPath = __DIR__ . '/' . $banner_path;
        
        $display_path = (file_exists($serverThumbnailPath)) ? $thumbnail_path :
                       (file_exists($serverBannerPath) ? $banner_path : $banner_path);
        
        echo "<strong>Would display:</strong> " . htmlspecialchars($display_path) . "<br>";
        
        // Check if this would actually show an image
        $final_server_path = __DIR__ . '/' . $display_path;
        if (file_exists($final_server_path)) {
            echo "<span class='success'>✓ Image would display correctly</span><br>";
            echo "<strong>Final image URL:</strong> " . htmlspecialchars($display_path) . "<br>";
        } else {
            echo "<span class='error'>✗ Image would NOT display - file missing</span><br>";
            echo "<strong>Missing file:</strong> " . htmlspecialchars($final_server_path) . "<br>";
        }
        echo "</div>";
    }
}
echo "</div>";
?>
