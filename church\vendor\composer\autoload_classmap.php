<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'BaconQrCode\\Common\\BitArray' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitArray.php',
    'BaconQrCode\\Common\\BitMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitMatrix.php',
    'BaconQrCode\\Common\\BitUtils' => $vendorDir . '/bacon/bacon-qr-code/src/Common/BitUtils.php',
    'BaconQrCode\\Common\\CharacterSetEci' => $vendorDir . '/bacon/bacon-qr-code/src/Common/CharacterSetEci.php',
    'BaconQrCode\\Common\\EcBlock' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlock.php',
    'BaconQrCode\\Common\\EcBlocks' => $vendorDir . '/bacon/bacon-qr-code/src/Common/EcBlocks.php',
    'BaconQrCode\\Common\\ErrorCorrectionLevel' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ErrorCorrectionLevel.php',
    'BaconQrCode\\Common\\FormatInformation' => $vendorDir . '/bacon/bacon-qr-code/src/Common/FormatInformation.php',
    'BaconQrCode\\Common\\Mode' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Mode.php',
    'BaconQrCode\\Common\\ReedSolomonCodec' => $vendorDir . '/bacon/bacon-qr-code/src/Common/ReedSolomonCodec.php',
    'BaconQrCode\\Common\\Version' => $vendorDir . '/bacon/bacon-qr-code/src/Common/Version.php',
    'BaconQrCode\\Encoder\\BlockPair' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/BlockPair.php',
    'BaconQrCode\\Encoder\\ByteMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/ByteMatrix.php',
    'BaconQrCode\\Encoder\\Encoder' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/Encoder.php',
    'BaconQrCode\\Encoder\\MaskUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MaskUtil.php',
    'BaconQrCode\\Encoder\\MatrixUtil' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/MatrixUtil.php',
    'BaconQrCode\\Encoder\\QrCode' => $vendorDir . '/bacon/bacon-qr-code/src/Encoder/QrCode.php',
    'BaconQrCode\\Exception\\ExceptionInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/ExceptionInterface.php',
    'BaconQrCode\\Exception\\InvalidArgumentException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/InvalidArgumentException.php',
    'BaconQrCode\\Exception\\OutOfBoundsException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/OutOfBoundsException.php',
    'BaconQrCode\\Exception\\RuntimeException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/RuntimeException.php',
    'BaconQrCode\\Exception\\UnexpectedValueException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/UnexpectedValueException.php',
    'BaconQrCode\\Exception\\WriterException' => $vendorDir . '/bacon/bacon-qr-code/src/Exception/WriterException.php',
    'BaconQrCode\\Renderer\\Color\\Alpha' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Alpha.php',
    'BaconQrCode\\Renderer\\Color\\Cmyk' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Cmyk.php',
    'BaconQrCode\\Renderer\\Color\\ColorInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/ColorInterface.php',
    'BaconQrCode\\Renderer\\Color\\Gray' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Gray.php',
    'BaconQrCode\\Renderer\\Color\\Rgb' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Color/Rgb.php',
    'BaconQrCode\\Renderer\\Eye\\CompositeEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/CompositeEye.php',
    'BaconQrCode\\Renderer\\Eye\\EyeInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/EyeInterface.php',
    'BaconQrCode\\Renderer\\Eye\\ModuleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/ModuleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SimpleCircleEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SimpleCircleEye.php',
    'BaconQrCode\\Renderer\\Eye\\SquareEye' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Eye/SquareEye.php',
    'BaconQrCode\\Renderer\\ImageRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/ImageRenderer.php',
    'BaconQrCode\\Renderer\\Image\\EpsImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/EpsImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\ImageBackEndInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImageBackEndInterface.php',
    'BaconQrCode\\Renderer\\Image\\ImagickImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/ImagickImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\SvgImageBackEnd' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/SvgImageBackEnd.php',
    'BaconQrCode\\Renderer\\Image\\TransformationMatrix' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Image/TransformationMatrix.php',
    'BaconQrCode\\Renderer\\Module\\DotsModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/DotsModule.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\Edge' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/Edge.php',
    'BaconQrCode\\Renderer\\Module\\EdgeIterator\\EdgeIterator' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/EdgeIterator/EdgeIterator.php',
    'BaconQrCode\\Renderer\\Module\\ModuleInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/ModuleInterface.php',
    'BaconQrCode\\Renderer\\Module\\RoundnessModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/RoundnessModule.php',
    'BaconQrCode\\Renderer\\Module\\SquareModule' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Module/SquareModule.php',
    'BaconQrCode\\Renderer\\Path\\Close' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Close.php',
    'BaconQrCode\\Renderer\\Path\\Curve' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Curve.php',
    'BaconQrCode\\Renderer\\Path\\EllipticArc' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/EllipticArc.php',
    'BaconQrCode\\Renderer\\Path\\Line' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Line.php',
    'BaconQrCode\\Renderer\\Path\\Move' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Move.php',
    'BaconQrCode\\Renderer\\Path\\OperationInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/OperationInterface.php',
    'BaconQrCode\\Renderer\\Path\\Path' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/Path/Path.php',
    'BaconQrCode\\Renderer\\PlainTextRenderer' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/PlainTextRenderer.php',
    'BaconQrCode\\Renderer\\RendererInterface' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererInterface.php',
    'BaconQrCode\\Renderer\\RendererStyle\\EyeFill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/EyeFill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Fill' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Fill.php',
    'BaconQrCode\\Renderer\\RendererStyle\\Gradient' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/Gradient.php',
    'BaconQrCode\\Renderer\\RendererStyle\\GradientType' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/GradientType.php',
    'BaconQrCode\\Renderer\\RendererStyle\\RendererStyle' => $vendorDir . '/bacon/bacon-qr-code/src/Renderer/RendererStyle/RendererStyle.php',
    'BaconQrCode\\Writer' => $vendorDir . '/bacon/bacon-qr-code/src/Writer.php',
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'DASPRiD\\Enum\\AbstractEnum' => $vendorDir . '/dasprid/enum/src/AbstractEnum.php',
    'DASPRiD\\Enum\\EnumMap' => $vendorDir . '/dasprid/enum/src/EnumMap.php',
    'DASPRiD\\Enum\\Exception\\CloneNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/CloneNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\ExceptionInterface' => $vendorDir . '/dasprid/enum/src/Exception/ExceptionInterface.php',
    'DASPRiD\\Enum\\Exception\\ExpectationException' => $vendorDir . '/dasprid/enum/src/Exception/ExpectationException.php',
    'DASPRiD\\Enum\\Exception\\IllegalArgumentException' => $vendorDir . '/dasprid/enum/src/Exception/IllegalArgumentException.php',
    'DASPRiD\\Enum\\Exception\\MismatchException' => $vendorDir . '/dasprid/enum/src/Exception/MismatchException.php',
    'DASPRiD\\Enum\\Exception\\SerializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/SerializeNotSupportedException.php',
    'DASPRiD\\Enum\\Exception\\UnserializeNotSupportedException' => $vendorDir . '/dasprid/enum/src/Exception/UnserializeNotSupportedException.php',
    'DASPRiD\\Enum\\NullValue' => $vendorDir . '/dasprid/enum/src/NullValue.php',
    'Dompdf\\Adapter\\CPDF' => $vendorDir . '/dompdf/dompdf/src/Adapter/CPDF.php',
    'Dompdf\\Adapter\\GD' => $vendorDir . '/dompdf/dompdf/src/Adapter/GD.php',
    'Dompdf\\Adapter\\PDFLib' => $vendorDir . '/dompdf/dompdf/src/Adapter/PDFLib.php',
    'Dompdf\\Canvas' => $vendorDir . '/dompdf/dompdf/src/Canvas.php',
    'Dompdf\\CanvasFactory' => $vendorDir . '/dompdf/dompdf/src/CanvasFactory.php',
    'Dompdf\\Cellmap' => $vendorDir . '/dompdf/dompdf/src/Cellmap.php',
    'Dompdf\\Cpdf' => $vendorDir . '/dompdf/dompdf/lib/Cpdf.php',
    'Dompdf\\Css\\AttributeTranslator' => $vendorDir . '/dompdf/dompdf/src/Css/AttributeTranslator.php',
    'Dompdf\\Css\\Color' => $vendorDir . '/dompdf/dompdf/src/Css/Color.php',
    'Dompdf\\Css\\Style' => $vendorDir . '/dompdf/dompdf/src/Css/Style.php',
    'Dompdf\\Css\\Stylesheet' => $vendorDir . '/dompdf/dompdf/src/Css/Stylesheet.php',
    'Dompdf\\Dompdf' => $vendorDir . '/dompdf/dompdf/src/Dompdf.php',
    'Dompdf\\Exception' => $vendorDir . '/dompdf/dompdf/src/Exception.php',
    'Dompdf\\Exception\\ImageException' => $vendorDir . '/dompdf/dompdf/src/Exception/ImageException.php',
    'Dompdf\\FontMetrics' => $vendorDir . '/dompdf/dompdf/src/FontMetrics.php',
    'Dompdf\\Frame' => $vendorDir . '/dompdf/dompdf/src/Frame.php',
    'Dompdf\\FrameDecorator\\AbstractFrameDecorator' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/AbstractFrameDecorator.php',
    'Dompdf\\FrameDecorator\\Block' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Block.php',
    'Dompdf\\FrameDecorator\\Image' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Image.php',
    'Dompdf\\FrameDecorator\\Inline' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Inline.php',
    'Dompdf\\FrameDecorator\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/ListBullet.php',
    'Dompdf\\FrameDecorator\\ListBulletImage' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/ListBulletImage.php',
    'Dompdf\\FrameDecorator\\NullFrameDecorator' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/NullFrameDecorator.php',
    'Dompdf\\FrameDecorator\\Page' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Page.php',
    'Dompdf\\FrameDecorator\\Table' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Table.php',
    'Dompdf\\FrameDecorator\\TableCell' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableCell.php',
    'Dompdf\\FrameDecorator\\TableRow' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableRow.php',
    'Dompdf\\FrameDecorator\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/TableRowGroup.php',
    'Dompdf\\FrameDecorator\\Text' => $vendorDir . '/dompdf/dompdf/src/FrameDecorator/Text.php',
    'Dompdf\\FrameReflower\\AbstractFrameReflower' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/AbstractFrameReflower.php',
    'Dompdf\\FrameReflower\\Block' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Block.php',
    'Dompdf\\FrameReflower\\Image' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Image.php',
    'Dompdf\\FrameReflower\\Inline' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Inline.php',
    'Dompdf\\FrameReflower\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/ListBullet.php',
    'Dompdf\\FrameReflower\\NullFrameReflower' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/NullFrameReflower.php',
    'Dompdf\\FrameReflower\\Page' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Page.php',
    'Dompdf\\FrameReflower\\Table' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Table.php',
    'Dompdf\\FrameReflower\\TableCell' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableCell.php',
    'Dompdf\\FrameReflower\\TableRow' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableRow.php',
    'Dompdf\\FrameReflower\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/TableRowGroup.php',
    'Dompdf\\FrameReflower\\Text' => $vendorDir . '/dompdf/dompdf/src/FrameReflower/Text.php',
    'Dompdf\\Frame\\Factory' => $vendorDir . '/dompdf/dompdf/src/Frame/Factory.php',
    'Dompdf\\Frame\\FrameListIterator' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameListIterator.php',
    'Dompdf\\Frame\\FrameTree' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameTree.php',
    'Dompdf\\Frame\\FrameTreeIterator' => $vendorDir . '/dompdf/dompdf/src/Frame/FrameTreeIterator.php',
    'Dompdf\\Helpers' => $vendorDir . '/dompdf/dompdf/src/Helpers.php',
    'Dompdf\\Image\\Cache' => $vendorDir . '/dompdf/dompdf/src/Image/Cache.php',
    'Dompdf\\JavascriptEmbedder' => $vendorDir . '/dompdf/dompdf/src/JavascriptEmbedder.php',
    'Dompdf\\LineBox' => $vendorDir . '/dompdf/dompdf/src/LineBox.php',
    'Dompdf\\Options' => $vendorDir . '/dompdf/dompdf/src/Options.php',
    'Dompdf\\PhpEvaluator' => $vendorDir . '/dompdf/dompdf/src/PhpEvaluator.php',
    'Dompdf\\Positioner\\Absolute' => $vendorDir . '/dompdf/dompdf/src/Positioner/Absolute.php',
    'Dompdf\\Positioner\\AbstractPositioner' => $vendorDir . '/dompdf/dompdf/src/Positioner/AbstractPositioner.php',
    'Dompdf\\Positioner\\Block' => $vendorDir . '/dompdf/dompdf/src/Positioner/Block.php',
    'Dompdf\\Positioner\\Fixed' => $vendorDir . '/dompdf/dompdf/src/Positioner/Fixed.php',
    'Dompdf\\Positioner\\Inline' => $vendorDir . '/dompdf/dompdf/src/Positioner/Inline.php',
    'Dompdf\\Positioner\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/Positioner/ListBullet.php',
    'Dompdf\\Positioner\\NullPositioner' => $vendorDir . '/dompdf/dompdf/src/Positioner/NullPositioner.php',
    'Dompdf\\Positioner\\TableCell' => $vendorDir . '/dompdf/dompdf/src/Positioner/TableCell.php',
    'Dompdf\\Positioner\\TableRow' => $vendorDir . '/dompdf/dompdf/src/Positioner/TableRow.php',
    'Dompdf\\Renderer' => $vendorDir . '/dompdf/dompdf/src/Renderer.php',
    'Dompdf\\Renderer\\AbstractRenderer' => $vendorDir . '/dompdf/dompdf/src/Renderer/AbstractRenderer.php',
    'Dompdf\\Renderer\\Block' => $vendorDir . '/dompdf/dompdf/src/Renderer/Block.php',
    'Dompdf\\Renderer\\Image' => $vendorDir . '/dompdf/dompdf/src/Renderer/Image.php',
    'Dompdf\\Renderer\\Inline' => $vendorDir . '/dompdf/dompdf/src/Renderer/Inline.php',
    'Dompdf\\Renderer\\ListBullet' => $vendorDir . '/dompdf/dompdf/src/Renderer/ListBullet.php',
    'Dompdf\\Renderer\\TableCell' => $vendorDir . '/dompdf/dompdf/src/Renderer/TableCell.php',
    'Dompdf\\Renderer\\TableRowGroup' => $vendorDir . '/dompdf/dompdf/src/Renderer/TableRowGroup.php',
    'Dompdf\\Renderer\\Text' => $vendorDir . '/dompdf/dompdf/src/Renderer/Text.php',
    'Endroid\\QrCode\\Bacon\\ErrorCorrectionLevelConverter' => $vendorDir . '/endroid/qr-code/src/Bacon/ErrorCorrectionLevelConverter.php',
    'Endroid\\QrCode\\Bacon\\MatrixFactory' => $vendorDir . '/endroid/qr-code/src/Bacon/MatrixFactory.php',
    'Endroid\\QrCode\\Builder\\Builder' => $vendorDir . '/endroid/qr-code/src/Builder/Builder.php',
    'Endroid\\QrCode\\Builder\\BuilderInterface' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderInterface.php',
    'Endroid\\QrCode\\Builder\\BuilderRegistry' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderRegistry.php',
    'Endroid\\QrCode\\Builder\\BuilderRegistryInterface' => $vendorDir . '/endroid/qr-code/src/Builder/BuilderRegistryInterface.php',
    'Endroid\\QrCode\\Color\\Color' => $vendorDir . '/endroid/qr-code/src/Color/Color.php',
    'Endroid\\QrCode\\Color\\ColorInterface' => $vendorDir . '/endroid/qr-code/src/Color/ColorInterface.php',
    'Endroid\\QrCode\\Encoding\\Encoding' => $vendorDir . '/endroid/qr-code/src/Encoding/Encoding.php',
    'Endroid\\QrCode\\Encoding\\EncodingInterface' => $vendorDir . '/endroid/qr-code/src/Encoding/EncodingInterface.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelHigh' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelHigh.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelInterface' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelInterface.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelLow' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelLow.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelMedium' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelMedium.php',
    'Endroid\\QrCode\\ErrorCorrectionLevel\\ErrorCorrectionLevelQuartile' => $vendorDir . '/endroid/qr-code/src/ErrorCorrectionLevel/ErrorCorrectionLevelQuartile.php',
    'Endroid\\QrCode\\Exception\\ValidationException' => $vendorDir . '/endroid/qr-code/src/Exception/ValidationException.php',
    'Endroid\\QrCode\\ImageData\\LabelImageData' => $vendorDir . '/endroid/qr-code/src/ImageData/LabelImageData.php',
    'Endroid\\QrCode\\ImageData\\LogoImageData' => $vendorDir . '/endroid/qr-code/src/ImageData/LogoImageData.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentCenter' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentCenter.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentInterface' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentInterface.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentLeft' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentLeft.php',
    'Endroid\\QrCode\\Label\\Alignment\\LabelAlignmentRight' => $vendorDir . '/endroid/qr-code/src/Label/Alignment/LabelAlignmentRight.php',
    'Endroid\\QrCode\\Label\\Font\\Font' => $vendorDir . '/endroid/qr-code/src/Label/Font/Font.php',
    'Endroid\\QrCode\\Label\\Font\\FontInterface' => $vendorDir . '/endroid/qr-code/src/Label/Font/FontInterface.php',
    'Endroid\\QrCode\\Label\\Font\\NotoSans' => $vendorDir . '/endroid/qr-code/src/Label/Font/NotoSans.php',
    'Endroid\\QrCode\\Label\\Font\\OpenSans' => $vendorDir . '/endroid/qr-code/src/Label/Font/OpenSans.php',
    'Endroid\\QrCode\\Label\\Label' => $vendorDir . '/endroid/qr-code/src/Label/Label.php',
    'Endroid\\QrCode\\Label\\LabelInterface' => $vendorDir . '/endroid/qr-code/src/Label/LabelInterface.php',
    'Endroid\\QrCode\\Label\\Margin\\Margin' => $vendorDir . '/endroid/qr-code/src/Label/Margin/Margin.php',
    'Endroid\\QrCode\\Label\\Margin\\MarginInterface' => $vendorDir . '/endroid/qr-code/src/Label/Margin/MarginInterface.php',
    'Endroid\\QrCode\\Logo\\Logo' => $vendorDir . '/endroid/qr-code/src/Logo/Logo.php',
    'Endroid\\QrCode\\Logo\\LogoInterface' => $vendorDir . '/endroid/qr-code/src/Logo/LogoInterface.php',
    'Endroid\\QrCode\\Matrix\\Matrix' => $vendorDir . '/endroid/qr-code/src/Matrix/Matrix.php',
    'Endroid\\QrCode\\Matrix\\MatrixFactoryInterface' => $vendorDir . '/endroid/qr-code/src/Matrix/MatrixFactoryInterface.php',
    'Endroid\\QrCode\\Matrix\\MatrixInterface' => $vendorDir . '/endroid/qr-code/src/Matrix/MatrixInterface.php',
    'Endroid\\QrCode\\QrCode' => $vendorDir . '/endroid/qr-code/src/QrCode.php',
    'Endroid\\QrCode\\QrCodeInterface' => $vendorDir . '/endroid/qr-code/src/QrCodeInterface.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeEnlarge' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeEnlarge.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeInterface' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeInterface.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeMargin' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeMargin.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeNone' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeNone.php',
    'Endroid\\QrCode\\RoundBlockSizeMode\\RoundBlockSizeModeShrink' => $vendorDir . '/endroid/qr-code/src/RoundBlockSizeMode/RoundBlockSizeModeShrink.php',
    'Endroid\\QrCode\\Writer\\AbstractGdWriter' => $vendorDir . '/endroid/qr-code/src/Writer/AbstractGdWriter.php',
    'Endroid\\QrCode\\Writer\\BinaryWriter' => $vendorDir . '/endroid/qr-code/src/Writer/BinaryWriter.php',
    'Endroid\\QrCode\\Writer\\ConsoleWriter' => $vendorDir . '/endroid/qr-code/src/Writer/ConsoleWriter.php',
    'Endroid\\QrCode\\Writer\\DebugWriter' => $vendorDir . '/endroid/qr-code/src/Writer/DebugWriter.php',
    'Endroid\\QrCode\\Writer\\EpsWriter' => $vendorDir . '/endroid/qr-code/src/Writer/EpsWriter.php',
    'Endroid\\QrCode\\Writer\\GifWriter' => $vendorDir . '/endroid/qr-code/src/Writer/GifWriter.php',
    'Endroid\\QrCode\\Writer\\PdfWriter' => $vendorDir . '/endroid/qr-code/src/Writer/PdfWriter.php',
    'Endroid\\QrCode\\Writer\\PngWriter' => $vendorDir . '/endroid/qr-code/src/Writer/PngWriter.php',
    'Endroid\\QrCode\\Writer\\Result\\AbstractResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/AbstractResult.php',
    'Endroid\\QrCode\\Writer\\Result\\BinaryResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/BinaryResult.php',
    'Endroid\\QrCode\\Writer\\Result\\ConsoleResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/ConsoleResult.php',
    'Endroid\\QrCode\\Writer\\Result\\DebugResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/DebugResult.php',
    'Endroid\\QrCode\\Writer\\Result\\EpsResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/EpsResult.php',
    'Endroid\\QrCode\\Writer\\Result\\GdResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/GdResult.php',
    'Endroid\\QrCode\\Writer\\Result\\GifResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/GifResult.php',
    'Endroid\\QrCode\\Writer\\Result\\PdfResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/PdfResult.php',
    'Endroid\\QrCode\\Writer\\Result\\PngResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/PngResult.php',
    'Endroid\\QrCode\\Writer\\Result\\ResultInterface' => $vendorDir . '/endroid/qr-code/src/Writer/Result/ResultInterface.php',
    'Endroid\\QrCode\\Writer\\Result\\SvgResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/SvgResult.php',
    'Endroid\\QrCode\\Writer\\Result\\WebPResult' => $vendorDir . '/endroid/qr-code/src/Writer/Result/WebPResult.php',
    'Endroid\\QrCode\\Writer\\SvgWriter' => $vendorDir . '/endroid/qr-code/src/Writer/SvgWriter.php',
    'Endroid\\QrCode\\Writer\\ValidatingWriterInterface' => $vendorDir . '/endroid/qr-code/src/Writer/ValidatingWriterInterface.php',
    'Endroid\\QrCode\\Writer\\WebPWriter' => $vendorDir . '/endroid/qr-code/src/Writer/WebPWriter.php',
    'Endroid\\QrCode\\Writer\\WriterInterface' => $vendorDir . '/endroid/qr-code/src/Writer/WriterInterface.php',
    'FontLib\\AdobeFontMetrics' => $vendorDir . '/phenx/php-font-lib/src/FontLib/AdobeFontMetrics.php',
    'FontLib\\BinaryStream' => $vendorDir . '/phenx/php-font-lib/src/FontLib/BinaryStream.php',
    'FontLib\\EOT\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EOT/File.php',
    'FontLib\\EOT\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EOT/Header.php',
    'FontLib\\EncodingMap' => $vendorDir . '/phenx/php-font-lib/src/FontLib/EncodingMap.php',
    'FontLib\\Exception\\FontNotFoundException' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Exception/FontNotFoundException.php',
    'FontLib\\Font' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Font.php',
    'FontLib\\Glyph\\Outline' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/Outline.php',
    'FontLib\\Glyph\\OutlineComponent' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineComponent.php',
    'FontLib\\Glyph\\OutlineComposite' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineComposite.php',
    'FontLib\\Glyph\\OutlineSimple' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Glyph/OutlineSimple.php',
    'FontLib\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Header.php',
    'FontLib\\OpenType\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/OpenType/File.php',
    'FontLib\\OpenType\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/OpenType/TableDirectoryEntry.php',
    'FontLib\\Table\\DirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/DirectoryEntry.php',
    'FontLib\\Table\\Table' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Table.php',
    'FontLib\\Table\\Type\\cmap' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/cmap.php',
    'FontLib\\Table\\Type\\cvt' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/cvt.php',
    'FontLib\\Table\\Type\\fpgm' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/fpgm.php',
    'FontLib\\Table\\Type\\glyf' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/glyf.php',
    'FontLib\\Table\\Type\\head' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/head.php',
    'FontLib\\Table\\Type\\hhea' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/hhea.php',
    'FontLib\\Table\\Type\\hmtx' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/hmtx.php',
    'FontLib\\Table\\Type\\kern' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/kern.php',
    'FontLib\\Table\\Type\\loca' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/loca.php',
    'FontLib\\Table\\Type\\maxp' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/maxp.php',
    'FontLib\\Table\\Type\\name' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/name.php',
    'FontLib\\Table\\Type\\nameRecord' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/nameRecord.php',
    'FontLib\\Table\\Type\\os2' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/os2.php',
    'FontLib\\Table\\Type\\post' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/post.php',
    'FontLib\\Table\\Type\\prep' => $vendorDir . '/phenx/php-font-lib/src/FontLib/Table/Type/prep.php',
    'FontLib\\TrueType\\Collection' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/Collection.php',
    'FontLib\\TrueType\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/File.php',
    'FontLib\\TrueType\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/Header.php',
    'FontLib\\TrueType\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/TrueType/TableDirectoryEntry.php',
    'FontLib\\WOFF\\File' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/File.php',
    'FontLib\\WOFF\\Header' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/Header.php',
    'FontLib\\WOFF\\TableDirectoryEntry' => $vendorDir . '/phenx/php-font-lib/src/FontLib/WOFF/TableDirectoryEntry.php',
    'Masterminds\\HTML5' => $vendorDir . '/masterminds/html5/src/HTML5.php',
    'Masterminds\\HTML5\\Elements' => $vendorDir . '/masterminds/html5/src/HTML5/Elements.php',
    'Masterminds\\HTML5\\Entities' => $vendorDir . '/masterminds/html5/src/HTML5/Entities.php',
    'Masterminds\\HTML5\\Exception' => $vendorDir . '/masterminds/html5/src/HTML5/Exception.php',
    'Masterminds\\HTML5\\InstructionProcessor' => $vendorDir . '/masterminds/html5/src/HTML5/InstructionProcessor.php',
    'Masterminds\\HTML5\\Parser\\CharacterReference' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/CharacterReference.php',
    'Masterminds\\HTML5\\Parser\\DOMTreeBuilder' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/DOMTreeBuilder.php',
    'Masterminds\\HTML5\\Parser\\EventHandler' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/EventHandler.php',
    'Masterminds\\HTML5\\Parser\\FileInputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/FileInputStream.php',
    'Masterminds\\HTML5\\Parser\\InputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/InputStream.php',
    'Masterminds\\HTML5\\Parser\\ParseError' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/ParseError.php',
    'Masterminds\\HTML5\\Parser\\Scanner' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/Scanner.php',
    'Masterminds\\HTML5\\Parser\\StringInputStream' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/StringInputStream.php',
    'Masterminds\\HTML5\\Parser\\Tokenizer' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/Tokenizer.php',
    'Masterminds\\HTML5\\Parser\\TreeBuildingRules' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/TreeBuildingRules.php',
    'Masterminds\\HTML5\\Parser\\UTF8Utils' => $vendorDir . '/masterminds/html5/src/HTML5/Parser/UTF8Utils.php',
    'Masterminds\\HTML5\\Serializer\\HTML5Entities' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/HTML5Entities.php',
    'Masterminds\\HTML5\\Serializer\\OutputRules' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/OutputRules.php',
    'Masterminds\\HTML5\\Serializer\\RulesInterface' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/RulesInterface.php',
    'Masterminds\\HTML5\\Serializer\\Traverser' => $vendorDir . '/masterminds/html5/src/HTML5/Serializer/Traverser.php',
    'PHPMailer\\PHPMailer\\DSNConfigurator' => $vendorDir . '/phpmailer/phpmailer/src/DSNConfigurator.php',
    'PHPMailer\\PHPMailer\\Exception' => $vendorDir . '/phpmailer/phpmailer/src/Exception.php',
    'PHPMailer\\PHPMailer\\OAuth' => $vendorDir . '/phpmailer/phpmailer/src/OAuth.php',
    'PHPMailer\\PHPMailer\\OAuthTokenProvider' => $vendorDir . '/phpmailer/phpmailer/src/OAuthTokenProvider.php',
    'PHPMailer\\PHPMailer\\PHPMailer' => $vendorDir . '/phpmailer/phpmailer/src/PHPMailer.php',
    'PHPMailer\\PHPMailer\\POP3' => $vendorDir . '/phpmailer/phpmailer/src/POP3.php',
    'PHPMailer\\PHPMailer\\SMTP' => $vendorDir . '/phpmailer/phpmailer/src/SMTP.php',
    'ParagonIE\\ConstantTime\\Base32' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32.php',
    'ParagonIE\\ConstantTime\\Base32Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Base32Hex.php',
    'ParagonIE\\ConstantTime\\Base64' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64.php',
    'ParagonIE\\ConstantTime\\Base64DotSlash' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlash.php',
    'ParagonIE\\ConstantTime\\Base64DotSlashOrdered' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64DotSlashOrdered.php',
    'ParagonIE\\ConstantTime\\Base64UrlSafe' => $vendorDir . '/paragonie/constant_time_encoding/src/Base64UrlSafe.php',
    'ParagonIE\\ConstantTime\\Binary' => $vendorDir . '/paragonie/constant_time_encoding/src/Binary.php',
    'ParagonIE\\ConstantTime\\EncoderInterface' => $vendorDir . '/paragonie/constant_time_encoding/src/EncoderInterface.php',
    'ParagonIE\\ConstantTime\\Encoding' => $vendorDir . '/paragonie/constant_time_encoding/src/Encoding.php',
    'ParagonIE\\ConstantTime\\Hex' => $vendorDir . '/paragonie/constant_time_encoding/src/Hex.php',
    'ParagonIE\\ConstantTime\\RFC4648' => $vendorDir . '/paragonie/constant_time_encoding/src/RFC4648.php',
    'PayPalCheckoutSdk\\Core\\AccessToken' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/AccessToken.php',
    'PayPalCheckoutSdk\\Core\\AccessTokenRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/AccessTokenRequest.php',
    'PayPalCheckoutSdk\\Core\\AuthorizationInjector' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/AuthorizationInjector.php',
    'PayPalCheckoutSdk\\Core\\FPTIInstrumentationInjector' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/FPTIInstrumentationInjector.php',
    'PayPalCheckoutSdk\\Core\\GzipInjector' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/GzipInjector.php',
    'PayPalCheckoutSdk\\Core\\PayPalEnvironment' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/PayPalEnvironment.php',
    'PayPalCheckoutSdk\\Core\\PayPalHttpClient' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/PayPalHttpClient.php',
    'PayPalCheckoutSdk\\Core\\ProductionEnvironment' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/ProductionEnvironment.php',
    'PayPalCheckoutSdk\\Core\\RefreshTokenRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/RefreshTokenRequest.php',
    'PayPalCheckoutSdk\\Core\\SandboxEnvironment' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/SandboxEnvironment.php',
    'PayPalCheckoutSdk\\Core\\UserAgent' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/UserAgent.php',
    'PayPalCheckoutSdk\\Core\\Version' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Core/Version.php',
    'PayPalCheckoutSdk\\Orders\\OrdersAuthorizeRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersAuthorizeRequest.php',
    'PayPalCheckoutSdk\\Orders\\OrdersCaptureRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersCaptureRequest.php',
    'PayPalCheckoutSdk\\Orders\\OrdersCreateRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersCreateRequest.php',
    'PayPalCheckoutSdk\\Orders\\OrdersGetRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersGetRequest.php',
    'PayPalCheckoutSdk\\Orders\\OrdersPatchRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersPatchRequest.php',
    'PayPalCheckoutSdk\\Orders\\OrdersValidateRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Orders/OrdersValidateRequest.php',
    'PayPalCheckoutSdk\\Payments\\AuthorizationsCaptureRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/AuthorizationsCaptureRequest.php',
    'PayPalCheckoutSdk\\Payments\\AuthorizationsGetRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/AuthorizationsGetRequest.php',
    'PayPalCheckoutSdk\\Payments\\AuthorizationsReauthorizeRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/AuthorizationsReauthorizeRequest.php',
    'PayPalCheckoutSdk\\Payments\\AuthorizationsVoidRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/AuthorizationsVoidRequest.php',
    'PayPalCheckoutSdk\\Payments\\CapturesGetRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/CapturesGetRequest.php',
    'PayPalCheckoutSdk\\Payments\\CapturesRefundRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/CapturesRefundRequest.php',
    'PayPalCheckoutSdk\\Payments\\RefundsGetRequest' => $vendorDir . '/paypal/paypal-checkout-sdk/lib/PayPalCheckoutSdk/Payments/RefundsGetRequest.php',
    'PayPalHttp\\Curl' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Curl.php',
    'PayPalHttp\\Encoder' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Encoder.php',
    'PayPalHttp\\Environment' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Environment.php',
    'PayPalHttp\\HttpClient' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/HttpClient.php',
    'PayPalHttp\\HttpException' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/HttpException.php',
    'PayPalHttp\\HttpRequest' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/HttpRequest.php',
    'PayPalHttp\\HttpResponse' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/HttpResponse.php',
    'PayPalHttp\\IOException' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/IOException.php',
    'PayPalHttp\\Injector' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Injector.php',
    'PayPalHttp\\Serializer' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer.php',
    'PayPalHttp\\Serializer\\Form' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer/Form.php',
    'PayPalHttp\\Serializer\\FormPart' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer/FormPart.php',
    'PayPalHttp\\Serializer\\Json' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer/Json.php',
    'PayPalHttp\\Serializer\\Multipart' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer/Multipart.php',
    'PayPalHttp\\Serializer\\Text' => $vendorDir . '/paypal/paypalhttp/lib/PayPalHttp/Serializer/Text.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\Google2FA' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/Google2FA.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\IncompatibleWithGoogleAuthenticator' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/IncompatibleWithGoogleAuthenticator.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidAlgorithm' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidAlgorithm.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\InvalidCharacters' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/InvalidCharacters.php',
    'PragmaRX\\Google2FA\\Exceptions\\Contracts\\SecretKeyTooShort' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Contracts/SecretKeyTooShort.php',
    'PragmaRX\\Google2FA\\Exceptions\\Google2FAException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/Google2FAException.php',
    'PragmaRX\\Google2FA\\Exceptions\\IncompatibleWithGoogleAuthenticatorException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/IncompatibleWithGoogleAuthenticatorException.php',
    'PragmaRX\\Google2FA\\Exceptions\\InvalidAlgorithmException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/InvalidAlgorithmException.php',
    'PragmaRX\\Google2FA\\Exceptions\\InvalidCharactersException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/InvalidCharactersException.php',
    'PragmaRX\\Google2FA\\Exceptions\\SecretKeyTooShortException' => $vendorDir . '/pragmarx/google2fa/src/Exceptions/SecretKeyTooShortException.php',
    'PragmaRX\\Google2FA\\Google2FA' => $vendorDir . '/pragmarx/google2fa/src/Google2FA.php',
    'PragmaRX\\Google2FA\\Support\\Base32' => $vendorDir . '/pragmarx/google2fa/src/Support/Base32.php',
    'PragmaRX\\Google2FA\\Support\\Constants' => $vendorDir . '/pragmarx/google2fa/src/Support/Constants.php',
    'PragmaRX\\Google2FA\\Support\\QRCode' => $vendorDir . '/pragmarx/google2fa/src/Support/QRCode.php',
    'Sabberworm\\CSS\\CSSElement' => $vendorDir . '/sabberworm/php-css-parser/src/CSSElement.php',
    'Sabberworm\\CSS\\CSSList\\AtRuleBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/AtRuleBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSBlockList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSBlockList.php',
    'Sabberworm\\CSS\\CSSList\\CSSList' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/CSSList.php',
    'Sabberworm\\CSS\\CSSList\\Document' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/Document.php',
    'Sabberworm\\CSS\\CSSList\\KeyFrame' => $vendorDir . '/sabberworm/php-css-parser/src/CSSList/KeyFrame.php',
    'Sabberworm\\CSS\\Comment\\Comment' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Comment.php',
    'Sabberworm\\CSS\\Comment\\Commentable' => $vendorDir . '/sabberworm/php-css-parser/src/Comment/Commentable.php',
    'Sabberworm\\CSS\\OutputFormat' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormat.php',
    'Sabberworm\\CSS\\OutputFormatter' => $vendorDir . '/sabberworm/php-css-parser/src/OutputFormatter.php',
    'Sabberworm\\CSS\\Parser' => $vendorDir . '/sabberworm/php-css-parser/src/Parser.php',
    'Sabberworm\\CSS\\Parsing\\Anchor' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/Anchor.php',
    'Sabberworm\\CSS\\Parsing\\OutputException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/OutputException.php',
    'Sabberworm\\CSS\\Parsing\\ParserState' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/ParserState.php',
    'Sabberworm\\CSS\\Parsing\\SourceException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/SourceException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedEOFException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedEOFException.php',
    'Sabberworm\\CSS\\Parsing\\UnexpectedTokenException' => $vendorDir . '/sabberworm/php-css-parser/src/Parsing/UnexpectedTokenException.php',
    'Sabberworm\\CSS\\Position\\Position' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Position.php',
    'Sabberworm\\CSS\\Position\\Positionable' => $vendorDir . '/sabberworm/php-css-parser/src/Position/Positionable.php',
    'Sabberworm\\CSS\\Property\\AtRule' => $vendorDir . '/sabberworm/php-css-parser/src/Property/AtRule.php',
    'Sabberworm\\CSS\\Property\\CSSNamespace' => $vendorDir . '/sabberworm/php-css-parser/src/Property/CSSNamespace.php',
    'Sabberworm\\CSS\\Property\\Charset' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Charset.php',
    'Sabberworm\\CSS\\Property\\Import' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Import.php',
    'Sabberworm\\CSS\\Property\\KeyframeSelector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/KeyframeSelector.php',
    'Sabberworm\\CSS\\Property\\Selector' => $vendorDir . '/sabberworm/php-css-parser/src/Property/Selector.php',
    'Sabberworm\\CSS\\Renderable' => $vendorDir . '/sabberworm/php-css-parser/src/Renderable.php',
    'Sabberworm\\CSS\\RuleSet\\AtRuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/AtRuleSet.php',
    'Sabberworm\\CSS\\RuleSet\\DeclarationBlock' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/DeclarationBlock.php',
    'Sabberworm\\CSS\\RuleSet\\RuleSet' => $vendorDir . '/sabberworm/php-css-parser/src/RuleSet/RuleSet.php',
    'Sabberworm\\CSS\\Rule\\Rule' => $vendorDir . '/sabberworm/php-css-parser/src/Rule/Rule.php',
    'Sabberworm\\CSS\\Settings' => $vendorDir . '/sabberworm/php-css-parser/src/Settings.php',
    'Sabberworm\\CSS\\Value\\CSSFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSFunction.php',
    'Sabberworm\\CSS\\Value\\CSSString' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CSSString.php',
    'Sabberworm\\CSS\\Value\\CalcFunction' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcFunction.php',
    'Sabberworm\\CSS\\Value\\CalcRuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/CalcRuleValueList.php',
    'Sabberworm\\CSS\\Value\\Color' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Color.php',
    'Sabberworm\\CSS\\Value\\LineName' => $vendorDir . '/sabberworm/php-css-parser/src/Value/LineName.php',
    'Sabberworm\\CSS\\Value\\PrimitiveValue' => $vendorDir . '/sabberworm/php-css-parser/src/Value/PrimitiveValue.php',
    'Sabberworm\\CSS\\Value\\RuleValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/RuleValueList.php',
    'Sabberworm\\CSS\\Value\\Size' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Size.php',
    'Sabberworm\\CSS\\Value\\URL' => $vendorDir . '/sabberworm/php-css-parser/src/Value/URL.php',
    'Sabberworm\\CSS\\Value\\Value' => $vendorDir . '/sabberworm/php-css-parser/src/Value/Value.php',
    'Sabberworm\\CSS\\Value\\ValueList' => $vendorDir . '/sabberworm/php-css-parser/src/Value/ValueList.php',
    'Sample\\AuthorizeIntentExamples\\AuthorizeOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/AuthorizeIntentExamples/AuthorizeOrder.php',
    'Sample\\AuthorizeIntentExamples\\CaptureOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/AuthorizeIntentExamples/CaptureOrder.php',
    'Sample\\AuthorizeIntentExamples\\CreateOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/AuthorizeIntentExamples/CreateOrder.php',
    'Sample\\CaptureIntentExamples\\CaptureOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/CaptureIntentExamples/CaptureOrder.php',
    'Sample\\CaptureIntentExamples\\CreateOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/CaptureIntentExamples/CreateOrder.php',
    'Sample\\GetOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/GetOrder.php',
    'Sample\\PatchOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/PatchOrder.php',
    'Sample\\PayPalClient' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/PayPalClient.php',
    'Sample\\RefundOrder' => $vendorDir . '/paypal/paypal-checkout-sdk/samples/RefundOrder.php',
    'Stripe\\Account' => $vendorDir . '/stripe/stripe-php/lib/Account.php',
    'Stripe\\AccountLink' => $vendorDir . '/stripe/stripe-php/lib/AccountLink.php',
    'Stripe\\AccountSession' => $vendorDir . '/stripe/stripe-php/lib/AccountSession.php',
    'Stripe\\ApiOperations\\All' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/All.php',
    'Stripe\\ApiOperations\\Create' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Create.php',
    'Stripe\\ApiOperations\\Delete' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Delete.php',
    'Stripe\\ApiOperations\\NestedResource' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/NestedResource.php',
    'Stripe\\ApiOperations\\Request' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Request.php',
    'Stripe\\ApiOperations\\Retrieve' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Retrieve.php',
    'Stripe\\ApiOperations\\Search' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Search.php',
    'Stripe\\ApiOperations\\SingletonRetrieve' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/SingletonRetrieve.php',
    'Stripe\\ApiOperations\\Update' => $vendorDir . '/stripe/stripe-php/lib/ApiOperations/Update.php',
    'Stripe\\ApiRequestor' => $vendorDir . '/stripe/stripe-php/lib/ApiRequestor.php',
    'Stripe\\ApiResource' => $vendorDir . '/stripe/stripe-php/lib/ApiResource.php',
    'Stripe\\ApiResponse' => $vendorDir . '/stripe/stripe-php/lib/ApiResponse.php',
    'Stripe\\ApplePayDomain' => $vendorDir . '/stripe/stripe-php/lib/ApplePayDomain.php',
    'Stripe\\Application' => $vendorDir . '/stripe/stripe-php/lib/Application.php',
    'Stripe\\ApplicationFee' => $vendorDir . '/stripe/stripe-php/lib/ApplicationFee.php',
    'Stripe\\ApplicationFeeRefund' => $vendorDir . '/stripe/stripe-php/lib/ApplicationFeeRefund.php',
    'Stripe\\Apps\\Secret' => $vendorDir . '/stripe/stripe-php/lib/Apps/Secret.php',
    'Stripe\\Balance' => $vendorDir . '/stripe/stripe-php/lib/Balance.php',
    'Stripe\\BalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/BalanceTransaction.php',
    'Stripe\\BankAccount' => $vendorDir . '/stripe/stripe-php/lib/BankAccount.php',
    'Stripe\\BaseStripeClient' => $vendorDir . '/stripe/stripe-php/lib/BaseStripeClient.php',
    'Stripe\\BaseStripeClientInterface' => $vendorDir . '/stripe/stripe-php/lib/BaseStripeClientInterface.php',
    'Stripe\\BillingPortal\\Configuration' => $vendorDir . '/stripe/stripe-php/lib/BillingPortal/Configuration.php',
    'Stripe\\BillingPortal\\Session' => $vendorDir . '/stripe/stripe-php/lib/BillingPortal/Session.php',
    'Stripe\\Billing\\Alert' => $vendorDir . '/stripe/stripe-php/lib/Billing/Alert.php',
    'Stripe\\Billing\\AlertTriggered' => $vendorDir . '/stripe/stripe-php/lib/Billing/AlertTriggered.php',
    'Stripe\\Billing\\CreditBalanceSummary' => $vendorDir . '/stripe/stripe-php/lib/Billing/CreditBalanceSummary.php',
    'Stripe\\Billing\\CreditBalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/Billing/CreditBalanceTransaction.php',
    'Stripe\\Billing\\CreditGrant' => $vendorDir . '/stripe/stripe-php/lib/Billing/CreditGrant.php',
    'Stripe\\Billing\\Meter' => $vendorDir . '/stripe/stripe-php/lib/Billing/Meter.php',
    'Stripe\\Billing\\MeterEvent' => $vendorDir . '/stripe/stripe-php/lib/Billing/MeterEvent.php',
    'Stripe\\Billing\\MeterEventAdjustment' => $vendorDir . '/stripe/stripe-php/lib/Billing/MeterEventAdjustment.php',
    'Stripe\\Billing\\MeterEventSummary' => $vendorDir . '/stripe/stripe-php/lib/Billing/MeterEventSummary.php',
    'Stripe\\Capability' => $vendorDir . '/stripe/stripe-php/lib/Capability.php',
    'Stripe\\Card' => $vendorDir . '/stripe/stripe-php/lib/Card.php',
    'Stripe\\CashBalance' => $vendorDir . '/stripe/stripe-php/lib/CashBalance.php',
    'Stripe\\Charge' => $vendorDir . '/stripe/stripe-php/lib/Charge.php',
    'Stripe\\Checkout\\Session' => $vendorDir . '/stripe/stripe-php/lib/Checkout/Session.php',
    'Stripe\\Climate\\Order' => $vendorDir . '/stripe/stripe-php/lib/Climate/Order.php',
    'Stripe\\Climate\\Product' => $vendorDir . '/stripe/stripe-php/lib/Climate/Product.php',
    'Stripe\\Climate\\Supplier' => $vendorDir . '/stripe/stripe-php/lib/Climate/Supplier.php',
    'Stripe\\Collection' => $vendorDir . '/stripe/stripe-php/lib/Collection.php',
    'Stripe\\ConfirmationToken' => $vendorDir . '/stripe/stripe-php/lib/ConfirmationToken.php',
    'Stripe\\ConnectCollectionTransfer' => $vendorDir . '/stripe/stripe-php/lib/ConnectCollectionTransfer.php',
    'Stripe\\CountrySpec' => $vendorDir . '/stripe/stripe-php/lib/CountrySpec.php',
    'Stripe\\Coupon' => $vendorDir . '/stripe/stripe-php/lib/Coupon.php',
    'Stripe\\CreditNote' => $vendorDir . '/stripe/stripe-php/lib/CreditNote.php',
    'Stripe\\CreditNoteLineItem' => $vendorDir . '/stripe/stripe-php/lib/CreditNoteLineItem.php',
    'Stripe\\Customer' => $vendorDir . '/stripe/stripe-php/lib/Customer.php',
    'Stripe\\CustomerBalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/CustomerBalanceTransaction.php',
    'Stripe\\CustomerCashBalanceTransaction' => $vendorDir . '/stripe/stripe-php/lib/CustomerCashBalanceTransaction.php',
    'Stripe\\CustomerSession' => $vendorDir . '/stripe/stripe-php/lib/CustomerSession.php',
    'Stripe\\Discount' => $vendorDir . '/stripe/stripe-php/lib/Discount.php',
    'Stripe\\Dispute' => $vendorDir . '/stripe/stripe-php/lib/Dispute.php',
    'Stripe\\Entitlements\\ActiveEntitlement' => $vendorDir . '/stripe/stripe-php/lib/Entitlements/ActiveEntitlement.php',
    'Stripe\\Entitlements\\ActiveEntitlementSummary' => $vendorDir . '/stripe/stripe-php/lib/Entitlements/ActiveEntitlementSummary.php',
    'Stripe\\Entitlements\\Feature' => $vendorDir . '/stripe/stripe-php/lib/Entitlements/Feature.php',
    'Stripe\\EphemeralKey' => $vendorDir . '/stripe/stripe-php/lib/EphemeralKey.php',
    'Stripe\\ErrorObject' => $vendorDir . '/stripe/stripe-php/lib/ErrorObject.php',
    'Stripe\\Event' => $vendorDir . '/stripe/stripe-php/lib/Event.php',
    'Stripe\\EventData\\V1BillingMeterErrorReportTriggeredEventData' => $vendorDir . '/stripe/stripe-php/lib/EventData/V1BillingMeterErrorReportTriggeredEventData.php',
    'Stripe\\EventData\\V1BillingMeterNoMeterFoundEventData' => $vendorDir . '/stripe/stripe-php/lib/EventData/V1BillingMeterNoMeterFoundEventData.php',
    'Stripe\\Events\\V1BillingMeterErrorReportTriggeredEvent' => $vendorDir . '/stripe/stripe-php/lib/Events/V1BillingMeterErrorReportTriggeredEvent.php',
    'Stripe\\Events\\V1BillingMeterNoMeterFoundEvent' => $vendorDir . '/stripe/stripe-php/lib/Events/V1BillingMeterNoMeterFoundEvent.php',
    'Stripe\\Exception\\ApiConnectionException' => $vendorDir . '/stripe/stripe-php/lib/Exception/ApiConnectionException.php',
    'Stripe\\Exception\\ApiErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/ApiErrorException.php',
    'Stripe\\Exception\\AuthenticationException' => $vendorDir . '/stripe/stripe-php/lib/Exception/AuthenticationException.php',
    'Stripe\\Exception\\BadMethodCallException' => $vendorDir . '/stripe/stripe-php/lib/Exception/BadMethodCallException.php',
    'Stripe\\Exception\\CardException' => $vendorDir . '/stripe/stripe-php/lib/Exception/CardException.php',
    'Stripe\\Exception\\ExceptionInterface' => $vendorDir . '/stripe/stripe-php/lib/Exception/ExceptionInterface.php',
    'Stripe\\Exception\\IdempotencyException' => $vendorDir . '/stripe/stripe-php/lib/Exception/IdempotencyException.php',
    'Stripe\\Exception\\InvalidArgumentException' => $vendorDir . '/stripe/stripe-php/lib/Exception/InvalidArgumentException.php',
    'Stripe\\Exception\\InvalidRequestException' => $vendorDir . '/stripe/stripe-php/lib/Exception/InvalidRequestException.php',
    'Stripe\\Exception\\OAuth\\ExceptionInterface' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/ExceptionInterface.php',
    'Stripe\\Exception\\OAuth\\InvalidClientException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidClientException.php',
    'Stripe\\Exception\\OAuth\\InvalidGrantException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidGrantException.php',
    'Stripe\\Exception\\OAuth\\InvalidRequestException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidRequestException.php',
    'Stripe\\Exception\\OAuth\\InvalidScopeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/InvalidScopeException.php',
    'Stripe\\Exception\\OAuth\\OAuthErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/OAuthErrorException.php',
    'Stripe\\Exception\\OAuth\\UnknownOAuthErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnknownOAuthErrorException.php',
    'Stripe\\Exception\\OAuth\\UnsupportedGrantTypeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedGrantTypeException.php',
    'Stripe\\Exception\\OAuth\\UnsupportedResponseTypeException' => $vendorDir . '/stripe/stripe-php/lib/Exception/OAuth/UnsupportedResponseTypeException.php',
    'Stripe\\Exception\\PermissionException' => $vendorDir . '/stripe/stripe-php/lib/Exception/PermissionException.php',
    'Stripe\\Exception\\RateLimitException' => $vendorDir . '/stripe/stripe-php/lib/Exception/RateLimitException.php',
    'Stripe\\Exception\\SignatureVerificationException' => $vendorDir . '/stripe/stripe-php/lib/Exception/SignatureVerificationException.php',
    'Stripe\\Exception\\TemporarySessionExpiredException' => $vendorDir . '/stripe/stripe-php/lib/Exception/TemporarySessionExpiredException.php',
    'Stripe\\Exception\\UnexpectedValueException' => $vendorDir . '/stripe/stripe-php/lib/Exception/UnexpectedValueException.php',
    'Stripe\\Exception\\UnknownApiErrorException' => $vendorDir . '/stripe/stripe-php/lib/Exception/UnknownApiErrorException.php',
    'Stripe\\ExchangeRate' => $vendorDir . '/stripe/stripe-php/lib/ExchangeRate.php',
    'Stripe\\File' => $vendorDir . '/stripe/stripe-php/lib/File.php',
    'Stripe\\FileLink' => $vendorDir . '/stripe/stripe-php/lib/FileLink.php',
    'Stripe\\FinancialConnections\\Account' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/Account.php',
    'Stripe\\FinancialConnections\\AccountOwner' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/AccountOwner.php',
    'Stripe\\FinancialConnections\\AccountOwnership' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/AccountOwnership.php',
    'Stripe\\FinancialConnections\\Session' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/Session.php',
    'Stripe\\FinancialConnections\\Transaction' => $vendorDir . '/stripe/stripe-php/lib/FinancialConnections/Transaction.php',
    'Stripe\\Forwarding\\Request' => $vendorDir . '/stripe/stripe-php/lib/Forwarding/Request.php',
    'Stripe\\FundingInstructions' => $vendorDir . '/stripe/stripe-php/lib/FundingInstructions.php',
    'Stripe\\HttpClient\\ClientInterface' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/ClientInterface.php',
    'Stripe\\HttpClient\\CurlClient' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/CurlClient.php',
    'Stripe\\HttpClient\\StreamingClientInterface' => $vendorDir . '/stripe/stripe-php/lib/HttpClient/StreamingClientInterface.php',
    'Stripe\\Identity\\VerificationReport' => $vendorDir . '/stripe/stripe-php/lib/Identity/VerificationReport.php',
    'Stripe\\Identity\\VerificationSession' => $vendorDir . '/stripe/stripe-php/lib/Identity/VerificationSession.php',
    'Stripe\\Invoice' => $vendorDir . '/stripe/stripe-php/lib/Invoice.php',
    'Stripe\\InvoiceItem' => $vendorDir . '/stripe/stripe-php/lib/InvoiceItem.php',
    'Stripe\\InvoiceLineItem' => $vendorDir . '/stripe/stripe-php/lib/InvoiceLineItem.php',
    'Stripe\\InvoiceRenderingTemplate' => $vendorDir . '/stripe/stripe-php/lib/InvoiceRenderingTemplate.php',
    'Stripe\\Issuing\\Authorization' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Authorization.php',
    'Stripe\\Issuing\\Card' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Card.php',
    'Stripe\\Issuing\\CardDetails' => $vendorDir . '/stripe/stripe-php/lib/Issuing/CardDetails.php',
    'Stripe\\Issuing\\Cardholder' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Cardholder.php',
    'Stripe\\Issuing\\Dispute' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Dispute.php',
    'Stripe\\Issuing\\PersonalizationDesign' => $vendorDir . '/stripe/stripe-php/lib/Issuing/PersonalizationDesign.php',
    'Stripe\\Issuing\\PhysicalBundle' => $vendorDir . '/stripe/stripe-php/lib/Issuing/PhysicalBundle.php',
    'Stripe\\Issuing\\Token' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Token.php',
    'Stripe\\Issuing\\Transaction' => $vendorDir . '/stripe/stripe-php/lib/Issuing/Transaction.php',
    'Stripe\\LineItem' => $vendorDir . '/stripe/stripe-php/lib/LineItem.php',
    'Stripe\\LoginLink' => $vendorDir . '/stripe/stripe-php/lib/LoginLink.php',
    'Stripe\\Mandate' => $vendorDir . '/stripe/stripe-php/lib/Mandate.php',
    'Stripe\\OAuth' => $vendorDir . '/stripe/stripe-php/lib/OAuth.php',
    'Stripe\\OAuthErrorObject' => $vendorDir . '/stripe/stripe-php/lib/OAuthErrorObject.php',
    'Stripe\\PaymentIntent' => $vendorDir . '/stripe/stripe-php/lib/PaymentIntent.php',
    'Stripe\\PaymentLink' => $vendorDir . '/stripe/stripe-php/lib/PaymentLink.php',
    'Stripe\\PaymentMethod' => $vendorDir . '/stripe/stripe-php/lib/PaymentMethod.php',
    'Stripe\\PaymentMethodConfiguration' => $vendorDir . '/stripe/stripe-php/lib/PaymentMethodConfiguration.php',
    'Stripe\\PaymentMethodDomain' => $vendorDir . '/stripe/stripe-php/lib/PaymentMethodDomain.php',
    'Stripe\\Payout' => $vendorDir . '/stripe/stripe-php/lib/Payout.php',
    'Stripe\\Person' => $vendorDir . '/stripe/stripe-php/lib/Person.php',
    'Stripe\\Plan' => $vendorDir . '/stripe/stripe-php/lib/Plan.php',
    'Stripe\\Price' => $vendorDir . '/stripe/stripe-php/lib/Price.php',
    'Stripe\\Product' => $vendorDir . '/stripe/stripe-php/lib/Product.php',
    'Stripe\\ProductFeature' => $vendorDir . '/stripe/stripe-php/lib/ProductFeature.php',
    'Stripe\\PromotionCode' => $vendorDir . '/stripe/stripe-php/lib/PromotionCode.php',
    'Stripe\\Quote' => $vendorDir . '/stripe/stripe-php/lib/Quote.php',
    'Stripe\\Radar\\EarlyFraudWarning' => $vendorDir . '/stripe/stripe-php/lib/Radar/EarlyFraudWarning.php',
    'Stripe\\Radar\\ValueList' => $vendorDir . '/stripe/stripe-php/lib/Radar/ValueList.php',
    'Stripe\\Radar\\ValueListItem' => $vendorDir . '/stripe/stripe-php/lib/Radar/ValueListItem.php',
    'Stripe\\Reason' => $vendorDir . '/stripe/stripe-php/lib/Reason.php',
    'Stripe\\RecipientTransfer' => $vendorDir . '/stripe/stripe-php/lib/RecipientTransfer.php',
    'Stripe\\Refund' => $vendorDir . '/stripe/stripe-php/lib/Refund.php',
    'Stripe\\RelatedObject' => $vendorDir . '/stripe/stripe-php/lib/RelatedObject.php',
    'Stripe\\Reporting\\ReportRun' => $vendorDir . '/stripe/stripe-php/lib/Reporting/ReportRun.php',
    'Stripe\\Reporting\\ReportType' => $vendorDir . '/stripe/stripe-php/lib/Reporting/ReportType.php',
    'Stripe\\RequestTelemetry' => $vendorDir . '/stripe/stripe-php/lib/RequestTelemetry.php',
    'Stripe\\ReserveTransaction' => $vendorDir . '/stripe/stripe-php/lib/ReserveTransaction.php',
    'Stripe\\Review' => $vendorDir . '/stripe/stripe-php/lib/Review.php',
    'Stripe\\SearchResult' => $vendorDir . '/stripe/stripe-php/lib/SearchResult.php',
    'Stripe\\Service\\AbstractService' => $vendorDir . '/stripe/stripe-php/lib/Service/AbstractService.php',
    'Stripe\\Service\\AbstractServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/AbstractServiceFactory.php',
    'Stripe\\Service\\AccountLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/AccountLinkService.php',
    'Stripe\\Service\\AccountService' => $vendorDir . '/stripe/stripe-php/lib/Service/AccountService.php',
    'Stripe\\Service\\AccountSessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/AccountSessionService.php',
    'Stripe\\Service\\ApplePayDomainService' => $vendorDir . '/stripe/stripe-php/lib/Service/ApplePayDomainService.php',
    'Stripe\\Service\\ApplicationFeeService' => $vendorDir . '/stripe/stripe-php/lib/Service/ApplicationFeeService.php',
    'Stripe\\Service\\Apps\\AppsServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Apps/AppsServiceFactory.php',
    'Stripe\\Service\\Apps\\SecretService' => $vendorDir . '/stripe/stripe-php/lib/Service/Apps/SecretService.php',
    'Stripe\\Service\\BalanceService' => $vendorDir . '/stripe/stripe-php/lib/Service/BalanceService.php',
    'Stripe\\Service\\BalanceTransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/BalanceTransactionService.php',
    'Stripe\\Service\\BillingPortal\\BillingPortalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/BillingPortalServiceFactory.php',
    'Stripe\\Service\\BillingPortal\\ConfigurationService' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/ConfigurationService.php',
    'Stripe\\Service\\BillingPortal\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/BillingPortal/SessionService.php',
    'Stripe\\Service\\Billing\\AlertService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/AlertService.php',
    'Stripe\\Service\\Billing\\BillingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/BillingServiceFactory.php',
    'Stripe\\Service\\Billing\\CreditBalanceSummaryService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/CreditBalanceSummaryService.php',
    'Stripe\\Service\\Billing\\CreditBalanceTransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/CreditBalanceTransactionService.php',
    'Stripe\\Service\\Billing\\CreditGrantService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/CreditGrantService.php',
    'Stripe\\Service\\Billing\\MeterEventAdjustmentService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/MeterEventAdjustmentService.php',
    'Stripe\\Service\\Billing\\MeterEventService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/MeterEventService.php',
    'Stripe\\Service\\Billing\\MeterService' => $vendorDir . '/stripe/stripe-php/lib/Service/Billing/MeterService.php',
    'Stripe\\Service\\ChargeService' => $vendorDir . '/stripe/stripe-php/lib/Service/ChargeService.php',
    'Stripe\\Service\\Checkout\\CheckoutServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Checkout/CheckoutServiceFactory.php',
    'Stripe\\Service\\Checkout\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Checkout/SessionService.php',
    'Stripe\\Service\\Climate\\ClimateServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Climate/ClimateServiceFactory.php',
    'Stripe\\Service\\Climate\\OrderService' => $vendorDir . '/stripe/stripe-php/lib/Service/Climate/OrderService.php',
    'Stripe\\Service\\Climate\\ProductService' => $vendorDir . '/stripe/stripe-php/lib/Service/Climate/ProductService.php',
    'Stripe\\Service\\Climate\\SupplierService' => $vendorDir . '/stripe/stripe-php/lib/Service/Climate/SupplierService.php',
    'Stripe\\Service\\ConfirmationTokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/ConfirmationTokenService.php',
    'Stripe\\Service\\CoreServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/CoreServiceFactory.php',
    'Stripe\\Service\\CountrySpecService' => $vendorDir . '/stripe/stripe-php/lib/Service/CountrySpecService.php',
    'Stripe\\Service\\CouponService' => $vendorDir . '/stripe/stripe-php/lib/Service/CouponService.php',
    'Stripe\\Service\\CreditNoteService' => $vendorDir . '/stripe/stripe-php/lib/Service/CreditNoteService.php',
    'Stripe\\Service\\CustomerService' => $vendorDir . '/stripe/stripe-php/lib/Service/CustomerService.php',
    'Stripe\\Service\\CustomerSessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/CustomerSessionService.php',
    'Stripe\\Service\\DisputeService' => $vendorDir . '/stripe/stripe-php/lib/Service/DisputeService.php',
    'Stripe\\Service\\Entitlements\\ActiveEntitlementService' => $vendorDir . '/stripe/stripe-php/lib/Service/Entitlements/ActiveEntitlementService.php',
    'Stripe\\Service\\Entitlements\\EntitlementsServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Entitlements/EntitlementsServiceFactory.php',
    'Stripe\\Service\\Entitlements\\FeatureService' => $vendorDir . '/stripe/stripe-php/lib/Service/Entitlements/FeatureService.php',
    'Stripe\\Service\\EphemeralKeyService' => $vendorDir . '/stripe/stripe-php/lib/Service/EphemeralKeyService.php',
    'Stripe\\Service\\EventService' => $vendorDir . '/stripe/stripe-php/lib/Service/EventService.php',
    'Stripe\\Service\\ExchangeRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/ExchangeRateService.php',
    'Stripe\\Service\\FileLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/FileLinkService.php',
    'Stripe\\Service\\FileService' => $vendorDir . '/stripe/stripe-php/lib/Service/FileService.php',
    'Stripe\\Service\\FinancialConnections\\AccountService' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/AccountService.php',
    'Stripe\\Service\\FinancialConnections\\FinancialConnectionsServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/FinancialConnectionsServiceFactory.php',
    'Stripe\\Service\\FinancialConnections\\SessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/SessionService.php',
    'Stripe\\Service\\FinancialConnections\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/FinancialConnections/TransactionService.php',
    'Stripe\\Service\\Forwarding\\ForwardingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Forwarding/ForwardingServiceFactory.php',
    'Stripe\\Service\\Forwarding\\RequestService' => $vendorDir . '/stripe/stripe-php/lib/Service/Forwarding/RequestService.php',
    'Stripe\\Service\\Identity\\IdentityServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/IdentityServiceFactory.php',
    'Stripe\\Service\\Identity\\VerificationReportService' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/VerificationReportService.php',
    'Stripe\\Service\\Identity\\VerificationSessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Identity/VerificationSessionService.php',
    'Stripe\\Service\\InvoiceItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/InvoiceItemService.php',
    'Stripe\\Service\\InvoiceRenderingTemplateService' => $vendorDir . '/stripe/stripe-php/lib/Service/InvoiceRenderingTemplateService.php',
    'Stripe\\Service\\InvoiceService' => $vendorDir . '/stripe/stripe-php/lib/Service/InvoiceService.php',
    'Stripe\\Service\\Issuing\\AuthorizationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/AuthorizationService.php',
    'Stripe\\Service\\Issuing\\CardService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/CardService.php',
    'Stripe\\Service\\Issuing\\CardholderService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/CardholderService.php',
    'Stripe\\Service\\Issuing\\DisputeService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/DisputeService.php',
    'Stripe\\Service\\Issuing\\IssuingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/IssuingServiceFactory.php',
    'Stripe\\Service\\Issuing\\PersonalizationDesignService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/PersonalizationDesignService.php',
    'Stripe\\Service\\Issuing\\PhysicalBundleService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/PhysicalBundleService.php',
    'Stripe\\Service\\Issuing\\TokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/TokenService.php',
    'Stripe\\Service\\Issuing\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Issuing/TransactionService.php',
    'Stripe\\Service\\MandateService' => $vendorDir . '/stripe/stripe-php/lib/Service/MandateService.php',
    'Stripe\\Service\\OAuthService' => $vendorDir . '/stripe/stripe-php/lib/Service/OAuthService.php',
    'Stripe\\Service\\PaymentIntentService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentIntentService.php',
    'Stripe\\Service\\PaymentLinkService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentLinkService.php',
    'Stripe\\Service\\PaymentMethodConfigurationService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentMethodConfigurationService.php',
    'Stripe\\Service\\PaymentMethodDomainService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentMethodDomainService.php',
    'Stripe\\Service\\PaymentMethodService' => $vendorDir . '/stripe/stripe-php/lib/Service/PaymentMethodService.php',
    'Stripe\\Service\\PayoutService' => $vendorDir . '/stripe/stripe-php/lib/Service/PayoutService.php',
    'Stripe\\Service\\PlanService' => $vendorDir . '/stripe/stripe-php/lib/Service/PlanService.php',
    'Stripe\\Service\\PriceService' => $vendorDir . '/stripe/stripe-php/lib/Service/PriceService.php',
    'Stripe\\Service\\ProductService' => $vendorDir . '/stripe/stripe-php/lib/Service/ProductService.php',
    'Stripe\\Service\\PromotionCodeService' => $vendorDir . '/stripe/stripe-php/lib/Service/PromotionCodeService.php',
    'Stripe\\Service\\QuoteService' => $vendorDir . '/stripe/stripe-php/lib/Service/QuoteService.php',
    'Stripe\\Service\\Radar\\EarlyFraudWarningService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/EarlyFraudWarningService.php',
    'Stripe\\Service\\Radar\\RadarServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/RadarServiceFactory.php',
    'Stripe\\Service\\Radar\\ValueListItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/ValueListItemService.php',
    'Stripe\\Service\\Radar\\ValueListService' => $vendorDir . '/stripe/stripe-php/lib/Service/Radar/ValueListService.php',
    'Stripe\\Service\\RefundService' => $vendorDir . '/stripe/stripe-php/lib/Service/RefundService.php',
    'Stripe\\Service\\Reporting\\ReportRunService' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportRunService.php',
    'Stripe\\Service\\Reporting\\ReportTypeService' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportTypeService.php',
    'Stripe\\Service\\Reporting\\ReportingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Reporting/ReportingServiceFactory.php',
    'Stripe\\Service\\ReviewService' => $vendorDir . '/stripe/stripe-php/lib/Service/ReviewService.php',
    'Stripe\\Service\\ServiceNavigatorTrait' => $vendorDir . '/stripe/stripe-php/lib/Service/ServiceNavigatorTrait.php',
    'Stripe\\Service\\SetupAttemptService' => $vendorDir . '/stripe/stripe-php/lib/Service/SetupAttemptService.php',
    'Stripe\\Service\\SetupIntentService' => $vendorDir . '/stripe/stripe-php/lib/Service/SetupIntentService.php',
    'Stripe\\Service\\ShippingRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/ShippingRateService.php',
    'Stripe\\Service\\Sigma\\ScheduledQueryRunService' => $vendorDir . '/stripe/stripe-php/lib/Service/Sigma/ScheduledQueryRunService.php',
    'Stripe\\Service\\Sigma\\SigmaServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Sigma/SigmaServiceFactory.php',
    'Stripe\\Service\\SourceService' => $vendorDir . '/stripe/stripe-php/lib/Service/SourceService.php',
    'Stripe\\Service\\SubscriptionItemService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionItemService.php',
    'Stripe\\Service\\SubscriptionScheduleService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionScheduleService.php',
    'Stripe\\Service\\SubscriptionService' => $vendorDir . '/stripe/stripe-php/lib/Service/SubscriptionService.php',
    'Stripe\\Service\\TaxCodeService' => $vendorDir . '/stripe/stripe-php/lib/Service/TaxCodeService.php',
    'Stripe\\Service\\TaxIdService' => $vendorDir . '/stripe/stripe-php/lib/Service/TaxIdService.php',
    'Stripe\\Service\\TaxRateService' => $vendorDir . '/stripe/stripe-php/lib/Service/TaxRateService.php',
    'Stripe\\Service\\Tax\\CalculationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Tax/CalculationService.php',
    'Stripe\\Service\\Tax\\RegistrationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Tax/RegistrationService.php',
    'Stripe\\Service\\Tax\\SettingsService' => $vendorDir . '/stripe/stripe-php/lib/Service/Tax/SettingsService.php',
    'Stripe\\Service\\Tax\\TaxServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Tax/TaxServiceFactory.php',
    'Stripe\\Service\\Tax\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Tax/TransactionService.php',
    'Stripe\\Service\\Terminal\\ConfigurationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ConfigurationService.php',
    'Stripe\\Service\\Terminal\\ConnectionTokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ConnectionTokenService.php',
    'Stripe\\Service\\Terminal\\LocationService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/LocationService.php',
    'Stripe\\Service\\Terminal\\ReaderService' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/ReaderService.php',
    'Stripe\\Service\\Terminal\\TerminalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Terminal/TerminalServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\ConfirmationTokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/ConfirmationTokenService.php',
    'Stripe\\Service\\TestHelpers\\CustomerService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/CustomerService.php',
    'Stripe\\Service\\TestHelpers\\Issuing\\AuthorizationService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Issuing/AuthorizationService.php',
    'Stripe\\Service\\TestHelpers\\Issuing\\CardService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Issuing/CardService.php',
    'Stripe\\Service\\TestHelpers\\Issuing\\IssuingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Issuing/IssuingServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\Issuing\\PersonalizationDesignService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Issuing/PersonalizationDesignService.php',
    'Stripe\\Service\\TestHelpers\\Issuing\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Issuing/TransactionService.php',
    'Stripe\\Service\\TestHelpers\\RefundService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/RefundService.php',
    'Stripe\\Service\\TestHelpers\\Terminal\\ReaderService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/ReaderService.php',
    'Stripe\\Service\\TestHelpers\\Terminal\\TerminalServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Terminal/TerminalServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\TestClockService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/TestClockService.php',
    'Stripe\\Service\\TestHelpers\\TestHelpersServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/TestHelpersServiceFactory.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\InboundTransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/InboundTransferService.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\OutboundPaymentService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/OutboundPaymentService.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\OutboundTransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/OutboundTransferService.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\ReceivedCreditService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/ReceivedCreditService.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\ReceivedDebitService' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/ReceivedDebitService.php',
    'Stripe\\Service\\TestHelpers\\Treasury\\TreasuryServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/TestHelpers/Treasury/TreasuryServiceFactory.php',
    'Stripe\\Service\\TokenService' => $vendorDir . '/stripe/stripe-php/lib/Service/TokenService.php',
    'Stripe\\Service\\TopupService' => $vendorDir . '/stripe/stripe-php/lib/Service/TopupService.php',
    'Stripe\\Service\\TransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/TransferService.php',
    'Stripe\\Service\\Treasury\\CreditReversalService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/CreditReversalService.php',
    'Stripe\\Service\\Treasury\\DebitReversalService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/DebitReversalService.php',
    'Stripe\\Service\\Treasury\\FinancialAccountService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/FinancialAccountService.php',
    'Stripe\\Service\\Treasury\\InboundTransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/InboundTransferService.php',
    'Stripe\\Service\\Treasury\\OutboundPaymentService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/OutboundPaymentService.php',
    'Stripe\\Service\\Treasury\\OutboundTransferService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/OutboundTransferService.php',
    'Stripe\\Service\\Treasury\\ReceivedCreditService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/ReceivedCreditService.php',
    'Stripe\\Service\\Treasury\\ReceivedDebitService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/ReceivedDebitService.php',
    'Stripe\\Service\\Treasury\\TransactionEntryService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/TransactionEntryService.php',
    'Stripe\\Service\\Treasury\\TransactionService' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/TransactionService.php',
    'Stripe\\Service\\Treasury\\TreasuryServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/Treasury/TreasuryServiceFactory.php',
    'Stripe\\Service\\V2\\Billing\\BillingServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Billing/BillingServiceFactory.php',
    'Stripe\\Service\\V2\\Billing\\MeterEventAdjustmentService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Billing/MeterEventAdjustmentService.php',
    'Stripe\\Service\\V2\\Billing\\MeterEventService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Billing/MeterEventService.php',
    'Stripe\\Service\\V2\\Billing\\MeterEventSessionService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Billing/MeterEventSessionService.php',
    'Stripe\\Service\\V2\\Billing\\MeterEventStreamService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Billing/MeterEventStreamService.php',
    'Stripe\\Service\\V2\\Core\\CoreServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Core/CoreServiceFactory.php',
    'Stripe\\Service\\V2\\Core\\EventDestinationService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Core/EventDestinationService.php',
    'Stripe\\Service\\V2\\Core\\EventService' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/Core/EventService.php',
    'Stripe\\Service\\V2\\V2ServiceFactory' => $vendorDir . '/stripe/stripe-php/lib/Service/V2/V2ServiceFactory.php',
    'Stripe\\Service\\WebhookEndpointService' => $vendorDir . '/stripe/stripe-php/lib/Service/WebhookEndpointService.php',
    'Stripe\\SetupAttempt' => $vendorDir . '/stripe/stripe-php/lib/SetupAttempt.php',
    'Stripe\\SetupIntent' => $vendorDir . '/stripe/stripe-php/lib/SetupIntent.php',
    'Stripe\\ShippingRate' => $vendorDir . '/stripe/stripe-php/lib/ShippingRate.php',
    'Stripe\\Sigma\\ScheduledQueryRun' => $vendorDir . '/stripe/stripe-php/lib/Sigma/ScheduledQueryRun.php',
    'Stripe\\SingletonApiResource' => $vendorDir . '/stripe/stripe-php/lib/SingletonApiResource.php',
    'Stripe\\Source' => $vendorDir . '/stripe/stripe-php/lib/Source.php',
    'Stripe\\SourceMandateNotification' => $vendorDir . '/stripe/stripe-php/lib/SourceMandateNotification.php',
    'Stripe\\SourceTransaction' => $vendorDir . '/stripe/stripe-php/lib/SourceTransaction.php',
    'Stripe\\Stripe' => $vendorDir . '/stripe/stripe-php/lib/Stripe.php',
    'Stripe\\StripeClient' => $vendorDir . '/stripe/stripe-php/lib/StripeClient.php',
    'Stripe\\StripeClientInterface' => $vendorDir . '/stripe/stripe-php/lib/StripeClientInterface.php',
    'Stripe\\StripeObject' => $vendorDir . '/stripe/stripe-php/lib/StripeObject.php',
    'Stripe\\StripeStreamingClientInterface' => $vendorDir . '/stripe/stripe-php/lib/StripeStreamingClientInterface.php',
    'Stripe\\Subscription' => $vendorDir . '/stripe/stripe-php/lib/Subscription.php',
    'Stripe\\SubscriptionItem' => $vendorDir . '/stripe/stripe-php/lib/SubscriptionItem.php',
    'Stripe\\SubscriptionSchedule' => $vendorDir . '/stripe/stripe-php/lib/SubscriptionSchedule.php',
    'Stripe\\TaxCode' => $vendorDir . '/stripe/stripe-php/lib/TaxCode.php',
    'Stripe\\TaxDeductedAtSource' => $vendorDir . '/stripe/stripe-php/lib/TaxDeductedAtSource.php',
    'Stripe\\TaxId' => $vendorDir . '/stripe/stripe-php/lib/TaxId.php',
    'Stripe\\TaxRate' => $vendorDir . '/stripe/stripe-php/lib/TaxRate.php',
    'Stripe\\Tax\\Calculation' => $vendorDir . '/stripe/stripe-php/lib/Tax/Calculation.php',
    'Stripe\\Tax\\CalculationLineItem' => $vendorDir . '/stripe/stripe-php/lib/Tax/CalculationLineItem.php',
    'Stripe\\Tax\\Registration' => $vendorDir . '/stripe/stripe-php/lib/Tax/Registration.php',
    'Stripe\\Tax\\Settings' => $vendorDir . '/stripe/stripe-php/lib/Tax/Settings.php',
    'Stripe\\Tax\\Transaction' => $vendorDir . '/stripe/stripe-php/lib/Tax/Transaction.php',
    'Stripe\\Tax\\TransactionLineItem' => $vendorDir . '/stripe/stripe-php/lib/Tax/TransactionLineItem.php',
    'Stripe\\Terminal\\Configuration' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Configuration.php',
    'Stripe\\Terminal\\ConnectionToken' => $vendorDir . '/stripe/stripe-php/lib/Terminal/ConnectionToken.php',
    'Stripe\\Terminal\\Location' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Location.php',
    'Stripe\\Terminal\\Reader' => $vendorDir . '/stripe/stripe-php/lib/Terminal/Reader.php',
    'Stripe\\TestHelpers\\TestClock' => $vendorDir . '/stripe/stripe-php/lib/TestHelpers/TestClock.php',
    'Stripe\\ThinEvent' => $vendorDir . '/stripe/stripe-php/lib/ThinEvent.php',
    'Stripe\\Token' => $vendorDir . '/stripe/stripe-php/lib/Token.php',
    'Stripe\\Topup' => $vendorDir . '/stripe/stripe-php/lib/Topup.php',
    'Stripe\\Transfer' => $vendorDir . '/stripe/stripe-php/lib/Transfer.php',
    'Stripe\\TransferReversal' => $vendorDir . '/stripe/stripe-php/lib/TransferReversal.php',
    'Stripe\\Treasury\\CreditReversal' => $vendorDir . '/stripe/stripe-php/lib/Treasury/CreditReversal.php',
    'Stripe\\Treasury\\DebitReversal' => $vendorDir . '/stripe/stripe-php/lib/Treasury/DebitReversal.php',
    'Stripe\\Treasury\\FinancialAccount' => $vendorDir . '/stripe/stripe-php/lib/Treasury/FinancialAccount.php',
    'Stripe\\Treasury\\FinancialAccountFeatures' => $vendorDir . '/stripe/stripe-php/lib/Treasury/FinancialAccountFeatures.php',
    'Stripe\\Treasury\\InboundTransfer' => $vendorDir . '/stripe/stripe-php/lib/Treasury/InboundTransfer.php',
    'Stripe\\Treasury\\OutboundPayment' => $vendorDir . '/stripe/stripe-php/lib/Treasury/OutboundPayment.php',
    'Stripe\\Treasury\\OutboundTransfer' => $vendorDir . '/stripe/stripe-php/lib/Treasury/OutboundTransfer.php',
    'Stripe\\Treasury\\ReceivedCredit' => $vendorDir . '/stripe/stripe-php/lib/Treasury/ReceivedCredit.php',
    'Stripe\\Treasury\\ReceivedDebit' => $vendorDir . '/stripe/stripe-php/lib/Treasury/ReceivedDebit.php',
    'Stripe\\Treasury\\Transaction' => $vendorDir . '/stripe/stripe-php/lib/Treasury/Transaction.php',
    'Stripe\\Treasury\\TransactionEntry' => $vendorDir . '/stripe/stripe-php/lib/Treasury/TransactionEntry.php',
    'Stripe\\UsageRecord' => $vendorDir . '/stripe/stripe-php/lib/UsageRecord.php',
    'Stripe\\UsageRecordSummary' => $vendorDir . '/stripe/stripe-php/lib/UsageRecordSummary.php',
    'Stripe\\Util\\ApiVersion' => $vendorDir . '/stripe/stripe-php/lib/Util/ApiVersion.php',
    'Stripe\\Util\\CaseInsensitiveArray' => $vendorDir . '/stripe/stripe-php/lib/Util/CaseInsensitiveArray.php',
    'Stripe\\Util\\DefaultLogger' => $vendorDir . '/stripe/stripe-php/lib/Util/DefaultLogger.php',
    'Stripe\\Util\\EventTypes' => $vendorDir . '/stripe/stripe-php/lib/Util/EventTypes.php',
    'Stripe\\Util\\LoggerInterface' => $vendorDir . '/stripe/stripe-php/lib/Util/LoggerInterface.php',
    'Stripe\\Util\\ObjectTypes' => $vendorDir . '/stripe/stripe-php/lib/Util/ObjectTypes.php',
    'Stripe\\Util\\RandomGenerator' => $vendorDir . '/stripe/stripe-php/lib/Util/RandomGenerator.php',
    'Stripe\\Util\\RequestOptions' => $vendorDir . '/stripe/stripe-php/lib/Util/RequestOptions.php',
    'Stripe\\Util\\Set' => $vendorDir . '/stripe/stripe-php/lib/Util/Set.php',
    'Stripe\\Util\\Util' => $vendorDir . '/stripe/stripe-php/lib/Util/Util.php',
    'Stripe\\V2\\Billing\\MeterEvent' => $vendorDir . '/stripe/stripe-php/lib/V2/Billing/MeterEvent.php',
    'Stripe\\V2\\Billing\\MeterEventAdjustment' => $vendorDir . '/stripe/stripe-php/lib/V2/Billing/MeterEventAdjustment.php',
    'Stripe\\V2\\Billing\\MeterEventSession' => $vendorDir . '/stripe/stripe-php/lib/V2/Billing/MeterEventSession.php',
    'Stripe\\V2\\Collection' => $vendorDir . '/stripe/stripe-php/lib/V2/Collection.php',
    'Stripe\\V2\\Event' => $vendorDir . '/stripe/stripe-php/lib/V2/Event.php',
    'Stripe\\V2\\EventDestination' => $vendorDir . '/stripe/stripe-php/lib/V2/EventDestination.php',
    'Stripe\\Webhook' => $vendorDir . '/stripe/stripe-php/lib/Webhook.php',
    'Stripe\\WebhookEndpoint' => $vendorDir . '/stripe/stripe-php/lib/WebhookEndpoint.php',
    'Stripe\\WebhookSignature' => $vendorDir . '/stripe/stripe-php/lib/WebhookSignature.php',
    'Svg\\CssLength' => $vendorDir . '/phenx/php-svg-lib/src/Svg/CssLength.php',
    'Svg\\DefaultStyle' => $vendorDir . '/phenx/php-svg-lib/src/Svg/DefaultStyle.php',
    'Svg\\Document' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Document.php',
    'Svg\\Gradient\\Stop' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Gradient/Stop.php',
    'Svg\\Style' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Style.php',
    'Svg\\Surface\\CPdf' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/CPdf.php',
    'Svg\\Surface\\SurfaceCpdf' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfaceCpdf.php',
    'Svg\\Surface\\SurfaceInterface' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfaceInterface.php',
    'Svg\\Surface\\SurfacePDFLib' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Surface/SurfacePDFLib.php',
    'Svg\\Tag\\AbstractTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/AbstractTag.php',
    'Svg\\Tag\\Anchor' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Anchor.php',
    'Svg\\Tag\\Circle' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Circle.php',
    'Svg\\Tag\\ClipPath' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/ClipPath.php',
    'Svg\\Tag\\Ellipse' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Ellipse.php',
    'Svg\\Tag\\Group' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Group.php',
    'Svg\\Tag\\Image' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Image.php',
    'Svg\\Tag\\Line' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Line.php',
    'Svg\\Tag\\LinearGradient' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/LinearGradient.php',
    'Svg\\Tag\\Path' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Path.php',
    'Svg\\Tag\\Polygon' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Polygon.php',
    'Svg\\Tag\\Polyline' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Polyline.php',
    'Svg\\Tag\\RadialGradient' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/RadialGradient.php',
    'Svg\\Tag\\Rect' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Rect.php',
    'Svg\\Tag\\Shape' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Shape.php',
    'Svg\\Tag\\Stop' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Stop.php',
    'Svg\\Tag\\StyleTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/StyleTag.php',
    'Svg\\Tag\\Symbol' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Symbol.php',
    'Svg\\Tag\\Text' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/Text.php',
    'Svg\\Tag\\UseTag' => $vendorDir . '/phenx/php-svg-lib/src/Svg/Tag/UseTag.php',
);
