<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once __DIR__ . '/../config.php';

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'setup_guest':
            handleSetupGuest();
            break;
        case 'setup_staff':
            handleSetupStaff();
            break;
        case 'generate_qr':
            handleGenerateQR();
            break;
        case 'scan_qr':
            handleScanQR();
            break;
        case 'checkin':
            handleCheckin();
            break;
        case 'get_stats':
            handleGetStats();
            break;
        case 'test_role_access':
            handleTestRoleAccess();
            break;
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleSetupGuest() {
    global $pdo;
    
    // Create or update test guest
    $guest_email = '<EMAIL>';
    $guest_name = 'Test Guest User';
    $event_id = 1; // Using the first event from our data check
    
    // Check if guest RSVP already exists
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps_guests WHERE event_id = ? AND guest_email = ?");
    $stmt->execute([$event_id, $guest_email]);
    $existing = $stmt->fetch();
    
    if (!$existing) {
        // Create guest RSVP
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps_guests (event_id, guest_name, guest_email, status, party_size)
            VALUES (?, ?, ?, 'attending', 1)
        ");
        $stmt->execute([$event_id, $guest_name, $guest_email]);
        $guest_id = $pdo->lastInsertId();
    } else {
        $guest_id = $existing['id'];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Guest user setup completed',
        'guest_id' => $guest_id,
        'guest_name' => $guest_name,
        'guest_email' => $guest_email,
        'event_id' => $event_id
    ]);
}

function handleSetupStaff() {
    global $pdo;
    
    // Create test staff members if they don't exist
    $staff_members = [
        ['name' => 'Sarah Staff', 'email' => '<EMAIL>', 'role' => 'staff'],
        ['name' => 'Mike Coordinator', 'email' => '<EMAIL>', 'role' => 'coordinator'],
        ['name' => 'Lisa Organizer', 'email' => '<EMAIL>', 'role' => 'organizer']
    ];
    
    $created_staff = [];
    
    foreach ($staff_members as $staff) {
        // Check if member exists
        $stmt = $pdo->prepare("SELECT id FROM members WHERE email = ?");
        $stmt->execute([$staff['email']]);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            // Create member (note: members table might not have role column)
            $stmt = $pdo->prepare("INSERT INTO members (full_name, email, is_active) VALUES (?, ?, 1)");
            $stmt->execute([$staff['name'], $staff['email']]);
            $member_id = $pdo->lastInsertId();
        } else {
            $member_id = $existing['id'];
        }
        
        $created_staff[] = [
            'id' => $member_id,
            'name' => $staff['name'],
            'email' => $staff['email'],
            'role' => $staff['role']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Staff users setup completed',
        'staff' => $created_staff
    ]);
}

function handleGenerateQR() {
    global $pdo;
    
    $guest_email = '<EMAIL>';
    $event_id = 1;
    
    // Get guest info
    $stmt = $pdo->prepare("SELECT * FROM event_rsvps_guests WHERE event_id = ? AND guest_email = ?");
    $stmt->execute([$event_id, $guest_email]);
    $guest = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$guest) {
        throw new Exception('Guest not found. Please setup guest first.');
    }
    
    // Check if QR code already exists
    $stmt = $pdo->prepare("SELECT * FROM member_qr_codes WHERE event_id = ? AND guest_email = ?");
    $stmt->execute([$event_id, $guest_email]);
    $existing_qr = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing_qr) {
        $qr_token = $existing_qr['qr_token'];
    } else {
        // Generate new QR code
        $qr_token = 'QR_' . $event_id . '_' . bin2hex(random_bytes(16));
        
        $stmt = $pdo->prepare("
            INSERT INTO member_qr_codes 
            (event_id, member_id, guest_email, qr_token, attendee_name, attendee_email, attendee_type)
            VALUES (?, NULL, ?, ?, ?, ?, 'guest')
        ");
        $stmt->execute([
            $event_id,
            $guest_email,
            $qr_token,
            $guest['guest_name'],
            $guest['guest_email']
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'QR code generated successfully',
        'qr_token' => $qr_token,
        'event_id' => $event_id,
        'attendee_name' => $guest['guest_name'],
        'attendee_email' => $guest['guest_email'],
        'attendee_type' => 'guest',
        'created_at' => date('Y-m-d H:i:s')
    ]);
}

function handleScanQR() {
    global $pdo;
    
    $qr_token = $_POST['qr_token'] ?? '';
    
    if (empty($qr_token)) {
        throw new Exception('QR token is required');
    }
    
    // Look up QR code
    $stmt = $pdo->prepare("SELECT * FROM member_qr_codes WHERE qr_token = ?");
    $stmt->execute([$qr_token]);
    $qr_code = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$qr_code) {
        throw new Exception('Invalid QR code');
    }
    
    if ($qr_code['is_used']) {
        throw new Exception('QR code already used');
    }
    
    // Get event info
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$qr_code['event_id']]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'QR code scanned successfully',
        'attendee_name' => $qr_code['attendee_name'],
        'attendee_email' => $qr_code['attendee_email'],
        'attendee_type' => $qr_code['attendee_type'],
        'event_title' => $event['title'],
        'event_date' => $event['event_date'],
        'qr_token' => $qr_token,
        'can_checkin' => true
    ]);
}

function handleCheckin() {
    global $pdo;
    
    $qr_token = $_POST['qr_token'] ?? '';
    $staff_id = $_POST['staff_id'] ?? 1; // Default to first staff member
    
    if (empty($qr_token)) {
        throw new Exception('QR token is required');
    }
    
    // Get QR code info
    $stmt = $pdo->prepare("SELECT * FROM member_qr_codes WHERE qr_token = ?");
    $stmt->execute([$qr_token]);
    $qr_code = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$qr_code) {
        throw new Exception('Invalid QR code');
    }
    
    if ($qr_code['is_used']) {
        throw new Exception('Already checked in');
    }
    
    // Mark QR code as used
    $stmt = $pdo->prepare("UPDATE member_qr_codes SET is_used = 1, used_at = NOW() WHERE qr_token = ?");
    $stmt->execute([$qr_token]);
    
    // Create attendance record for sessions if applicable
    $stmt = $pdo->prepare("SELECT id FROM event_sessions WHERE event_id = ? AND status = 'active'");
    $stmt->execute([$qr_code['event_id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sessions as $session) {
        // Check if attendance record exists
        $stmt = $pdo->prepare("SELECT id FROM session_attendance WHERE session_id = ? AND guest_email = ?");
        $stmt->execute([$session['id'], $qr_code['attendee_email']]);
        $existing = $stmt->fetch();
        
        if (!$existing) {
            // Create attendance record
            $stmt = $pdo->prepare("
                INSERT INTO session_attendance 
                (session_id, member_id, guest_name, guest_email, attendance_status, attendance_date)
                VALUES (?, ?, ?, ?, 'attended', NOW())
            ");
            $stmt->execute([
                $session['id'],
                $qr_code['member_id'],
                $qr_code['attendee_name'],
                $qr_code['attendee_email']
            ]);
        } else {
            // Update existing record
            $stmt = $pdo->prepare("
                UPDATE session_attendance 
                SET attendance_status = 'attended', attendance_date = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$existing['id']]);
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Check-in completed successfully',
        'attendee_name' => $qr_code['attendee_name'],
        'checkin_time' => date('Y-m-d H:i:s'),
        'sessions_updated' => count($sessions)
    ]);
}

function handleGetStats() {
    global $pdo;
    
    $event_id = $_GET['event_id'] ?? 1;
    
    // Get total registered
    $stmt = $pdo->prepare("
        SELECT 
            (SELECT COUNT(*) FROM event_rsvps WHERE event_id = ?) +
            (SELECT COUNT(*) FROM event_rsvps_guests WHERE event_id = ?) as total_registered
    ");
    $stmt->execute([$event_id, $event_id]);
    $total_registered = $stmt->fetch(PDO::FETCH_COLUMN);
    
    // Get checked in count
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM member_qr_codes WHERE event_id = ? AND is_used = 1");
    $stmt->execute([$event_id]);
    $checked_in = $stmt->fetch(PDO::FETCH_COLUMN);
    
    // Get recent check-ins
    $stmt = $pdo->prepare("
        SELECT attendee_name, attendee_email, used_at 
        FROM member_qr_codes 
        WHERE event_id = ? AND is_used = 1 
        ORDER BY used_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$event_id]);
    $recent_checkins = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'stats' => [
            'total_registered' => $total_registered,
            'checked_in' => $checked_in,
            'pending' => $total_registered - $checked_in,
            'recent_checkins' => $recent_checkins
        ]
    ]);
}

function handleTestRoleAccess() {
    global $pdo;
    
    // Simulate role access testing
    $roles = ['staff', 'coordinator', 'organizer'];
    $test_results = [];
    
    foreach ($roles as $role) {
        // Simulate permission checks
        $permissions = [
            'qr_scanner' => true,
            'checkin_management' => true,
            'event_management' => in_array($role, ['coordinator', 'organizer']),
            'reports_access' => $role !== 'staff' ? 'full' : 'limited'
        ];
        
        $test_results[$role] = [
            'permissions' => $permissions,
            'test_passed' => true,
            'test_time' => date('Y-m-d H:i:s')
        ];
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Role access testing completed',
        'results' => $test_results
    ]);
}
?>
