<?php
require_once '../config.php';

echo "<h2>Banner Fix Test</h2>";
echo "<style>
.test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
</style>";

// Test the improved banner query
$sql = "
    SELECT e.id, e.title, ef.event_id, ef.file_path, ef.file_type, ef.file_name, ef.id as file_id
    FROM events e 
    LEFT JOIN event_files ef ON e.id = ef.event_id AND ef.is_header_banner = 1
    WHERE e.title LIKE '%Sunday%' OR e.title LIKE '%Emmanuel%'
    ORDER BY e.id, ef.id DESC
";

$stmt = $conn->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Improved Banner Query Results:</h3>";
foreach ($results as $result) {
    echo "<div class='test-result " . ($result['file_path'] ? 'success' : 'warning') . "'>";
    echo "<strong>Event:</strong> " . htmlspecialchars($result['title']) . " (ID: {$result['id']})<br>";
    
    if ($result['file_path']) {
        echo "<strong>Banner Found:</strong> " . htmlspecialchars($result['file_name']) . "<br>";
        echo "<strong>File Path:</strong> " . htmlspecialchars($result['file_path']) . "<br>";
        
        // Test the improved path logic
        $dbBannerPath = $result['file_path'];
        $banner_path = admin_file_path($dbBannerPath);
        $thumbnail_path = str_replace('/promotional/', '/thumbnails/', dirname($banner_path)) . '/thumb_' . basename($banner_path);
        
        $serverThumbnailPath = __DIR__ . '/' . $thumbnail_path;
        $serverBannerPath = __DIR__ . '/' . $banner_path;
        
        echo "<strong>Converted Path:</strong> " . htmlspecialchars($banner_path) . "<br>";
        echo "<strong>Thumbnail Path:</strong> " . htmlspecialchars($thumbnail_path) . "<br>";
        
        // Test improved fallback logic
        $display_path = null;
        $path_source = '';
        
        if (file_exists($serverThumbnailPath)) {
            $display_path = $thumbnail_path;
            $path_source = 'thumbnail';
        } elseif (file_exists($serverBannerPath)) {
            $display_path = $banner_path;
            $path_source = 'banner';
        } else {
            // Try alternative path formats
            $alt_banner_path = '../uploads/events/promotional/' . basename($banner_path);
            $alt_server_path = __DIR__ . '/' . $alt_banner_path;
            if (file_exists($alt_server_path)) {
                $display_path = $alt_banner_path;
                $path_source = 'alternative';
            } else {
                $display_path = $banner_path;
                $path_source = 'fallback (may not exist)';
            }
        }
        
        echo "<strong>Display Path:</strong> " . htmlspecialchars($display_path) . " (from: $path_source)<br>";
        
        // Test if final path exists
        $final_server_path = __DIR__ . '/' . $display_path;
        if (file_exists($final_server_path)) {
            echo "<span style='color: green; font-weight: bold;'>✓ Final file exists and should display</span><br>";
            echo "<img src='" . htmlspecialchars($display_path) . "' style='max-width: 100px; max-height: 60px; margin-top: 10px;' alt='Banner preview'>";
        } else {
            echo "<span style='color: red; font-weight: bold;'>✗ Final file missing: " . htmlspecialchars($final_server_path) . "</span><br>";
        }
    } else {
        echo "<span style='color: orange;'>No banner file found for this event</span>";
    }
    echo "</div>";
}

// Test directory structure
echo "<h3>Directory Structure Check:</h3>";
$dirs_to_check = [
    '../uploads/events',
    '../uploads/events/promotional', 
    '../uploads/events/thumbnails'
];

foreach ($dirs_to_check as $dir) {
    $server_dir = __DIR__ . '/' . $dir;
    echo "<div class='test-result " . (is_dir($server_dir) ? 'success' : 'error') . "'>";
    echo "<strong>Directory:</strong> " . htmlspecialchars($dir) . "<br>";
    echo "<strong>Server Path:</strong> " . htmlspecialchars($server_dir) . "<br>";
    
    if (is_dir($server_dir)) {
        $files = array_diff(scandir($server_dir), ['.', '..']);
        echo "<strong>Files:</strong> " . count($files) . "<br>";
        if (count($files) > 0) {
            echo "<strong>Sample files:</strong><br>";
            $count = 0;
            foreach ($files as $file) {
                if ($count < 3) {
                    echo "• " . htmlspecialchars($file) . "<br>";
                    $count++;
                }
            }
        }
    } else {
        echo "<span style='color: red;'>Directory does not exist!</span>";
    }
    echo "</div>";
}
?>
