<?php
/**
 * User RSVP Handler
 * 
 * Handles RSVP submissions from authenticated users
 */

session_start();

// Include configuration and classes
require_once '../config.php';
require_once '../classes/SecurityManager.php';
require_once '../classes/UserAuthManager.php';
require_once '../includes/QRCodeEmailService.php';
require_once '../includes/admin_email_notifications.php';

// Initialize managers
$security = new SecurityManager($pdo);
$userAuth = new UserAuthManager($pdo, $security);

// Set security headers
$security->setSecurityHeaders();

// Check if user is authenticated
if (!$userAuth->isAuthenticated()) {
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit();
}

$userId = $_SESSION['user_id'];

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

// Note: CSRF token validation temporarily disabled for testing
// TODO: Implement CSRF token in the form

// Get and validate input
$eventId = filter_input(INPUT_POST, 'event_id', FILTER_VALIDATE_INT);
$status = isset($_POST['status']) ? trim($_POST['status']) : '';
$notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';

if (!$eventId || !$status) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit();
}

// Validate status
$validStatuses = ['attending', 'maybe', 'not_attending'];
if (!in_array($status, $validStatuses)) {
    echo json_encode(['success' => false, 'message' => 'Invalid RSVP status']);
    exit();
}

try {
    // Check if event exists and is active (try both is_active and status columns)
    try {
        // First try with is_active column
        $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND is_active = 1");
        $stmt->execute([$eventId]);
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // If is_active column doesn't exist, try with status column
        try {
            $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ? AND status = 'published'");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e2) {
            // If neither column exists, just check if event exists
            $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
            $stmt->execute([$eventId]);
            $event = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    if (!$event) {
        echo json_encode(['success' => false, 'message' => 'Event not found']);
        exit();
    }
    
    // Check if event is in the future (event_date is already a datetime)
    if (strtotime($event['event_date']) < time()) {
        echo json_encode(['success' => false, 'message' => 'Cannot RSVP to past events']);
        exit();
    }
    
    // Check if user already has an RSVP for this event
    $stmt = $pdo->prepare("SELECT id FROM event_rsvps WHERE event_id = ? AND user_id = ?");
    $stmt->execute([$eventId, $userId]);
    $existingRsvp = $stmt->fetch();

    if ($existingRsvp) {
        // Update existing RSVP
        $stmt = $pdo->prepare("
            UPDATE event_rsvps
            SET status = ?, notes = ?, updated_at = NOW()
            WHERE event_id = ? AND user_id = ?
        ");
        $stmt->execute([$status, $notes, $eventId, $userId]);
        $message = 'RSVP updated successfully!';

        // Send admin notification for RSVP update
        try {
            $rsvpData = [
                'name' => $userData['full_name'],
                'email' => $userData['email'],
                'phone' => $userData['phone'] ?? '',
                'status' => $status,
                'party_size' => 1,
                'special_requirements' => $notes
            ];
            sendRSVPNotificationToAdmins($rsvpData, $eventId);
        } catch (Exception $e) {
            error_log("Failed to send RSVP admin notification: " . $e->getMessage());
        }

        // Generate and send QR code email for attending members
        if ($status === 'attending') {
            $qrService = new QRCodeEmailService($pdo);
            $qrEmailSent = $qrService->generateAndSendEventQRCode($eventId, $userId);

            if (!$qrEmailSent) {
                error_log("Failed to send QR code email to member: $userId for event: $eventId");
            }
        }

        // Send basic confirmation email to member (like guests receive)
        sendRSVPConfirmationEmail($userData['email'], $userData['full_name'], $eventData, $status);
    } else {
        // Check capacity for new attendees
        if ($status === 'attending' && $event['max_attendees']) {
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as attending_count
                FROM event_rsvps
                WHERE event_id = ? AND status = 'attending'
            ");
            $stmt->execute([$eventId]);
            $attendingCount = $stmt->fetch(PDO::FETCH_ASSOC)['attending_count'];

            if ($attendingCount >= $event['max_attendees']) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Event is full. Please try joining the waitlist.'
                ]);
                exit();
            }
        }

        // Create new RSVP
        $stmt = $pdo->prepare("
            INSERT INTO event_rsvps (event_id, user_id, status, notes, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
        ");
        $stmt->execute([$eventId, $userId, $status, $notes]);
        $message = 'RSVP submitted successfully!';

        // Send admin notification for new RSVP
        try {
            $rsvpData = [
                'name' => $userData['full_name'],
                'email' => $userData['email'],
                'phone' => $userData['phone'] ?? '',
                'status' => $status,
                'party_size' => 1,
                'special_requirements' => $notes
            ];
            sendRSVPNotificationToAdmins($rsvpData, $eventId);
        } catch (Exception $e) {
            error_log("Failed to send RSVP admin notification: " . $e->getMessage());
        }

        // Generate and send QR code email for attending members
        if ($status === 'attending') {
            $qrService = new QRCodeEmailService($pdo);
            $qrEmailSent = $qrService->generateAndSendEventQRCode($eventId, $userId);

            if (!$qrEmailSent) {
                error_log("Failed to send QR code email to member: $userId for event: $eventId");
            }
        }

        // Send basic confirmation email to member (like guests receive)
        sendRSVPConfirmationEmail($userData['email'], $userData['full_name'], $eventData, $status);
    }
    
    // Get updated counts for response (including both member and guest RSVPs)
    $stmt = $pdo->prepare("
        SELECT
            (
                (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = ? AND er.status = 'attending') +
                (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = ? AND erg.status = 'attending')
            ) as attending_count,
            (
                (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = ? AND er.status = 'maybe') +
                (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = ? AND erg.status = 'maybe')
            ) as maybe_count,
            (
                (SELECT COUNT(*) FROM event_rsvps er WHERE er.event_id = ? AND er.status = 'not_attending') +
                (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = ? AND erg.status = 'not_attending')
            ) as not_attending_count
    ");
    $stmt->execute([$eventId, $eventId, $eventId, $eventId, $eventId, $eventId]);
    $counts = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => $message,
        'status' => $status,
        'counts' => $counts,
        'event_id' => $eventId,
        'new_attending_count' => (int)$counts['attending_count']
    ]);
    
} catch (PDOException $e) {
    error_log("RSVP Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error occurred', 'debug' => $e->getMessage()]);
} catch (Exception $e) {
    error_log("RSVP General Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred', 'debug' => $e->getMessage()]);
}

function sendRSVPConfirmationEmail($email, $name, $event, $status) {
    // This is a basic email function - you can enhance it based on your email system
    $subject = "RSVP Confirmation - " . $event['title'];

    $message = "Dear " . $name . ",\n\n";

    if ($status === 'attending') {
        $message .= "Thank you for confirming your attendance at " . $event['title'] . "!\n\n";
    } elseif ($status === 'not_attending') {
        $message .= "Thank you for letting us know you won't be able to attend " . $event['title'] . ".\n\n";
    } elseif ($status === 'waitlist') {
        $message .= "You have been added to the waitlist for " . $event['title'] . ". We will notify you if a spot becomes available.\n\n";
    }

    $message .= "Event Details:\n";
    $message .= "Title: " . $event['title'] . "\n";
    $message .= "Date: " . date('F j, Y', strtotime($event['event_date'])) . "\n";
    $message .= "Time: " . date('g:i A', strtotime($event['event_date'])) . "\n";
    if (!empty($event['location'])) {
        $message .= "Location: " . $event['location'] . "\n";
    }
    $message .= "\n";

    $message .= "Best regards,\n";
    $message .= get_organization_name();

    $headers = "From: " . get_organization_name() . " <noreply@" . $_SERVER['HTTP_HOST'] . ">\r\n";
    $headers .= "Reply-To: noreply@" . $_SERVER['HTTP_HOST'] . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();

    // Send email (you might want to use your existing email system)
    @mail($email, $subject, $message, $headers);
}
?>
