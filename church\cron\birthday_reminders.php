<?php
/**
 * Birthday Reminder Cron Job - Dynamic URL Support
 *
 * This script is designed to be run via any hosting provider's cron job system using wget.
 * It handles sending birthday reminders and notifications to organization members.
 *
 * Dynamic Cron Job Command (replace YOUR_DOMAIN.COM/YOUR_PATH):
 * wget -q -O /dev/null "https://YOUR_DOMAIN.COM/YOUR_PATH/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 *
 * Example for freedomassemblydb.online:
 * wget -q -O /dev/null "https://freedomassemblydb.online/campaign/church/cron/birthday_reminders.php?cron_key=fac_2024_secure_cron_8x9q2p5m"
 *
 * For security:
 * 1. Keep this file in a separate /cron directory
 * 2. Use the cron_key parameter to prevent unauthorized access
 * 3. Set up logging for monitoring
 * 4. Works with any domain/subdirectory structure
 */

// Set script execution time limit to 5 minutes
set_time_limit(300);

// Define your secret key here - DO NOT SHARE THIS KEY
define('CRON_KEY', 'fac_2024_secure_cron_8x9q2p5m');

// Basic security check
if (!isset($_GET['cron_key']) || $_GET['cron_key'] !== CRON_KEY) {
    header('HTTP/1.0 403 Forbidden');
    exit('Access Denied');
}

// Include necessary files
require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/../classes/BirthdayReminder.php';

// Initialize logging
$logFile = __DIR__ . '/../logs/birthday_reminders.log';
$logDir = dirname($logFile);

// Ensure log directory exists
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

// Log start of execution
$log_message = '[' . date('Y-m-d H:i:s') . '] Birthday reminder cron job started via wget.' . PHP_EOL;
file_put_contents($logFile, $log_message, FILE_APPEND);

try {
    // Initialize the birthday reminder system
    $reminder = new BirthdayReminder($pdo);
    
    // Set a detailed log prefix to distinguish between different notification types
    error_log('======== STARTING AUTOMATED EMAIL PROCESS (' . date('Y-m-d H:i:s') . ') ========');
    
    // Send all types of emails: birthdays, reminders, and notifications
    $results = $reminder->sendReminders();
    
    // Log the results
    $successLog = '[' . date('Y-m-d H:i:s') . '] Cron job completed successfully. ';
    $successLog .= 'Sent: ' . ($results['total_sent'] ?? 0) . ' emails. ';
    $successLog .= 'Failed: ' . ($results['total_failed'] ?? 0) . ' emails. ';
    
    // Add detailed breakdown by type
    if (!empty($results['sent'])) {
        $emailTypes = [];
        foreach ($results['sent'] as $sentEmail) {
            $type = $sentEmail['type'] ?? 'unknown';
            $emailTypes[$type] = ($emailTypes[$type] ?? 0) + 1;
        }
        $successLog .= 'Breakdown by type: ' . json_encode($emailTypes) . '.';
    }
    
    $successLog .= PHP_EOL;
    file_put_contents($logFile, $successLog, FILE_APPEND);
    
    // Return success response
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'success',
        'message' => 'All automated emails sent successfully',
        'timestamp' => date('Y-m-d H:i:s'),
        'results' => $results
    ]);
    
    error_log('======== COMPLETED AUTOMATED EMAIL PROCESS (' . date('Y-m-d H:i:s') . ') ========');
    
} catch (Exception $e) {
    // Log error
    $errorLog = '[' . date('Y-m-d H:i:s') . '] Error: ' . $e->getMessage() . PHP_EOL;
    file_put_contents($logFile, $errorLog, FILE_APPEND);
    
    // Return error response
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Error sending automated emails: ' . $e->getMessage(),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    error_log('======== ERROR IN AUTOMATED EMAIL PROCESS (' . date('Y-m-d H:i:s') . ') ========');
} 