<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit();
}

// Include the configuration file
require_once '../../config.php';

// Set content type to JSON
header('Content-Type: application/json');

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    $offset = isset($input['offset']) ? (int)$input['offset'] : 0;
    $limit = isset($input['limit']) ? (int)$input['limit'] : 10;
    
    // Validate input
    if ($offset < 0 || $limit < 1 || $limit > 50) {
        throw new Exception('Invalid parameters');
    }
    
    // Get recent user activity with pagination
    $stmt = $pdo->prepare("
        SELECT 
            a.username,
            ur.role_display_name,
            dal.dashboard_accessed,
            dal.access_time,
            dal.actions_performed
        FROM dashboard_access_log dal
        JOIN admins a ON dal.user_id = a.id
        LEFT JOIN user_role_assignments ura ON a.id = ura.user_id AND ura.is_active = 1
        LEFT JOIN user_roles ur ON ura.role_id = ur.id
        ORDER BY dal.access_time DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $activities = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format the activities for display
    $formatted_activities = [];
    foreach ($activities as $activity) {
        $formatted_activities[] = [
            'username' => htmlspecialchars($activity['username']),
            'role_display_name' => $activity['role_display_name'] ? htmlspecialchars($activity['role_display_name']) : null,
            'dashboard_accessed' => str_replace('_', ' ', $activity['dashboard_accessed']),
            'access_time' => date('M j, g:i A', strtotime($activity['access_time'])),
            'actions_performed' => (int)$activity['actions_performed']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'activities' => $formatted_activities,
        'total_loaded' => count($formatted_activities)
    ]);
    
} catch (Exception $e) {
    error_log("Error in load_more_activity.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to load more activity'
    ]);
}
?>
