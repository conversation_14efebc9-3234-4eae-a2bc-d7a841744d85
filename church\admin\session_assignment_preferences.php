<?php
/**
 * Session Assignment Notification Preferences
 * Allows admin users to configure their notification preferences for session assignments
 */

require_once '../config.php';
require_once 'includes/functions.php';

// Check if user is logged in and has appropriate permissions
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$message = '';
$error = '';

// Create notification preferences table if it doesn't exist
try {
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS admin_notification_preferences (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            admin_id INT(11) NOT NULL,
            notification_type VARCHAR(50) NOT NULL,
            email_enabled TINYINT(1) DEFAULT 1,
            web_enabled TINYINT(1) DEFAULT 1,
            sms_enabled TINYINT(1) DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_admin_type (admin_id, notification_type),
            FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    ");
} catch (PDOException $e) {
    error_log("Error creating admin_notification_preferences table: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $preferences = $_POST['preferences'] ?? [];
        
        foreach ($preferences as $type => $settings) {
            $email_enabled = isset($settings['email']) ? 1 : 0;
            $web_enabled = isset($settings['web']) ? 1 : 0;
            $sms_enabled = isset($settings['sms']) ? 1 : 0;
            
            $stmt = $pdo->prepare("
                INSERT INTO admin_notification_preferences 
                (admin_id, notification_type, email_enabled, web_enabled, sms_enabled)
                VALUES (?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                email_enabled = VALUES(email_enabled),
                web_enabled = VALUES(web_enabled),
                sms_enabled = VALUES(sms_enabled),
                updated_at = NOW()
            ");
            
            $stmt->execute([$admin_id, $type, $email_enabled, $web_enabled, $sms_enabled]);
        }
        
        $message = "Notification preferences updated successfully!";
        
    } catch (Exception $e) {
        $error = "Error updating preferences: " . $e->getMessage();
    }
}

// Get current preferences
$stmt = $pdo->prepare("
    SELECT notification_type, email_enabled, web_enabled, sms_enabled
    FROM admin_notification_preferences
    WHERE admin_id = ?
");
$stmt->execute([$admin_id]);
$current_preferences = [];
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $current_preferences[$row['notification_type']] = $row;
}

// Define available notification types
$notification_types = [
    'session_location_assignment' => [
        'title' => 'Session Location Assignment',
        'description' => 'When you are assigned to manage a session location'
    ],
    'device_assignment' => [
        'title' => 'Device Assignment',
        'description' => 'When a device is assigned to you for a session'
    ],
    'session_updates' => [
        'title' => 'Session Updates',
        'description' => 'When session details are changed or updated'
    ],
    'session_reminders' => [
        'title' => 'Session Reminders',
        'description' => 'Reminders before your assigned sessions'
    ]
];

$page_title = 'Session Assignment Preferences';
$page_header = 'Session Assignment Notification Preferences';
$page_description = 'Configure how you receive notifications for session assignments';

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            
            <?php if ($message): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($message); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo htmlspecialchars($error); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-bell"></i> Notification Preferences
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-8">
                                <p class="text-muted mb-4">
                                    Choose how you want to receive notifications for different types of session assignments.
                                </p>
                                
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Notification Type</th>
                                                <th class="text-center">Email</th>
                                                <th class="text-center">Web</th>
                                                <th class="text-center">SMS</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($notification_types as $type => $config): ?>
                                                <?php
                                                $prefs = $current_preferences[$type] ?? [
                                                    'email_enabled' => 1,
                                                    'web_enabled' => 1,
                                                    'sms_enabled' => 0
                                                ];
                                                ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($config['title']); ?></strong>
                                                        <br>
                                                        <small class="text-muted"><?php echo htmlspecialchars($config['description']); ?></small>
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="preferences[<?php echo $type; ?>][email]" 
                                                                   id="email_<?php echo $type; ?>"
                                                                   <?php echo $prefs['email_enabled'] ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="email_<?php echo $type; ?>">
                                                                <i class="bi bi-envelope"></i>
                                                            </label>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="preferences[<?php echo $type; ?>][web]" 
                                                                   id="web_<?php echo $type; ?>"
                                                                   <?php echo $prefs['web_enabled'] ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="web_<?php echo $type; ?>">
                                                                <i class="bi bi-bell"></i>
                                                            </label>
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" 
                                                                   name="preferences[<?php echo $type; ?>][sms]" 
                                                                   id="sms_<?php echo $type; ?>"
                                                                   <?php echo $prefs['sms_enabled'] ? 'checked' : ''; ?>>
                                                            <label class="form-check-label" for="sms_<?php echo $type; ?>">
                                                                <i class="bi bi-phone"></i>
                                                            </label>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="mt-4">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-lg"></i> Save Preferences
                                    </button>
                                    <a href="dashboard.php" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                                    </a>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="bi bi-info-circle"></i> Notification Types
                                        </h6>
                                        <ul class="list-unstyled small">
                                            <li><strong>Email:</strong> Notifications sent to your email address</li>
                                            <li><strong>Web:</strong> In-app notifications visible in the admin panel</li>
                                            <li><strong>SMS:</strong> Text messages sent to your phone (if configured)</li>
                                        </ul>
                                        
                                        <hr>
                                        
                                        <h6 class="card-title">
                                            <i class="bi bi-gear"></i> SMS Setup
                                        </h6>
                                        <p class="small text-muted">
                                            To receive SMS notifications, ensure your phone number is configured in your profile and SMS is enabled in system settings.
                                        </p>
                                        
                                        <a href="profile.php" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-person"></i> Update Profile
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
